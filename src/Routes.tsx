import { ReactElement } from 'react';
import { BusinessRouter } from './app/business/BusinessRouter';
import { ClientRouter } from './app/client/ClientRouter';
import { CommonRouter } from './app/common/CommonRouter';

export function Routes(): ReactElement {
    return (
        <CommonRouter>
            {({ Screen }): ReactElement[] => [
                <Screen key={'BusinessApplication'} name={'BusinessApplication'} component={BusinessRouter} />,
                <Screen key={'ClientApplication'} name={'ClientApplication'} component={ClientRouter} />,
            ]}
        </CommonRouter>
    );
}
