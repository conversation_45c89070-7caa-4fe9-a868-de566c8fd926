import { <PERSON><PERSON><PERSON>r } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ReactElement, ReactNode } from 'react';
import { ClientDetailsAppointmentsScreen } from '~/app/business/screens/ClientDetailsAppointmentsScreen';
import { ClientDetailsScreen } from '~/app/business/screens/ClientDetailsScreen';
import { ImportClientScreen } from '~/app/business/screens/ImportClientScreen';
import { SendNotificationComposeScreen } from '~/app/business/screens/SendNotification/SendNotificationComposeScreen';
import { SendNotificationConfirmationScreen } from '~/app/business/screens/SendNotification/SendNotificationConfirmationScreen';
import { SendNotificationSelectClientsScreen } from '~/app/business/screens/SendNotification/SendNotificationSelectClientsScreen';
import { SettingsStack } from '~/app/business/screens/SettingsStack';
import { BookNowSessionsScreen } from '~/app/client/screens/BookNowSessionsScreen';
import { SearchBusinessesCategoryScreen } from '~/app/client/screens/SearchBusinessesCategoryScreen';
import { SearchMapScreen } from '~/app/client/screens/SearchMapScreen';
import { AppointmentChoosePaymentScreen } from '~/app/common/screens/AppointmentChoosePaymentScreen';
import { BusinessLocationScreen } from '~/app/common/screens/BusinessLocationScreen';
import { UnpaidBillScreen } from '~/app/common/screens/UnpaidBillScreen';
import { WaitingListScreen } from '~/app/common/screens/WaitingListScreen';
import { InitializeNotificationsHandler } from '~/components/InitializeNotificationsHandler';
import { InitializeTrackUserConsent } from '~/components/InitializeTrackUserConsent';
import { NotificationsContainer } from '~/components/NotificationsContainer';
import { useAuthChange } from '~/hooks/useAuthChange';
import { topLevelNavigatorRef } from '~/lib/utils/navigation';
import { BookNowScreen } from '../client/screens/BookNowScreen';
import { EditAppointmentScreen } from '../client/screens/EditAppointmentScreen';
import { AccountVerificationScreen } from './screens/AccountVerificationScreen';
import { AppointmentConfirmationScreen } from './screens/AppointmentConfirmationScreen';
import { AppointmentDetailsScreen } from './screens/AppointmentDetailsScreen';
import { BusinessProfileScreen } from './screens/BusinessProfileScreen';
import { BusinessReviewsDetailsScreen } from './screens/BusinessReviewsDetailsScreen';
import { BusinessStaffScreen } from './screens/BusinessStaffScreen';
import { ChangePasswordScreen } from './screens/ChangePasswordScreen';
import { ChooseAccountTypeScreen } from './screens/ChooseAccountTypeScreen';
import { ChooseSubscriptionPlanScreen } from './screens/ChooseSubscriptionPlanScreen';
import { CreateAppointmentScreen } from './screens/CreateAppointmentScreen';
import { CreateBreakScreen } from './screens/CreateBreakScreen';
import { EditProfileScreen } from './screens/EditProfileScreen';
import { FeedbackScreen } from './screens/FeedbackScreen';
import { IntroScreen } from './screens/IntroScreen';
import { JoinBusinessScreen } from './screens/JoinBusinessScreen';
import { MainScreen } from './screens/MainScreen';
import { NotificationPreferenceScreen } from './screens/NotificationPreferenceScreen';
import { NotificationsScreen } from './screens/NotificationsScreen';
import { ProfilePicturePickerScreen } from './screens/ProfilePicturePickerScreen';
import { ReviewScreen } from './screens/ReviewScreen';
import { SelectEventTypeScreen } from './screens/SelectEventTypeScreen';
import { SignInScreen } from './screens/SignInScreen';
import { SignUpBusinessCategoriesScreen } from './screens/SignUpBusinessCategoriesScreen';
import { SignUpBusinessDetailsScreen } from './screens/SignUpBusinessDetailsScreen';
import { SignUpBusinessLocationScreen } from './screens/SignUpBusinessLocationScreen';
import { SignUpBusinessProgramScreen } from './screens/SignUpBusinessProgramScreen';
import { SignUpScreen } from './screens/SignUpScreen';
import { SubmitMissingUserDetailsScreen } from './screens/SubmitMissingUserDetailsScreen';
import { SubscriptionPlansScreen } from './screens/SubscriptionPlansScreen';

const Stack = createNativeStackNavigator();

interface ComponentRouterProps {
    children: (stack: typeof Stack) => ReactNode[];
}

export function CommonRouter({ children }: ComponentRouterProps): ReactElement {
    useAuthChange();

    return (
        <>
            <NavigationContainer ref={topLevelNavigatorRef}>
                <InitializeNotificationsHandler />
                <InitializeTrackUserConsent />
                <Stack.Navigator initialRouteName={'MainScreen'} screenOptions={{ headerShown: false }}>
                    <Stack.Screen name={'MainScreen'} component={MainScreen} />
                    <Stack.Screen name={'IntroScreen'} component={IntroScreen} />
                    <Stack.Screen name={'SignInScreen'} component={SignInScreen} />
                    <Stack.Screen name={'SignUpScreen'} component={SignUpScreen} />
                    <Stack.Screen name={'AccountVerificationScreen'} component={AccountVerificationScreen} />
                    <Stack.Screen name={'ChooseAccountTypeScreen'} component={ChooseAccountTypeScreen} />
                    <Stack.Screen name={'JoinBusinessScreen'} component={JoinBusinessScreen} />
                    <Stack.Screen name={'NotificationPreferenceScreen'} component={NotificationPreferenceScreen} />
                    <Stack.Screen name={'ProfilePicturePickerScreen'} component={ProfilePicturePickerScreen} />
                    <Stack.Screen name={'SignUpBusinessDetailsScreen'} component={SignUpBusinessDetailsScreen} />
                    <Stack.Screen name={'SignUpBusinessCategoriesScreen'} component={SignUpBusinessCategoriesScreen} />
                    <Stack.Screen name={'SignUpBusinessLocationScreen'} component={SignUpBusinessLocationScreen} />
                    <Stack.Screen name={'SignUpBusinessProgramScreen'} component={SignUpBusinessProgramScreen} />
                    <Stack.Screen name={'ChooseSubscriptionPlanScreen'} component={ChooseSubscriptionPlanScreen} />
                    <Stack.Screen name={'BookNowScreen'} component={BookNowScreen} />
                    <Stack.Screen name={'EditAppointmentScreen'} component={EditAppointmentScreen} />
                    <Stack.Screen name={'BookNowSessionsScreen'} component={BookNowSessionsScreen} />
                    <Stack.Screen name={'SearchBusinessesCategoryScreen'} component={SearchBusinessesCategoryScreen} />
                    <Stack.Screen name={'SearchMapScreen'} component={SearchMapScreen} />
                    <Stack.Screen name={'BusinessProfileScreen'} component={BusinessProfileScreen} />
                    <Stack.Screen name={'NotificationsScreen'} component={NotificationsScreen} />
                    <Stack.Screen name={'SelectEventTypeScreen'} component={SelectEventTypeScreen} />
                    <Stack.Screen name={'CreateAppointmentScreen'} component={CreateAppointmentScreen} />
                    <Stack.Screen name={'CreateBreakScreen'} component={CreateBreakScreen} />
                    <Stack.Screen name={'ClientDetailsScreen'} component={ClientDetailsScreen} />
                    <Stack.Screen name={'BusinessLocationScreen'} component={BusinessLocationScreen} />
                    <Stack.Screen name={'ReviewScreen'} component={ReviewScreen} />
                    <Stack.Screen name={'EditProfileScreen'} component={EditProfileScreen} />
                    <Stack.Screen name={'FeedbackScreen'} component={FeedbackScreen} />
                    <Stack.Screen name={'ChangePasswordScreen'} component={ChangePasswordScreen} />
                    <Stack.Screen name={'BusinessStaffScreen'} component={BusinessStaffScreen} />
                    <Stack.Screen name={'BusinessReviewsDetailsScreen'} component={BusinessReviewsDetailsScreen} />
                    <Stack.Screen name={'WaitingListScreen'} component={WaitingListScreen} />
                    <Stack.Screen name={'UnpaidBillScreen'} component={UnpaidBillScreen} />
                    <Stack.Screen
                        name={'SendNotificationSelectClientsScreen'}
                        component={SendNotificationSelectClientsScreen}
                    />
                    <Stack.Screen name={'SendNotificationComposeScreen'} component={SendNotificationComposeScreen} />
                    <Stack.Screen
                        name={'SendNotificationConfirmationScreen'}
                        component={SendNotificationConfirmationScreen}
                    />

                    <Stack.Screen name={'BusinessSettingsStack'} component={SettingsStack} />

                    <Stack.Screen
                        name={'SubscriptionPlansScreen'}
                        component={SubscriptionPlansScreen}
                        options={{
                            gestureEnabled: false,
                        }}
                    />
                    <Stack.Screen
                        name={'AppointmentDetailsScreen'}
                        component={AppointmentDetailsScreen}
                        initialParams={{ isStaffView: false }}
                    />

                    <Stack.Group
                        screenOptions={{
                            presentation: 'modal',
                        }}
                    >
                        <Stack.Screen
                            name={'ClientDetailsAppointmentsScreen'}
                            component={ClientDetailsAppointmentsScreen}
                        />
                        <Stack.Screen
                            name={'SubmitMissingUserDetailsScreen'}
                            component={SubmitMissingUserDetailsScreen}
                        />

                        <Stack.Screen name={'ImportClientScreen'} component={ImportClientScreen} />
                        <Stack.Screen
                            name={'AppointmentConfirmationScreen'}
                            component={AppointmentConfirmationScreen}
                        />
                    </Stack.Group>
                    <Stack.Screen name={'AppointmentChoosePaymentScreen'} component={AppointmentChoosePaymentScreen} />

                    {children(Stack)}
                </Stack.Navigator>
            </NavigationContainer>
            <NotificationsContainer />
        </>
    );
}
