import React, { ReactElement } from 'react';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { VStack, VStackProps } from '~/components/ui/Grid';

export function Loader({ children, ...rest }: VStackProps): ReactElement {
    return (
        <VStack justifyContent={'center'} alignItems={'center'} {...rest}>
            {children ?? <CircularProgress />}
        </VStack>
    );
}
