import { useFocusEffect } from '@react-navigation/native';
import { useFormikContext } from 'formik';
import { get } from 'lodash';
import { ReactElement, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { Loader } from '~/app/common/components/Loader';
import { Avatar } from '~/components/ui/Avatar';
import { Button } from '~/components/ui/Button';
import { MenuItem } from '~/components/ui/MenuItem';
import { Paper } from '~/components/ui/Paper';
import { Select, SelectProps } from '~/components/ui/Select';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useSafeState } from '~/hooks/useSafeState';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';

interface Props extends Omit<SelectProps, 'value' | 'children'> {
    name: string;
    staffMemberKey: string;
}

export function FormikSelectStaffMemberService({ name, staffMemberKey, onChange, ...rest }: Props): ReactElement {
    const { t } = useTranslation();
    const formik = useFormikContext();
    const { value } = formik.getFieldMeta<number | null>(name);
    const { value: staffMemberId } = formik.getFieldMeta<string | null>(staffMemberKey);
    const user = useUser();

    const [services, setServices] = useSafeState<ServiceModel[]>([]);
    const [amiSelected, setAmiSelected] = useSafeState(false);

    const currentBusiness = useUserBusiness({
        onSuccess: (business) => {
            const { value } = formik.getFieldMeta<number | null>(name);

            const members = business?.staffMembers;
            const selectedStaffMember = members?.find((member) => member.uid === staffMemberId);
            const services = (selectedStaffMember?.services ?? []).sort((a, b) =>
                a.serviceRank > b.serviceRank ? 1 : b.serviceRank > a.serviceRank ? -1 : 0,
            );
            setServices(services);
            setAmiSelected(!!user?.uid && selectedStaffMember?.uid === user?.uid);

            const initialValue = get(formik.initialValues, name);
            if (!initialValue && !value) {
                formik.setFieldValue(name, services[0]?.id, true);
            }
        },
    });

    const handleChange = useEvent<Exclude<Props['onChange'], undefined>>((item, value, data) => {
        formik.setFieldValue(name, value);
        onChange?.(item, value, data);
    });

    const handleFocus = useEvent(() => {
        // noinspection JSIgnoredPromiseFromCall
        currentBusiness.refetch();
    });

    useEffect(() => {
        handleFocus();
    }, [staffMemberId, handleFocus]);

    useFocusEffect(handleFocus);

    if (currentBusiness.isFetching) {
        return <Loader height={56} alignItems={'center'} />;
    }

    if (!services.length) {
        return (
            <Paper py={2} px={3} mt={1} flexWrap={'nowrap'} bgColor="backgroundPrimary" borderColor={'borderOpaque'}>
                <Typography fontWeight={500}>{t('noServices')}</Typography>
                <Typography variant="caption1" color="textSecondary" fontWeight={500} lineHeight={20}>
                    {t('weCouldNotFindAnyServicesForTheSelectedStaffMember')}
                </Typography>

                {amiSelected && (
                    <Button
                        mt={2}
                        color={'accent'}
                        to={{
                            screen: 'BusinessSettingsStack',
                            params: {
                                screen: 'SettingsServiceScreen',
                                params: {
                                    comingFromCreateAppointment: true,
                                },
                            },
                        }}
                        label={t('createNewService')}
                    />
                )}
            </Paper>
        );
    }

    return (
        <Select value={value} onChange={handleChange} {...rest}>
            {services && services.length > 0 ? (
                services.map((service) => (
                    <MenuItem key={service.id} value={service.id} avatar={<Avatar size={24} bgColor={service.color} />}>
                        {`${service.name} - ${service.duration} mins`}
                    </MenuItem>
                ))
            ) : (
                <MenuItem value={''}>{t('noServicesFound')}</MenuItem>
            )}
        </Select>
    );
}
