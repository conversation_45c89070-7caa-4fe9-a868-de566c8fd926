import { useNavigation } from '@react-navigation/native';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { Button } from '~/components/ui/Button';
import { useNotifications } from '~/hooks/useNotifications';
import { useApplicationStore } from '~/store/useApplicationStore';
import { useAuthStore } from '~/store/useAuthStore';

export function GetStartedButton() {
    const { t } = useTranslation();
    const navigation = useNavigation();
    const setVisitedIntro = useApplicationStore((state) => state.setVisitedIntro);
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const updateUser = useAuthStore((state) => state.updateUser);
    const notifications = useNotifications();

    const updateAccountTypeMutation = useMutation(
        'updateUser',
        (accountType: AccountType) => {
            return updateUser({ accountType });
        },
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
        },
    );

    const handlePress = useCallback(async () => {
        await updateAccountTypeMutation.mutateAsync(AccountType.BusinessOwner);
        await resolveUser();
        setVisitedIntro(true);
        navigation.navigate('MainScreen');
    }, [navigation, resolveUser, setVisitedIntro, updateAccountTypeMutation]);

    return <Button color={'accent'} size={'large'} onPress={handlePress} label={t('startNow')} mb={2} />;
}
