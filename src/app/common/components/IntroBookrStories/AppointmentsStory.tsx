import { useTranslation } from 'react-i18next';
import { GetStartedButton } from '~/app/common/components/IntroBookrStories/GetStartedButton';
import { Typography } from '~/components/ui/Typography';

export function AppointmentsStory() {
    const { t } = useTranslation();

    return (
        <>
            <Typography variant={'title1'} color={'#fff'} fontWeight={700}>
                {t('appointmentsStoryHeadline')}
            </Typography>
            <GetStartedButton />
        </>
    );
}
