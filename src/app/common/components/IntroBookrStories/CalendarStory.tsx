import { useTranslation } from 'react-i18next';
import { Typography } from '~/components/ui/Typography';
import { GetStartedButton } from './GetStartedButton';

export function CalendarStory() {
    const { t } = useTranslation();

    return (
        <>
            <Typography variant={'title1'} color={'#fff'} fontWeight={700}>
                {t('calendarStoryHeadline')}
            </Typography>
            <GetStartedButton />
        </>
    );
}
