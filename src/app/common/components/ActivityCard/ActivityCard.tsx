/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment';
import { transparentize } from 'polished';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useTheme } from 'styled-components';
import { ActivityModel } from '@bookr-technologies/api/models/ActivityModel';
import { ActivityType } from '@bookr-technologies/api/models/ActivityType';
import { Grid } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { useApplicationStore } from '~/store/useApplicationStore';

interface ActivityCardProps {
    activity: ActivityModel;
    onPress: (activity: ActivityModel) => void;
}

export function ActivityCard({ activity, onPress }: ActivityCardProps) {
    const theme = useTheme();
    const { t } = useTranslation();
    const notificationsLastChecked = useApplicationStore((state) => state.notificationsLastChecked);

    const isNewActivity = useMemo(() => {
        if (!notificationsLastChecked) {
            return true;
        }
        const lastChecked = moment(notificationsLastChecked);
        const activityDate = moment(activity.createdAt);
        return activityDate.isAfter(lastChecked);
    }, [notificationsLastChecked, activity]);

    const color = useMemo(() => {
        switch (activity.activityType) {
            case ActivityType.NewAppointment:
                return theme.palette.typography.success;
            case ActivityType.AppointmentCancelled:
                return theme.palette.typography.error;
            case ActivityType.AppointmentRescheduled:
                return theme.palette.typography.warning;
            default:
                return theme.palette.typography.accent;
        }
    }, [activity.activityType, theme]);

    const icon = useMemo(() => {
        switch (activity.activityType) {
            case ActivityType.NewAppointment:
                return <MaterialIcons name={'event-available'} size={20} color={color} />;
            case ActivityType.AppointmentCancelled:
                return <MaterialIcons name={'event-busy'} size={20} color={color} />;
            case ActivityType.AppointmentRescheduled:
                return <MaterialCommunityIcons name={'calendar-edit'} size={20} color={color} />;
            default:
                return <MaterialCommunityIcons name={'rocket-launch'} size={20} color={color} />;
        }
    }, [activity.activityType, color]);

    return (
        <TouchableOpacity onPress={() => onPress(activity)}>
            <Grid flexDirection={'row'} bgColor={'secondary'} borderRadius={12} p={2} mb={1}>
                <Grid
                    width={36}
                    height={36}
                    borderRadius={18}
                    justifyContent={'center'}
                    alignItems={'center'}
                    bgColor={transparentize(0.8, color)}
                    mr={2}
                >
                    {icon}
                    {isNewActivity && (
                        <Grid
                            position={'absolute'}
                            top={1}
                            right={0}
                            width={10}
                            height={10}
                            borderRadius={5}
                            bgColor={theme.palette.typography.error}
                        />
                    )}
                </Grid>
                <Grid flex>
                    <Typography
                        variant={'subhead'}
                        fontWeight={500}
                        fontSize={15}
                        lineHeight={22.5}
                        color={isNewActivity ? 'primary' : 'textSecondary'}
                    >
                        {t(`activities.${activity.activityType}.title` as any)}!
                    </Typography>
                    <Typography
                        variant={'footnote'}
                        fontWeight={500}
                        fontSize={13}
                        lineHeight={19.5}
                        color={theme.palette.typography.disabled}
                    >
                        {t(`activities.${activity.activityType}.message` as any, {
                            appointment: activity.appointment,
                            date: moment(activity.appointment.dateTime).format('DD MMM YYYY'),
                            displayName: activity.client?.displayName,
                            service: activity.appointment.service.name,
                            time: moment(activity.appointment.dateTime).format('HH:mm'),
                        })}
                    </Typography>
                </Grid>
            </Grid>
        </TouchableOpacity>
    );
}
