import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import React, { ReactNode, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    LayoutAnimation,
    Linking,
    NativeSyntheticEvent,
    Pressable,
    TextLayoutEventData,
    TouchableOpacity,
} from 'react-native';
import { useTheme } from 'styled-components';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Avatar } from '~/components/ui/Avatar';
import { Box } from '~/components/ui/Box';
import { Button } from '~/components/ui/Button';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useAnimStyle } from '~/hooks/useAnimStyle';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useEvent } from '~/hooks/useEvent';
import { usePressable } from '~/hooks/usePressable';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { useAuthStore } from '~/store/useAuthStore';

const useStyles = makeStyles(({ theme }) => ({
    readMore: {
        textDecorationColor: theme.palette.primary.main,
        textDecorationLine: 'underline',
    },
    staffHeader: {
        paddingVertical: theme.mixins.spacingValue(2),
        paddingHorizontal: theme.mixins.spacingValue(3),
        marginHorizontal: theme.mixins.spacingValue(-3),
        flex: 1,
        flexDirection: 'row',
    },
    staffHeaderPressed: {
        backgroundColor: theme.palette.backgroundTertiary.main,
    },
}));

const NUM_LINES = 2;

interface CollapsibleSearchBarProps {
    initialExpanded?: boolean;
    onSearchChange: (query: string) => void;
    placeholder?: string;
}

interface CollapsibleSearchBarRenderProps {
    isExpanded: boolean;
    searchBar: ReactNode | null;
    searchIcon: ReactNode;
    searchQuery: string;
}

function CollapsibleSearchBar({
    onSearchChange,
    placeholder = 'Search services by name...',
    initialExpanded = false,
}: CollapsibleSearchBarProps): CollapsibleSearchBarRenderProps {
    const { t } = useTranslation();
    const theme = useTheme();
    const [searchQuery, setSearchQuery] = useState('');
    const [isSearchExpanded, setIsSearchExpanded] = useState(initialExpanded);

    // Animation values for search icon fade
    const [iconOpacity, iconOpacityAnimation] = useAnimatedValue(initialExpanded ? 0 : 1);

    // Animation styles
    const searchIconStyle = useAnimStyle(() => ({
        opacity: iconOpacity.current,
    }));

    const handleToggleSearch = useEvent(() => {
        LayoutAnimation.configureNext({
            duration: 300,
            create: {
                type: LayoutAnimation.Types.easeInEaseOut,
                property: LayoutAnimation.Properties.opacity,
            },
            update: {
                type: LayoutAnimation.Types.easeInEaseOut,
            },
            delete: {
                type: LayoutAnimation.Types.easeInEaseOut,
                property: LayoutAnimation.Properties.opacity,
            },
        });

        if (isSearchExpanded) {
            // Collapse search bar
            setIsSearchExpanded(false);
            setSearchQuery(''); // Clear search when collapsing
            onSearchChange(''); // Notify parent of search clear
            iconOpacityAnimation.timing(1, { duration: 300 }).start();
        } else {
            // Expand search bar
            setIsSearchExpanded(true);
            iconOpacityAnimation.timing(0, { duration: 200 }).start();
        }
    });

    const handleSearchChange = useEvent((text: string) => {
        setSearchQuery(text);
        onSearchChange(text);
    });

    const handleClearSearch = useEvent(() => {
        setSearchQuery('');
        onSearchChange('');
    });

    const searchIcon = (
        <Animated.View style={searchIconStyle}>
            <IconButton onPress={handleToggleSearch} size={'medium'}>
                <MaterialIcons name={'search'} size={24} color={theme.palette.contentSecondary.main} />
            </IconButton>
        </Animated.View>
    );

    const searchBar = isSearchExpanded ? (
        <Grid mx={3} mb={3}>
            <TextField
                // eslint-disable-next-line jsx-a11y/no-autofocus
                autoFocus
                value={searchQuery}
                onChangeText={handleSearchChange}
                placeholder={t('searchServicesByName', placeholder)}
                startAdornment={
                    <TextFieldAdornment variant={'start'}>
                        <MaterialIcons name={'search'} size={20} color={theme.palette.contentSecondary.main} />
                    </TextFieldAdornment>
                }
                endAdornment={
                    <TextFieldAdornment variant={'end'} maxWidth={120} pr={1}>
                        <HStack>
                            {searchQuery.trim() && (
                                <IconButton onPress={handleClearSearch} size={'small'}>
                                    <MaterialIcons
                                        name={'close'}
                                        size={18}
                                        color={theme.palette.contentSecondary.main}
                                    />
                                </IconButton>
                            )}
                            <IconButton onPress={handleToggleSearch} size={'small'}>
                                <MaterialIcons
                                    name={'keyboard-arrow-up'}
                                    size={18}
                                    color={theme.palette.contentSecondary.main}
                                />
                            </IconButton>
                        </HStack>
                    </TextFieldAdornment>
                }
            />
        </Grid>
    ) : null;

    return {
        searchIcon,
        searchBar,
        isExpanded: isSearchExpanded,
        searchQuery,
    };
}

export function Services({ business }: { business: BusinessModel }): JSX.Element {
    const { t } = useTranslation();
    const [searchQuery, setSearchQuery] = useState('');

    const { searchIcon, searchBar, isExpanded } = CollapsibleSearchBar({
        onSearchChange: setSearchQuery,
        placeholder: t('searchServiceByName'),
        initialExpanded: false,
    });

    // businesses that don't have any services will display a phone call button
    const services = business.staffMembers
        .map((staffMember) => staffMember.services)
        .flat()
        .filter(({ hiddenFromClients }) => !hiddenFromClients);

    // Filter staff members based on search query
    const filteredStaffMembers = useMemo(() => {
        if (!searchQuery.trim()) {
            return business.staffMembers
                .filter((staffMember) => !(staffMember as any).hidden)
                .sort((a, b) => a.staffRank - b.staffRank);
        }

        const query = searchQuery.toLowerCase().trim();
        return business.staffMembers
            .filter((staffMember) => !(staffMember as any).hidden)
            .map((staffMember) => ({
                ...staffMember,
                services: staffMember.services.filter(
                    (service) =>
                        !service.hiddenFromClients &&
                        (service.name.toLowerCase().includes(query) ||
                            service.description.toLowerCase().includes(query)),
                ),
            }))
            .filter((staffMember) => staffMember.services.length > 0)
            .sort((a, b) => a.staffRank - b.staffRank);
    }, [business.staffMembers, searchQuery]);

    const handleBookPhoneCall = () => {
        logAnalyticsEvent(AnalyticsEvent.PhoneCallAppointment, {
            businessId: business.id,
            businessName: business.name,
            phoneNumber: business.phoneNumber,
        });
        Linking.openURL(`tel:${business.phoneNumber}`);
    };

    return (
        <Grid mt={services.length > 0 ? 4 : 1.5}>
            {services.length > 0 ? (
                <>
                    {/* Title and Search Toggle */}
                    <HStack justifyContent={'space-between'} alignItems={'center'} mx={3} mb={isExpanded ? 0 : 2}>
                        <Typography variant={'title3'} fontWeight={700} fontSize={20} lineHeight={25.2}>
                            {t('services')}
                        </Typography>

                        {/* Search Icon Button - visible when search is collapsed */}
                        {searchIcon}
                    </HStack>

                    {/* Collapsible Search Bar */}
                    {searchBar}

                    <Paper borderRadius={0} overflow={'hidden'}>
                        {filteredStaffMembers.map((staffMember) => (
                            <StaffMemberWithServices
                                key={staffMember.uid}
                                business={business}
                                staff={staffMember}
                                searchQuery={searchQuery}
                            />
                        ))}
                        {filteredStaffMembers.length === 0 && searchQuery.trim() && (
                            <Grid p={3} alignItems={'center'}>
                                <Typography variant={'body'} color={'typography.textSecondary'} textAlign={'center'}>
                                    {t('noServicesFound', 'No services found matching your search.')}
                                </Typography>
                            </Grid>
                        )}
                    </Paper>
                </>
            ) : (
                <Grid bgColor={'backgroundPrimary'} mb={1.5} p={2} mx={3} borderRadius={12}>
                    <Typography variant={'body'} fontWeight={700} mb={1}>
                        {t('phoneCallAppointment')}
                    </Typography>
                    <Typography variant={'footnote'} color={'typography.textSecondary'} fontWeight={500}>
                        {t('phoneCallAppointmentDescription')}
                    </Typography>
                    <Button
                        startIcon={<MaterialIcons name={'phone-in-talk'} size={20} color={'#fff'} />}
                        mt={2}
                        size={'small'}
                        color={'accent'}
                        label={t('bookPhoneCall')}
                        onPress={handleBookPhoneCall}
                    />
                </Grid>
            )}
        </Grid>
    );
}

const StaffMemberWithServices = ({
    business,
    staff,
    searchQuery,
}: {
    business: BusinessModel;
    searchQuery?: string;
    staff: UserModel;
}) => {
    const { t } = useTranslation();
    const [showMore, setShowMore] = useState(false);
    const theme = useTheme();
    const styles = useStyles();
    const { navigate } = useNavigation();
    const [isPressed, pressableProps] = usePressable();

    const handleShowMoreLess = (): void => {
        LayoutAnimation.easeInEaseOut();
        setShowMore(!showMore);
    };

    const handleStaffMemberPress = useEvent(() => {
        navigate('BusinessStaffScreen', { staff });
    });

    // Use filtered services if search query exists, otherwise show all services
    const services = useMemo(() => {
        const allServices = staff.services.filter(({ hiddenFromClients }) => !hiddenFromClients);

        if (!searchQuery?.trim()) {
            return allServices.sort(ServiceModel.sort);
        }

        const query = searchQuery.toLowerCase().trim();
        return allServices
            .filter(
                (service) =>
                    service.name.toLowerCase().includes(query) || service.description.toLowerCase().includes(query),
            )
            .sort(ServiceModel.sort);
    }, [staff.services, searchQuery]);

    return (
        <Grid bgColor={'backgroundPrimary'} mb={1} pb={services.length === 0 ? 3 : 1}>
            <Paper direction={'row'} fullWidth bgColor={'secondary'} px={3} pb={2} overflow={'hidden'} borderRadius={0}>
                <Pressable
                    onPress={handleStaffMemberPress}
                    style={sx(styles.staffHeader, isPressed && styles.staffHeaderPressed)}
                    {...pressableProps}
                >
                    <Avatar source={staff.photoURL} name={staff.displayName} size={48} />
                    <VStack ml={2} flex={1} flexGrow>
                        <Typography variant={'body'} fontWeight={500} fontSize={17} lineHeight={25.2}>
                            {staff.displayName}
                        </Typography>
                        <Typography
                            variant={'caption1'}
                            color={'disabled'}
                            fontWeight={500}
                            fontSize={12}
                            lineHeight={15}
                        >
                            {t('staffMember')}
                        </Typography>
                        {!staff.userSettings?.acceptsNewClients && (
                            <Typography
                                variant={'caption1'}
                                color={'disabled'}
                                fontWeight={500}
                                fontSize={12}
                                lineHeight={15}
                            >
                                {t('currentlyNotAcceptingNewClients')}
                            </Typography>
                        )}
                    </VStack>
                </Pressable>
            </Paper>

            <Grid flexDirection={'column'} px={3}>
                {(!showMore ? services.slice(0, 2) : services)
                    .filter(({ hiddenFromClients }) => !hiddenFromClients)
                    .sort(ServiceModel.sort)
                    .map((service) => (
                        <Grid mb={3} key={service.id}>
                            <Service business={business} service={service} staff={staff} />
                        </Grid>
                    ))}
            </Grid>
            {services.length > 2 && (
                <TouchableOpacity activeOpacity={0.7} onPress={handleShowMoreLess}>
                    <VStack alignItems={'center'} justifyContent={'center'}>
                        <Typography
                            textAlign={'center'}
                            variant={'footnote'}
                            color={'disabled'}
                            fontWeight={500}
                            fontSize={13}
                            lineHeight={19}
                        >
                            {!showMore ? t('showMore') : t('showLess')}
                        </Typography>
                        <MaterialIcons
                            name={!showMore ? 'keyboard-arrow-down' : 'keyboard-arrow-up'}
                            size={24}
                            color={theme.palette.accent.main}
                        />
                    </VStack>
                </TouchableOpacity>
            )}
            {services.length === 0 && (
                <Typography
                    pl={3}
                    variant={'caption1'}
                    color={'disabled'}
                    fontWeight={400}
                    fontSize={12}
                    lineHeight={15}
                >
                    {t('noServices')}
                </Typography>
            )}
        </Grid>
    );
};

export const Service = ({
    business,
    service,
    staff,
}: {
    business: BusinessModel;
    service: ServiceModel;
    staff: UserModel;
}) => {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const isGuest = useAuthStore((state) => state.isGuest);
    const styles = useStyles();
    const [showMore, setShowMore] = useState(false);
    const [numOfLines, setNumOfLines] = useState(0);

    const onTextLayout = useCallback(
        (e: NativeSyntheticEvent<TextLayoutEventData>) => numOfLines === 0 && setNumOfLines(e.nativeEvent.lines.length),
        [numOfLines],
    );

    const handleShowMoreLess = () => {
        LayoutAnimation.easeInEaseOut();
        setShowMore(!showMore);
    };

    const handleBookNow = useEvent(() => {
        if (isGuest) {
            navigate('SignInScreen', {
                showGuestOption: false,
            });
            return;
        }
        if (service.numberOfSessions > 0) {
            navigate('BookNowSessionsScreen', { business, service, staff });
        } else {
            navigate('BookNowScreen', { business, service, staff });
        }
    });

    return (
        <Grid>
            <HStack justifyContent={'space-between'}>
                <VStack maxWidth={'60%'}>
                    <Typography variant={'subhead'} fontWeight={700} fontSize={15} lineHeight={22.5}>
                        {service.name}
                    </Typography>
                    {service.acceptsOnlinePayments ? (
                        <Typography
                            color={'disabled'}
                            variant={'caption1'}
                            fontWeight={500}
                            fontSize={12}
                            lineHeight={15.5}
                        >
                            {t('acceptsOnlinePayment')}
                        </Typography>
                    ) : null}
                    {service.onlineEvent && (
                        <Typography
                            color={'disabled'}
                            variant={'caption1'}
                            fontWeight={500}
                            fontSize={12}
                            lineHeight={15.5}
                        >
                            {t('serviceCreatesVideoCall')}
                        </Typography>
                    )}
                    <HStack alignItems={'center'}>
                        <Typography
                            color={'disabled'}
                            variant={'caption1'}
                            fontWeight={500}
                            fontSize={12}
                            lineHeight={15.5}
                        >
                            {service.price} {service.currency}
                        </Typography>
                        <Box width={3} height={3} borderRadius={3} bgColor={'#afafaf'} mx={0.8} />
                        <Typography
                            color={'disabled'}
                            variant={'caption1'}
                            fontWeight={500}
                            fontSize={12}
                            lineHeight={15.5}
                        >
                            {service.duration} mins
                        </Typography>
                        {service.numberOfSessions > 0 ? (
                            <>
                                <Box width={3} height={3} borderRadius={3} bgColor={'#afafaf'} mx={0.8} />
                                <Typography
                                    color={'disabled'}
                                    variant={'caption1'}
                                    fontWeight={500}
                                    fontSize={12}
                                    lineHeight={15.5}
                                >
                                    {t('subscription')}
                                </Typography>
                            </>
                        ) : null}
                    </HStack>
                </VStack>
                <Button size={'small'} color={'accent'} label={'Book now'} onPress={handleBookNow} />
            </HStack>
            {service.description?.trim() ? (
                <Grid flexDirection={'column'} my={1}>
                    <Typography
                        color={'textSecondary'}
                        variant={'caption1'}
                        fontWeight={500}
                        lineHeight={18}
                        onTextLayout={onTextLayout}
                        numberOfLines={numOfLines === 0 ? undefined : showMore ? numOfLines : NUM_LINES}
                    >
                        {service.description.trim()}
                    </Typography>
                </Grid>
            ) : null}
            {numOfLines > NUM_LINES && (
                <TouchableOpacity activeOpacity={0.5} onPress={handleShowMoreLess}>
                    <Typography
                        variant={'caption1'}
                        fontWeight={500}
                        style={styles.readMore}
                        textTransform={'lowercase'}
                    >
                        {showMore ? t('showLess') : t('showMore')}
                    </Typography>
                </TouchableOpacity>
            )}
        </Grid>
    );
};
