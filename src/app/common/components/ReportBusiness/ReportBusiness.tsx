/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Formik } from 'formik';
import { ReactElement, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useMutation } from 'react-query';
import * as Yup from 'yup';
import { reportContentEndpoint } from '@bookr-technologies/api/endpoints/reportContentEndpoint';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { BottomSheet } from '~/components/BottomSheet';
import { FormikButton } from '~/components/ui/Button';
import { Grid, HStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { FormikRadio } from '~/components/ui/Radio';
import { Typography } from '~/components/ui/Typography';
import { useNotifications } from '~/hooks/useNotifications';
import { useValidationSchema } from '~/hooks/useValidationSchema';

const REPORT_REASONS = [
    'sexualContent',
    'violentOrRepulsiveContent',
    'hatefulOrAbusiveContent',
    'harmfulDangerousActs',
    'childAbuse',
    'infringesMyRights',
    'promotesTerrorism',
    'spamOrMisleading',
];

export function ReportBusiness({ business }: { business: BusinessModel }): ReactElement {
    const { t } = useTranslation();
    const notifications = useNotifications();
    const reportMutation = useMutation(
        'reportBusiness',
        (reason: string) =>
            reportContentEndpoint.reportContent({
                businessId: business.id,
                reason,
            }),
        {
            onError: () => {
                handleReportContentClose();
                notifications.error(t('reportContentError'));
            },
            onSuccess: () => {
                handleReportContentClose();
                notifications.success(t('reportContentSuccess'));
            },
        },
    );
    const [reportContentOpen, setReportContentOpen] = useState(false);

    const handleReportContent = (): void => setReportContentOpen(true);
    const handleReportContentClose = (): void => setReportContentOpen(false);

    const handleSubmit = useCallback(
        async (values: { reportReason: string }) => {
            reportMutation.mutate(values.reportReason);
        },
        [reportMutation],
    );

    const validation = useValidationSchema({
        reportReason: Yup.string().required(t('requiredField')),
    });

    return (
        <TouchableOpacity onPress={handleReportContent}>
            <HStack
                mt={1}
                px={3}
                py={2}
                bgColor={'backgroundPrimary'}
                justifyContent={'space-between'}
                alignItems={'center'}
            >
                <Typography variant={'callout'} fontSize={16} fontWeight={500} lineHeight={24}>
                    {t('reportBusiness')}
                </Typography>
                <Grid
                    p={0.5}
                    alignItems={'center'}
                    justifyContent={'center'}
                    bgColor={'backgroundSecondary'}
                    borderRadius={8}
                >
                    <MaterialIcons name={'flag'} size={24} color={'#AFAFAF'} />
                </Grid>
            </HStack>
            <BottomSheet snapPoints={['90%']} open={reportContentOpen} onClose={handleReportContentClose}>
                <Formik
                    validateOnMount
                    enableReinitialize
                    initialValues={{
                        reportReason: '',
                    }}
                    onSubmit={handleSubmit}
                    {...validation}
                >
                    <Grid bgColor={'backgroundPrimary'} flex p={3}>
                        <Typography variant={'title1'} fontSize={28} fontWeight={700} lineHeight={35}>
                            {t('reportContent')}
                        </Typography>
                        <List width={'100%'} pt={3}>
                            {REPORT_REASONS.map((reportReason) => (
                                <ListItem key={reportReason} alignItems={'flex-start'} px={0}>
                                    <FormikRadio
                                        style={{ flexGrow: 1, marginHorizontal: -16 }}
                                        name={'reportReason'}
                                        value={reportReason}
                                        label={t(reportReason as any)}
                                    />
                                </ListItem>
                            ))}
                        </List>
                        <Grid flex={1} justifyContent={'flex-end'} pb={2}>
                            <FormikButton
                                size={'large'}
                                label={t('reportBusiness')}
                                loading={reportMutation.isLoading}
                            />
                        </Grid>
                    </Grid>
                </Formik>
            </BottomSheet>
        </TouchableOpacity>
    );
}
