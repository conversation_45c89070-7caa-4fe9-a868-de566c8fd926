import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { ImageGallery } from '@georstat/react-native-image-gallery';
import { ImageObject } from '@georstat/react-native-image-gallery/lib/typescript/types';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { WorkingHoursBottomSheet } from '~/app/common/components/WorkingHoursBottomSheet';
import Button from '~/components/ui/Button';
import { HStack } from '~/components/ui/Grid';
import { useEvent } from '~/hooks/useEvent';

export function StaffProfileActions({ business, staff }: { business: BusinessModel; staff: UserModel }): JSX.Element {
    const { t } = useTranslation();
    const [scheduleOpen, setScheduleOpen] = useState(false);
    const [galleryOpen, setGalleryOpen] = useState(false);

    const handleWorkingHoursPress = useEvent(() => {
        setScheduleOpen(true);
    });

    const handleWorkingHoursClose = useEvent(() => {
        setScheduleOpen(false);
    });

    const handleCallPress = useEvent(() => {
        Linking.openURL(`tel:${staff.phoneNumber}`);
    });

    const handlePortofolioPress = useEvent(() => {
        setGalleryOpen(true);
    });

    const handlePortofolioClose = useEvent(() => {
        setGalleryOpen(false);
    });

    const galleryImages = useMemo<ImageObject[]>(
        // TODO: add staff portofolio photos
        () =>
            [staff.photoURL]
                .filter((u) => !!u)
                .map((photo, index) => {
                    return {
                        id: index,
                        url: photo,
                    };
                }),
        [staff],
    );

    return (
        <HStack my={2} fullWidth px={3} justifyContent={'center'}>
            <Button
                mr={1.25}
                color={'secondary'}
                size={'small'}
                label={t('program')}
                onPress={handleWorkingHoursPress}
                startIcon={<MaterialCommunityIcons name={'calendar-text'} color={'accent'} />}
            />
            <Button
                mr={1.25}
                color={'secondary'}
                size={'small'}
                label={t('gallery')}
                onPress={handlePortofolioPress}
                startIcon={<MaterialCommunityIcons name={'image-multiple'} color={'accent'} />}
            />
            <Button
                mr={1.25}
                color={'secondary'}
                size={'small'}
                label={t('call')}
                onPress={handleCallPress}
                startIcon={<MaterialCommunityIcons name={'phone-in-talk-outline'} color={'accent'} />}
            />
            <WorkingHoursBottomSheet
                hours={staff.workingHours}
                zoneId={business.zoneId}
                open={scheduleOpen}
                onClose={handleWorkingHoursClose}
            />
            {galleryImages.length > 0 && (
                <ImageGallery close={handlePortofolioClose} isOpen={galleryOpen} images={galleryImages} />
            )}
        </HStack>
    );
}
