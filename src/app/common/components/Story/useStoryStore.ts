import { Dimensions } from 'react-native';
import { create } from 'zustand';
import { StoryConfig } from './StoryConfig';

export interface StoryStoreType {
    activeStoryIndex: number;
    defaultOffset: number;
    paused: boolean;
    stories: StoryConfig[];
    tick: number;

    nextStory(): void;

    prevStory(): void;

    setActiveStoryIndex(activeStoryIndex: number): void;

    setPaused(paused: boolean): void;

    setStories(stories: StoryConfig[]): void;

    storyTick(): void;
}

export const useStoryStore = create<StoryStoreType>((set, get) => {
    return {
        activeStoryIndex: 0,
        defaultOffset: 0,
        nextStory() {
            const { activeStoryIndex, stories } = get();
            const nextIndex = activeStoryIndex + 1;

            if (nextIndex < stories.length) {
                const newStories = stories.map((story, index) => ({
                    ...story,
                    percent: index >= nextIndex ? 0 : 1,
                    time: index >= nextIndex ? 0 : story.time,
                }));

                set({
                    activeStoryIndex: nextIndex,
                    paused: false,
                    stories: newStories,
                });
            }
        },
        paused: false,
        prevStory() {
            const { activeStoryIndex, stories } = get();
            const prevIndex = activeStoryIndex - 1;

            if (prevIndex >= 0) {
                const newStories = stories.map((story, index) => ({
                    ...story,
                    percent: index >= prevIndex ? 0 : 1,
                    time: index >= prevIndex ? 0 : story.time,
                }));

                set({
                    activeStoryIndex: prevIndex,
                    paused: false,
                    stories: newStories,
                });
            }
        },

        setActiveStoryIndex(activeStoryIndex: number) {
            set({ activeStoryIndex });
        },
        setPaused(paused: boolean) {
            set({ paused });
        },
        setStories(stories) {
            set({
                activeStoryIndex: 0,
                defaultOffset: -(Dimensions.get('screen').width / stories.length),
                stories,
            });
        },
        stories: [],
        storyTick() {
            const { tick, activeStoryIndex, stories, paused } = get();
            if (paused) {
                return;
            }

            const story = { ...stories[activeStoryIndex] };

            story.time += tick;
            story.percent = story.time / story.duration;

            let newStoryIndex = story.percent >= 1 ? activeStoryIndex + 1 : activeStoryIndex;
            const isOver = newStoryIndex >= stories.length;

            if (isOver) {
                newStoryIndex = stories.length - 1;
            }

            set({
                activeStoryIndex: newStoryIndex,
                paused: isOver || paused,
                stories: stories.map((s, i) => (i === activeStoryIndex ? story : s)),
            });
        },

        tick: 100,
    };
});
