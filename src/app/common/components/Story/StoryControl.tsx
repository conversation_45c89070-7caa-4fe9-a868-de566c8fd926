import { useCallback } from 'react';
import { Pressable, PressableProps } from 'react-native';
import styled from 'styled-components/native';
import { useStoryStore } from './useStoryStore';

interface StoryControlProps extends PressableProps {
    position: 'left' | 'right';
}

function StoryControlComponent({ position, ...rest }: StoryControlProps) {
    const setPaused = useStoryStore((store) => store.setPaused);
    const nextStory = useStoryStore((store) => store.nextStory);
    const prevStory = useStoryStore((store) => store.prevStory);

    const handlePressIn = useCallback(() => setPaused(true), [setPaused]);

    const handlePressOut = useCallback(() => setPaused(false), [setPaused]);

    const handlePress = useCallback(() => {
        if (position === 'left') {
            prevStory();
        } else {
            nextStory();
        }
    }, [nextStory, position, prevStory]);

    return <Pressable onPressIn={handlePressIn} onPressOut={handlePressOut} onPress={handlePress} {...rest} />;
}

export const StoryControl = styled(StoryControlComponent)`
    position: absolute;
    flex: 1;
    top: 0;
    bottom: 0;
    width: 33%;
    z-index: 1;
    ${({ position }) => position}: -24px;
`;
