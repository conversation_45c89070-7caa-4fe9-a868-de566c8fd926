import { StoryScreen } from './StoryScreen';
import { useStoryStore } from './useStoryStore';

export function StoryContent() {
    const Component = useStoryStore((state) => state.stories[state.activeStoryIndex]?.component);
    const background = useStoryStore((state) => state.stories[state.activeStoryIndex]?.background);

    if (!Component) {
        return null;
    }

    return (
        <StoryScreen background={background} justifyContent={'space-between'}>
            <Component />
        </StoryScreen>
    );
}
