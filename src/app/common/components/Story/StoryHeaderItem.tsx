import { memo, useEffect, useMemo } from 'react';
import { Animated, Easing } from 'react-native';
import styled from 'styled-components/native';
import { useAnimStyle } from '~/hooks/useAnimStyle';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { StoryConfig } from './StoryConfig';
import { useStoryStore } from './useStoryStore';

interface StoryHeaderItemProps {
    story: StoryConfig;
}

function StoryHeaderItemComponent({ story }: StoryHeaderItemProps) {
    const defaultOffset = useStoryStore((state) => state.defaultOffset);
    const tick = useStoryStore((state) => state.tick);
    const offset = useMemo(
        () => Math.min(0, defaultOffset + Math.abs(defaultOffset * story.percent)),
        [defaultOffset, story.percent],
    );

    const [width, { timing }] = useAnimatedValue(defaultOffset);
    const style = useAnimStyle({
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        transform: [{ translateX: width.current as any }],
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
    useEffect(() => {
        timing(offset, {
            duration: tick,
            easing: Easing.linear,
        }).start();
    }, [offset, tick, timing]);

    return (
        <Root>
            <Inner style={style} />
        </Root>
    );
}

export const StoryHeaderItem = memo(StoryHeaderItemComponent);

const Root = styled.View`
    background-color: rgba(255, 255, 255, 0.33);
    border-radius: 2px;
    flex-basis: 0;
    flex-grow: 1;
    flex-shrink: 1;
    margin-right: 4px;
    margin-left: 4px;
    overflow: hidden;
`;

const Inner = styled(Animated.View)`
    background-color: #fff;
    height: 4px;
    border-radius: 2px;
    width: 100%;
`;
