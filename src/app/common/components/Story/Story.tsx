import { useEffect } from 'react';
import { StoryConfig } from './StoryConfig';
import { StoryContent } from './StoryContent';
import { useStoryStore } from './useStoryStore';

export interface StoryProps {
    activeIndex?: number;
    autoPlay?: boolean;
    stories: StoryConfig[];
}

export function Story({ stories, activeIndex = 0, autoPlay = true }: StoryProps) {
    const setStories = useStoryStore((state) => state.setStories);
    const setPaused = useStoryStore((state) => state.setPaused);
    const setActiveStoryIndex = useStoryStore((state) => state.setActiveStoryIndex);

    useEffect(() => setStories(stories), [setStories, stories]);
    useEffect(() => setPaused(!autoPlay), [setPaused, autoPlay]);
    useEffect(() => setActiveStoryIndex(activeIndex), [setActiveStoryIndex, activeIndex]);

    return <StoryContent />;
}
