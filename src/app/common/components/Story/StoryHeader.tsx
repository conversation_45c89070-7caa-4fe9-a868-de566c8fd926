import { useNavigation } from '@react-navigation/native';
import React from 'react';
import styled from 'styled-components/native';
import { HStack, VStack } from '~/components/ui/Grid';
import { ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useInterval } from '~/hooks/useInterval';
import { useApplicationStore } from '~/store/useApplicationStore';
import { StoryHeaderItem } from './StoryHeaderItem';
import { useStoryStore } from './useStoryStore';

export function StoryHeader() {
    const stories = useStoryStore((state) => state.stories);
    const activeStory = useStoryStore((state) => state.stories[state.activeStoryIndex]);
    const storyTick = useStoryStore((state) => state.storyTick);
    const tick = useStoryStore((state) => state.tick);
    const { canGoBack } = useNavigation();
    const setVisitedIntro = useApplicationStore((state) => state.setVisitedIntro);

    useInterval(storyTick, tick);

    const handleBackPress = useEvent(() => {
        setVisitedIntro(true);
    });

    return (
        <VStack pt={1.5} zIndex={5}>
            {canGoBack() && (
                <ScreenHeader
                    disableSafeViewArea
                    leftSlot={<ScreenHeaderBackButton color={'backgroundPrimary'} onPress={handleBackPress} />}
                />
            )}
            <HStack mx={-0.5} mb={1}>
                {stories.map((story, index) => (
                    <StoryHeaderItem key={`${story.name}_${index}`} story={story} />
                ))}
            </HStack>
            {activeStory?.name && (
                <HStack alignItems={'center'}>
                    <BookrSmallIcon source={require('~/assets/bookrSmallIcon.png')} />
                    <Typography variant={'caption2'} color={'primary.shade40'} fontWeight={400} ml={1}>
                        {activeStory?.name}
                    </Typography>
                </HStack>
            )}
        </VStack>
    );
}

const BookrSmallIcon = styled.Image`
    width: 16px;
    height: 16px;
`;
