import { ImageBackground, ImageSourcePropType, StyleSheet } from 'react-native';
import { VStack, VStackProps } from '~/components/ui/Grid';
import { StoryControl } from './StoryControl';
import { StoryHeader } from './StoryHeader';

interface StoryScreenProps extends VStackProps {
    background?: ImageSourcePropType | null;
}

export function StoryScreen({ children, background, ...rest }: StoryScreenProps) {
    const children$ = (
        <VStack flex safeArea px={3}>
            <StoryHeader />

            <StoryControl position={'left'} />
            <StoryControl position={'right'} />
            <VStack flex zIndex={3} pointerEvents={'box-none'} {...rest}>
                {children}
            </VStack>
        </VStack>
    );

    return (
        <VStack flex>
            {background ? (
                <ImageBackground style={styles.background} source={background}>
                    {children$}
                </ImageBackground>
            ) : (
                children$
            )}
        </VStack>
    );
}

const styles = StyleSheet.create({
    background: {
        flex: 1,
        height: '100%',
        resizeMode: 'contain',
        width: '100%',
    },
});
