import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { BottomSheet, BottomSheetProps } from '~/components/BottomSheet';
import { Button } from '~/components/ui/Button';
import { Container, HStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';

export function AccountVerificationBottomSheet({ onClose, ...rest }: Omit<BottomSheetProps, 'children'>): ReactElement {
    const { t } = useTranslation();

    return (
        <BottomSheet snapPoints={[460]} {...rest}>
            <Container flex safeBottom>
                <HStack py={5} alignItems={'center'} justifyContent={'center'}>
                    <Icon name={'drafts'} size={56} color="accent" />
                </HStack>
                <Typography variant="title3" fontWeight={700} textAlign="center" mb={2}>
                    {t('accountVerificationDialogTitle')}
                </Typography>
                <Typography variant="subhead" fontWeight={500} color="textSecondary" textAlign="center" mb={4}>
                    {t('accountVerificationDialogMessage')}
                </Typography>
                <Spacer />
                <Button label={t('gotIt')} size={'large'} mb={2} onPress={onClose} />
            </Container>
        </BottomSheet>
    );
}
