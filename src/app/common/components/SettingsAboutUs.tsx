import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import * as Linking from 'expo-linking';
import * as StoreReview from 'expo-store-review';
import * as WebBrowser from 'expo-web-browser';
import { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { RootStackParamList } from '~/RoutesParams';
import { List } from '~/components/ui/List';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { useEvent } from '~/hooks/useEvent';

export function SettingsAboutUs(): ReactElement {
    const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
    const { t } = useTranslation();

    const [canReview, setCanReview] = useState(false);

    const handleStoreReview = useEvent(() => StoreReview.requestReview());

    const handleInstagram = useEvent(() => Linking.openURL('https://www.instagram.com/bookr.ro/'));

    const handlePrivacyPolicy = useEvent(() =>
        WebBrowser.openBrowserAsync('https://bookr.ro/documents/privacy-policy.pdf'),
    );

    const handleTermsAndConditions = useEvent(() =>
        WebBrowser.openBrowserAsync('https://bookr.ro/documents/terms-and-conditions.pdf'),
    );

    const handleFeedback = useEvent(() => navigate('FeedbackScreen'));

    const handleDonate = useEvent(() => Linking.openURL('https://donate.stripe.com/eVa01k2fTeUU6YM7ss'));

    useEffect(() => {
        StoreReview.isAvailableAsync().then((canReview) => {
            setCanReview(canReview);
        });
    }, []);

    return (
        <Paper label={t('aboutUs')} bgColor={'backgroundPrimary'} fullWidth mb={2}>
            <List>
                {canReview && (
                    <ListItemButton onPress={handleStoreReview}>
                        <ListItemIcon>
                            <MaterialIcons name={'star'} />
                        </ListItemIcon>
                        <ListItemText
                            primary={
                                t(Platform.OS === 'android' ? 'reviewUsOnPlayStore' : 'reviewUsOnAppStore') as string
                            }
                            primaryTypographyProps={{ fontWeight: 500 }}
                        />
                    </ListItemButton>
                )}
                <ListItemButton onPress={handleInstagram}>
                    <ListItemIcon>
                        <MaterialCommunityIcons name={'instagram'} />
                    </ListItemIcon>
                    <ListItemText primary={t('followUsOnInstagram')} primaryTypographyProps={{ fontWeight: 500 }} />
                </ListItemButton>
                <ListItemButton onPress={handlePrivacyPolicy}>
                    <ListItemIcon>
                        <MaterialIcons name={'privacy-tip'} />
                    </ListItemIcon>
                    <ListItemText primary={t('privacyPolicy')} primaryTypographyProps={{ fontWeight: 500 }} />
                </ListItemButton>
                <ListItemButton onPress={handleTermsAndConditions}>
                    <ListItemIcon>
                        <MaterialCommunityIcons name={'file-document-multiple'} />
                    </ListItemIcon>
                    <ListItemText primary={t('termsAndConditions')} primaryTypographyProps={{ fontWeight: 500 }} />
                </ListItemButton>
                <ListItemButton onPress={handleFeedback}>
                    <ListItemIcon>
                        <MaterialIcons name={'feedback'} />
                    </ListItemIcon>
                    <ListItemText primary={t('feedback')} primaryTypographyProps={{ fontWeight: 500 }} />
                </ListItemButton>
                <ListItemButton onPress={handleDonate}>
                    <ListItemIcon>
                        <MaterialIcons name={'volunteer-activism'} color={'accent'} />
                    </ListItemIcon>
                    <ListItemText primary={t('donate')} primaryTypographyProps={{ fontWeight: 500 }} />
                </ListItemButton>
            </List>
        </Paper>
    );
}
