import { useFormikContext } from 'formik';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BottomSheetDatePicker } from '~/components/BottomSheetDatePicker';
import { BottomSheetTimePicker } from '~/components/BottomSheetTimePicker';
import { HStack, VStack } from '~/components/ui/Grid';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { FormikSelectStaffMember } from './FormikSelectStaffMember';

export function CreateBreakForm() {
    const { t } = useTranslation();
    const { values, handleChange } = useFormikContext<{
        endDay: string;
        endTime: string;
        staffMemberId: string | null;
        startDay: string;
        startTime: string;
        title: string;
    }>();

    return (
        <VStack mt={1}>
            <VStack mb={4}>
                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mb={1}>
                    {t('chooseTeamMember')}:
                </Typography>
                <FormikSelectStaffMember name={'staffMemberId'} disabled={false} />
            </VStack>
            <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mb={1}>
                {t('addBreakName')}:
            </Typography>
            <FormikTextField name={'title'} mb={4} label={t('breakName')} />
            <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500}>
                {t('breakBegins')}:
            </Typography>
            <HStack flexWrap={'nowrap'} mt={1} mb={4}>
                <VStack xs mr={1}>
                    <BottomSheetDatePicker
                        value={values.startDay}
                        label={t('chooseDay')}
                        onChange={handleChange('startDay')}
                    />
                </VStack>
                <VStack xs ml={1}>
                    <BottomSheetTimePicker
                        value={values.startTime}
                        label={t('chooseHour')}
                        onChange={handleChange('startTime')}
                    />
                </VStack>
            </HStack>

            <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500}>
                {t('breakEnds')}:
            </Typography>
            <HStack flexWrap={'nowrap'} mt={1}>
                <VStack xs mr={1}>
                    <BottomSheetDatePicker
                        value={values.endDay}
                        label={t('chooseDay')}
                        onChange={handleChange('endDay')}
                    />
                </VStack>
                <VStack xs ml={1}>
                    <BottomSheetTimePicker
                        value={values.endTime}
                        label={t('chooseHour')}
                        onChange={handleChange('endTime')}
                    />
                </VStack>
            </HStack>
        </VStack>
    );
}
