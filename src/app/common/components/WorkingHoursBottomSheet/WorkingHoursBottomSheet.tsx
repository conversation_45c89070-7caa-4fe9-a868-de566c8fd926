/* eslint-disable @typescript-eslint/no-explicit-any */
import { capitalize } from 'lodash';
import moment from 'moment';
import { ReactElement, useMemo } from 'react';
import { useTheme } from 'styled-components';
import { WorkingDay } from '@bookr-technologies/api/constants/WorkingDay';
import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';
import { BottomSheet } from '~/components/BottomSheet';
import { Badge } from '~/components/ui/Badge';
import { Container, Grid, HStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { Typography } from '~/components/ui/Typography';
import { convertHoursToBusinessTimezone } from '~/components/utils/time';
import { useI18n } from '~/hooks/useTranslation';

export interface WorkingHoursBottomSheetProps {
    hours: WorkingHourModel[];
    onClose: () => void;
    open: boolean;
    zoneId: string;
}

export function WorkingHoursBottomSheet({ hours, zoneId, open, onClose }: WorkingHoursBottomSheetProps): ReactElement {
    const t = useI18n();
    const theme = useTheme();

    const computeHours = (dayHours: WorkingHourModel[]): string =>
        convertHoursToBusinessTimezone(dayHours, zoneId)
            .map((hour) => `${String(hour.start)} - ${String(hour.end)}`)
            .join('\n');

    const TodayBadgeStatus = (): ReactElement => {
        let todayHours = hours.filter(
            (hour) => moment().locale('en').day(hour.day).day() === moment().locale('en').day(),
        );
        if (todayHours.length === 0) {
            return <Badge title={t('closed')} color={'error'} />;
        }
        todayHours = convertHoursToBusinessTimezone(todayHours, zoneId);
        const now = moment().format('HH:mm');
        for (const day of todayHours) {
            if (day.start <= now && day.end >= now) {
                return <Badge title={t('open')} color={'success'} />;
            }
        }
        return <Badge title={t('closed')} color={'error'} />;
    };

    const workingHours = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(
        (d, index) => ({
            index,
            day: t(`days.${d}` as any),
            hours:
                computeHours(
                    hours.filter(
                        (hour) => hour.day === (WorkingDay[capitalize(d) as keyof typeof WorkingDay] as WorkingDay),
                    ),
                ) || t('closed'),
        }),
    );

    const currentDay = useMemo(() => moment().weekday(), []);

    return (
        <BottomSheet snapPoints={['80%']} open={open} onClose={onClose}>
            <Container flex pt={3}>
                <HStack alignItems={'center'} justifyContent={'space-between'}>
                    <Typography variant={'title3'} fontSize={20} fontWeight={700} lineHeight={25}>
                        {t('workingSchedule')}
                    </Typography>
                    <TodayBadgeStatus />
                </HStack>

                <Grid mx={-2} flex>
                    <List scrollable>
                        {workingHours.map(({ day, index, hours }) => {
                            const isCurrentDay = index === currentDay;

                            return (
                                <ListItem
                                    bgColor={isCurrentDay ? theme.palette.accent.shade10 : 'transparent'}
                                    key={day}
                                    flexGrow={1}
                                    alignItems={'center'}
                                    justifyContent={'space-between'}
                                    borderRadius={12}
                                    px={2}
                                    my={0.5}
                                >
                                    <Typography
                                        variant={'subhead'}
                                        fontSize={15}
                                        fontWeight={500}
                                        lineHeight={22.5}
                                        color={isCurrentDay ? theme.palette.accent.main : 'primary'}
                                    >
                                        {day}
                                    </Typography>
                                    <Typography
                                        variant={'subhead'}
                                        fontSize={15}
                                        fontWeight={500}
                                        textAlign={'right'}
                                        lineHeight={22.5}
                                        color={isCurrentDay ? theme.palette.accent.main : 'primary'}
                                    >
                                        {hours}
                                    </Typography>
                                </ListItem>
                            );
                        })}
                    </List>
                </Grid>
            </Container>
        </BottomSheet>
    );
}
