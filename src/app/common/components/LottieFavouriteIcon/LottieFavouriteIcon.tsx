/* eslint-disable */
import LottieView from 'lottie-react-native';
import { useEffect, useRef } from 'react';
import { TouchableOpacity } from 'react-native';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import heartAnimation from '../../../../assets/lottie/heart.json';

interface LottieFavouriteIconProps {
    containerStyle?: any;
    isFavourite: boolean;
    lottieStyle?: any;
    onPress: () => void;
}

const useStyles = makeStyles(() => ({
    container: {
        width: '100%',
        height: '100%',
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    lottieView: {
        width: 60,
        height: 60,
    },
}));

export function LottieFavouriteIcon({ isFavourite, onPress, containerStyle, lottieStyle }: LottieFavouriteIconProps) {
    const styles = useStyles();
    const lottieRef = useRef<LottieView | null>(null);

    const handleOnPress = useEvent(() => {
        if (isFavourite) {
            lottieRef.current?.reset();
        }
        onPress && onPress();
    });

    useEffect(() => {
        if (isFavourite) {
            lottieRef.current?.play();
        }
    }, [isFavourite]);

    return (
        <TouchableOpacity onPress={handleOnPress} style={[styles.container, containerStyle || {}]}>
            <LottieView
                loop={false}
                ref={lottieRef}
                style={[styles.lottieView, lottieStyle || {}]}
                source={heartAnimation}
            />
        </TouchableOpacity>
    );
}
