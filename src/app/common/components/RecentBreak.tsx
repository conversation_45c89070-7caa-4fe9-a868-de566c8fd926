import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components/native';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { formatDate } from '@bookr-technologies/core';
import { Box } from '~/components/ui/Box';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';

export function RecentBreak({
    breakItem,
    onDelete,
}: {
    breakItem: BreakModel;
    onDelete: (id: number) => void | Promise<void>;
}): ReactElement {
    const theme = useTheme();
    const loading = useLoading();
    const { t } = useTranslation();

    const handleDeletePress = useEvent(() => loading.from(() => onDelete?.(breakItem.id)));

    return (
        <Paper mb={2} p={2} bgColor={'backgroundPrimary'}>
            <HStack alignItems={'center'} justifyContent={'space-between'} mb={2}>
                <HStack alignItems={'center'} justifyContent={'center'}>
                    <MaterialCommunityIcons size={24} name={'coffee'} color={theme.palette.typography.textPrimary} />
                    <Typography variant={'footnote'} fontWeight={600} fontSize={16} ml={1.5}>
                        {breakItem.title || t('break')}
                    </Typography>
                </HStack>
                {loading.isLoading() ? (
                    <Box width={40} height={40} alignItems={'center'} alignContent={'center'} justifyContent={'center'}>
                        <CircularProgress />
                    </Box>
                ) : (
                    <IconButton onPress={handleDeletePress} color={'typography.textSecondary'}>
                        <MaterialIcons name={'close'} />
                    </IconButton>
                )}
            </HStack>
            <VStack>
                <HStack mb={0.5}>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {t('from')}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500}>
                        {formatDate(breakItem.fromDateTime, 'LLL')}
                    </Typography>
                </HStack>
                <HStack>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {t('to')}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500}>
                        {formatDate(breakItem.toDateTime, 'LLL')}
                    </Typography>
                </HStack>
            </VStack>
        </Paper>
    );
}
