import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { BottomSheet, BottomSheetProps } from '~/components/BottomSheet';
import { Button } from '~/components/ui/Button';
import { Container, HStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { List } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useUser } from '~/hooks/useUser';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { useAuthStore } from '~/store/useAuthStore';

export function DeleteAccountModal({ ...rest }: Omit<BottomSheetProps, 'children'>): ReactElement {
    const { t } = useTranslation();
    const user = useUser();
    const logout = useAuthStore((state) => state.logout);

    const deleteAccountMutation = useMutation(async () => {
        if (user?.accountType === AccountType.BusinessOwner) {
            logAnalyticsEvent(AnalyticsEvent.BusinessDeleted);
        }
        await usersEndpoint.deleteAccount();
        await logout();
    });

    const sideEffects = useMemo(
        () => [
            t('yourDevicesWillNotBeAbleToAccessYourAccount'),
            t('allYourDataWillBeLost'),
            !!user?.business?.id && t('allYourStaffMembersWillBeRemoved'),
            t('allYourFutureBookingsWillBeCancelled'),
        ],
        [t, user?.business?.id],
    );

    const handlePressDelete = useEvent(() => deleteAccountMutation.mutateAsync());
    return (
        <BottomSheet snapPoints={['80%']} {...rest}>
            <Container flex safeBottom={2}>
                <Typography variant="title2" fontWeight={700} mb={1}>
                    {t('deleteAccount')}
                </Typography>
                <Typography variant="subhead" fontWeight={500} color="textSecondary" mb={6}>
                    {t('weWillNotBeAbleToRecoverYourAccount')}
                </Typography>

                <Typography fontWeight={700} mb={2}>
                    {t('deleteAccountSideEffects')}
                </Typography>
                <List flexDirection={'column'} justifyContent={'flex-start'} py={0} mb={2}>
                    {sideEffects.filter(Boolean).map((text, index) => (
                        <ListItem key={index} alignItems={'flex-start'} px={0} my={1} minHeight={0} height={'auto'}>
                            <ListItemIcon color={'error'}>
                                <Icon name={'highlight-off'} />
                            </ListItemIcon>
                            <ListItemText
                                flexWrap={'nowrap'}
                                primary={text}
                                primaryTypographyProps={{
                                    variant: 'subhead',
                                    fontWeight: 500,
                                    color: 'textSecondary',
                                }}
                            />
                        </ListItem>
                    ))}
                </List>

                <HStack mb={2}>
                    {!deleteAccountMutation.isLoading && (
                        <Button
                            variant={'subtle'}
                            size={'small'}
                            label={t('deleteAccount')}
                            onPress={handlePressDelete}
                        />
                    )}
                </HStack>

                <Spacer />

                <Button
                    color={'accent'}
                    size={'large'}
                    label={t('keepMyAccountActive')}
                    onPress={rest.onClose}
                    loading={deleteAccountMutation.isLoading}
                />
            </Container>
        </BottomSheet>
    );
}
