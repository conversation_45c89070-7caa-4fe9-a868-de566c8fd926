import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { CacheManager } from 'react-native-expo-image-cache';
import { DeleteAccountModal } from '~/app/common/components/DeleteAccountModal';
import { List } from '~/components/ui/List';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useAuthStore } from '~/store/useAuthStore';

export function SettingsActions(): ReactElement {
    const { t } = useTranslation();
    const deleteModal = useModal();

    const logout = useAuthStore((store) => store.logout);

    const handlePressLogout = useEvent(() => Promise.all([AsyncStorage.clear(), CacheManager.clearCache(), logout()]));

    return (
        <>
            <Paper bgColor={'backgroundPrimary'} fullWidth mb={5}>
                <List>
                    <ListItemButton onPress={handlePressLogout}>
                        <ListItemIcon color={'error'}>
                            <MaterialIcons name={'logout'} />
                        </ListItemIcon>
                        <ListItemText primary={t('logout')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                </List>
            </Paper>

            <Paper bgColor={'backgroundPrimary'} fullWidth mb={3}>
                <List>
                    <ListItemButton onPress={deleteModal.open}>
                        <ListItemIcon color={'error'}>
                            <MaterialIcons name={'delete'} />
                        </ListItemIcon>
                        <ListItemText
                            primary={t('deleteAccount')}
                            primaryTypographyProps={{ fontWeight: 500, color: 'error' }}
                        />
                    </ListItemButton>
                </List>
            </Paper>

            <DeleteAccountModal {...deleteModal.props} />
        </>
    );
}
