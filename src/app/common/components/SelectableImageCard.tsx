import { useCallback } from 'react';
import { Image, ImageSourcePropType, ImageStyle, LayoutRectangle, Pressable } from 'react-native';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useLayout } from '~/hooks/useLayout';
import { usePressable } from '~/hooks/usePressable';

interface Props<T> {
    description: string;
    headline: string;

    image: ImageSourcePropType;

    onPress: (value?: T) => void;
    value?: T;
}

export function SelectableImageCard<T = never>({ image, headline, description, value, onPress }: Props<T>) {
    const layout = useLayout();
    const styles = useStyles(layout);

    const [isPressing, pressableProps] = usePressable();

    const handlePress = useCallback(() => onPress(value), [onPress, value]);

    return (
        <Pressable onPress={handlePress} {...pressableProps}>
            <Paper
                mt={2}
                bgColor={!isPressing ? 'backgroundTertiary' : 'backgroundSecondary'}
                overflow={'hidden'}
                minHeight={160}
                onLayout={layout.handler}
            >
                <Image source={image} style={styles.image as ImageStyle} />
                <VStack p={3} maxWidth={224}>
                    <Typography variant={'title3'} fontWeight={700} mb={1}>
                        {headline}
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} maxWidth={'100%'}>
                        {description}
                    </Typography>
                </VStack>
            </Paper>
        </Pressable>
    );
}

const useStyles = makeStyles<Pick<LayoutRectangle, 'height'>, Styles<'image'>>(({ height }) => ({
    image: {
        bottom: 0,
        height,
        maxWidth: 300,
        position: 'absolute',
        right: 0,
        top: 0,
    },
}));
