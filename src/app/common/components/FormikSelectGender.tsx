import { useFormikContext } from 'formik';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { MenuItem } from '~/components/ui/MenuItem';
import { Select, SelectProps } from '~/components/ui/Select';

interface Props extends Omit<SelectProps, 'value' | 'children'> {
    name: string;
}

export function FormikSelectGender({ name, onChange, ...rest }: Props) {
    const { t } = useTranslation();
    const formik = useFormikContext();
    const { value } = formik.getFieldMeta<string | null>(name);

    const handleChange = useCallback<Exclude<Props['onChange'], undefined>>(
        (item, value, data) => {
            formik.setFieldValue(name, value);
            onChange?.(item, value, data);
        },
        [formik, name, onChange],
    );

    return (
        <Select value={value} onChange={handleChange} {...rest}>
            {['male', 'female', 'other'].map((gender) => (
                <MenuItem key={gender} value={gender.toUpperCase()}>
                    {t(gender as any)}
                </MenuItem>
            ))}
        </Select>
    );
}
