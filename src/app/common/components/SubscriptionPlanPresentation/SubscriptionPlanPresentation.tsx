/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement } from 'react';
import { Trans } from 'react-i18next';
import { ImageBackground, ImageStyle } from 'react-native';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { Optional } from '@bookr-technologies/core/types';
import { HStack, VStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { Spacer } from '~/components/ui/Spacer';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { TermsAndConditionsLink } from '~/data/applicationData';
import * as subscriptionPlansData from '~/data/subscriptionPlansData';
import { useLayout } from '~/hooks/useLayout';
import { useI18n } from '~/hooks/useTranslation';

interface SubscriptionPlanPresentationProps {
    data: (typeof subscriptionPlansData)[keyof typeof subscriptionPlansData];
    plan: {
        planName: string;
        previousPlanName: string;
        price: Optional<SubscriptionPlanModel>;
    };
    planName: string;
}

const useStyles = makeStyles(() => ({
    banner: {
        borderRadius: 12,
        overflow: 'hidden',
        width: '100%',
    },
    lineThrough: {
        textDecorationLine: 'line-through',
    },
}));

export function SubscriptionPlanPresentation({
    planName,
    data,
    plan,
}: SubscriptionPlanPresentationProps): ReactElement {
    const layout = useLayout();
    const styles = useStyles();
    const t = useI18n();

    return (
        <VStack pb={3} px={3} scrollable>
            <ImageBackground
                source={data.banner}
                style={[styles.banner as ImageStyle, { height: layout.width * 0.6 }]}
                onLayout={layout.handler}
            >
                <VStack flex p={2}>
                    <Typography color={'#fff'} variant={'title2'} fontWeight={700}>
                        {planName}
                    </Typography>

                    <Spacer />

                    <Typography color={'#fff'} variant={'callout'} fontWeight={700} mb={0.5}>
                        {plan.price?.price}
                    </Typography>
                    <Typography color={'#fff'} variant={'footnote'} fontWeight={500}>
                        {t(data.bannerDescription)}
                    </Typography>
                </VStack>
            </ImageBackground>

            {data.features.map((section, row) => (
                <Paper bgColor={'backgroundPrimary'} key={row} mt={2}>
                    <Typography variant={'caption1'} fontWeight={600} color={'textSecondary'} pt={3} pl={2} pb={1}>
                        {t(section.name)}
                    </Typography>
                    <List>
                        {section.features.map((feature, col) => (
                            <ListItem key={`${row}_${col}`}>
                                <ListItemIcon
                                    color={!feature.disabled ? 'typography.textPrimary' : 'typography.disabled'}
                                >
                                    {/* eslint-disable-next-line @typescript-eslint/no-non-null-assertion */}
                                    {feature.icon!}
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <HStack>
                                            <Typography
                                                variant={'subhead'}
                                                fontWeight={500}
                                                color={
                                                    !feature.disabled ? 'typography.textPrimary' : 'typography.disabled'
                                                }
                                                style={feature.disabled && styles.lineThrough}
                                            >
                                                {t(feature.name)}
                                            </Typography>

                                            {feature.limited ? (
                                                <Typography
                                                    variant={'footnote'}
                                                    fontWeight={500}
                                                    color={'typography.disabled'}
                                                    pl={1}
                                                >
                                                    {t('limited')}
                                                </Typography>
                                            ) : null}
                                        </HStack>
                                    }
                                />
                            </ListItem>
                        ))}
                    </List>
                </Paper>
            ))}

            <HStack justifyContent={'center'} flexWrap p={2}>
                <Trans
                    i18nKey={'chooseSubscriptionPlanTerms'}
                    components={{
                        CancelLink: (
                            <TypographyLink
                                variant={'caption1'}
                                fontWeight={500}
                                href={TermsAndConditionsLink}
                                ml={0.5}
                            />
                        ),
                        TermsLink: (
                            <TypographyLink
                                variant={'caption1'}
                                fontWeight={500}
                                href={TermsAndConditionsLink}
                                mx={0.5}
                            />
                        ),
                        Text: <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'} />,
                    }}
                />
            </HStack>
        </VStack>
    );
}
