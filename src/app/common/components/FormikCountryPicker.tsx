import { useFormikContext } from 'formik';
import React, { useState } from 'react';
import { Platform } from 'react-native';
import { CountryPicker } from '~/components/PhoneInput/CountryPicker';
import { DialCode } from '~/components/PhoneInput/assets/dialCodes';
import { FormikTextField, TextFieldProps } from '~/components/ui/TextField';
import { useEvent } from '~/hooks/useEvent';

interface Props extends Omit<TextFieldProps, 'value' | 'defaultValue' | 'error'> {
    name: string;
}
export function FormikCountryPicker({ name, onChange, ...rest }: Props) {
    const formik = useFormikContext();
    const [countryPickerVisible, setCountryPickerVisible] = useState(false);

    const handleSelect = useEvent((countrySelected: DialCode): void => {
        formik.setFieldValue(name, countrySelected.countryCode);
        setCountryPickerVisible(false);
    });

    const handleOnClose = useEvent(() => setCountryPickerVisible(false));

    return (
        <>
            <FormikTextField
                name={name}
                editable={Platform.OS === 'android'}
                onPressIn={() => setCountryPickerVisible(true)}
                {...rest}
            />
            <CountryPicker visible={countryPickerVisible} onSelect={handleSelect} onRequestClose={handleOnClose} />
        </>
    );
}
