import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation } from 'react-native';
import { useQuery } from 'react-query';
import { categoriesEndpoint } from '@bookr-technologies/api/endpoints/categoriesEndpoint';
import { CategoryBox } from '~/app/common/components/CategoryList/CategoryBox';
import { Button } from '~/components/ui/Button';
import { HStack } from '~/components/ui/Grid';

interface Props {
    multiSelect?: boolean;
    onCategoryPress: (category: string) => void;
}

export const CategoryList = ({ multiSelect, onCategoryPress }: Props) => {
    const { t } = useTranslation();
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [showAllCategories, setShowAllCategories] = useState(false);
    const { data: categories, isLoading } = useQuery('categories', () => categoriesEndpoint.fetchAvailableCategories());

    const handleOnPressShowAll = (): void => {
        LayoutAnimation.easeInEaseOut();
        setShowAllCategories((s) => !s);
    };

    if (isLoading || !categories) {
        return null;
    }

    const _onPressCategory = (category: string): void => {
        setSelectedCategories((prev) => {
            if (!multiSelect) {
                return [category];
            }
            if (prev.includes(category)) {
                return prev.filter((c) => c !== category);
            }
            return [...prev, category];
        });
        onCategoryPress(category);
    };

    const renderCategories = () => {
        const list = showAllCategories ? categories : categories.slice(0, 8);
        const categoriesList = [];
        for (let i = 0; i < list.length; i += 2) {
            categoriesList.push(
                <HStack width={'100%'} key={i}>
                    <CategoryBox
                        width={'40%'}
                        flex
                        category={list[i].code}
                        selected={selectedCategories.includes(list[i].code)}
                        onPress={() => _onPressCategory(list[i].code)}
                        mb={1}
                        mr={0.5}
                    />
                    {list[i + 1] && (
                        <CategoryBox
                            width={'40%'}
                            flex
                            category={list[i + 1].code}
                            selected={selectedCategories.includes(list[i + 1].code)}
                            onPress={() => _onPressCategory(list[i + 1].code)}
                            mb={1}
                            ml={0.5}
                        />
                    )}
                </HStack>,
            );
        }
        return categoriesList;
    };

    return (
        <HStack width={'100%'} alignItems={'center'} justifyContent={'space-between'}>
            {renderCategories()}
            <Button
                mt={1.5}
                variant={'outlined'}
                fullWidth
                size={'medium'}
                borderWidth={2}
                borderColor={'contentTertiary'}
                label={showAllCategories ? t('showLess') : t('showAllCategories')}
                onPress={handleOnPressShowAll}
            />
        </HStack>
    );
};
