import {
    BarberIcon,
    BeautyIcon,
    BodyRemodelingIcon,
    CoachingIcon,
    ConsultancyIcon,
    DentistryIcon,
    DermatologyIcon,
    GinecologyIcon,
    HairImplant,
    MakeupIcon,
    ManicureIcon,
    MassageAndSpaIcon,
    MentoringIcon,
    NutritionDieateticsIcon,
    OphthalmologyIcon,
    OrlIcon,
    OtherIcon,
    PermanentHairRemovalIcon,
    PersonalTrainerIcon,
    PetGroomingIcon,
    PlasticSurgeryIcon,
    PodiatryIcon,
    PsychologistIcon,
    SurgeryIcon,
} from '~/app/common/components/CategoryIcons/CategoryIcons';

export const getCategoryIcon = (category: string) => {
    switch (category.toUpperCase()) {
        case 'BARBER':
            return <BarberIcon />;
        case 'COSMETICS':
            return <MakeupIcon />;
        case 'COWORKING':
            return <OtherIcon />;
        case 'DENTISTRY':
            return <DentistryIcon />;
        case 'DERMATOLOGY':
            return <DermatologyIcon />;
        case 'EVENT':
            return <OtherIcon />;
        case 'HAIRSTYLING':
            return <BeautyIcon />;
        case 'MAKEUP':
            return <MakeupIcon />;
        case 'MANICURE':
            return <ManicureIcon />;
        case 'MASSAGE':
            return <MassageAndSpaIcon />;
        case 'NUTRITION_DIETETICS':
            return <NutritionDieateticsIcon />;
        case 'OPHTHALMOLOGY':
            return <OphthalmologyIcon />;
        case 'OTHER':
            return <OtherIcon />;
        case 'PHOTOGRAPHY':
            return <OtherIcon />;
        case 'PSYCHOLOGIST':
            return <PsychologistIcon />;
        case 'BODY_REMODELING':
            return <BodyRemodelingIcon />;
        case 'SPORT':
            return <PersonalTrainerIcon />;
        case 'SURGERY':
            return <SurgeryIcon />;
        case 'TATTOOS':
            return <OtherIcon />;
        case 'VIDEOGRAPHY':
            return <OtherIcon />;
        case 'ORL':
            return <OrlIcon />;
        case 'IMPLANT':
            return <HairImplant />;
        case 'PODIATRY':
            return <PodiatryIcon />;
        case 'GYNECOLOGY':
            return <GinecologyIcon />;
        case 'PLASTIC_SURGERY':
            return <PlasticSurgeryIcon />;
        case 'PERMANENT_HAIR_REMOVAL':
            return <PermanentHairRemovalIcon />;
        case 'MENTORING':
            return <MentoringIcon />;
        case 'COACHING':
            return <CoachingIcon />;
        case 'CONSULTANCY':
            return <ConsultancyIcon />;
        case 'PET_GROOMING':
            return <PetGroomingIcon />;
        default:
            return <OtherIcon />;
    }
};
