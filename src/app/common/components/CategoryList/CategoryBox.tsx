import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { getCategoryIcon } from '~/app/common/components/CategoryList/getCategoryIcon';
import { VStack, VStackProps } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';

interface Props extends VStackProps {
    category: string;
    onPress: (category: string) => void;
    selected: boolean;
}

export const CategoryBox = ({ category, selected, onPress, ...rest }: Props) => {
    const { t } = useTranslation();
    const styles = useStyles();
    return (
        <VStack
            justifyContent={'center'}
            alignItems={'center'}
            width={'100%'}
            height={176}
            bgColor={'backgroundSecondary'}
            borderRadius={8}
            borderWidth={2}
            borderColor={'transparent'}
            style={selected ? styles.selected : undefined}
            {...rest}
        >
            <TouchableOpacity style={styles.root} onPress={() => onPress(category)}>
                {getCategoryIcon(category)}

                <Typography variant={'footnote'} color={'contentPrimary'} fontWeight={700} mt={0.5}>
                    {t(`categories.${category.toLowerCase()}` as any)}
                </Typography>
            </TouchableOpacity>
        </VStack>
    );
};

const useStyles = makeStyles(({ theme }) => ({
    root: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    selected: {
        borderColor: theme.palette.accent.main,
    },
}));
