import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { IconButton } from '~/components/ui/IconButton';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';

type RatingAndReviewsProps = {
    onReviewPress?: () => void;
    reviewsInfo: BusinessModel['reviewInfo'];
    showStar?: boolean;
};

const useStyles = makeStyles(() => ({
    textUnderline: {
        textDecorationLine: 'underline',
    },
}));

export function RatingAndReviews({ reviewsInfo, showStar, onReviewPress }: RatingAndReviewsProps): JSX.Element {
    const { t } = useTranslation();
    const styles = useStyles();

    return (
        <>
            {showStar && (
                <IconButton disablePadding mr={1} disabled>
                    <MaterialIcons name={'star'} size={13} color={`warning`} />
                </IconButton>
            )}
            <Typography variant={'footnote'} fontWeight={500}>
                {t('ratingNumber', { rating: reviewsInfo.averageRating })}
            </Typography>
            <Typography fontSize={4} color={'disabled'} variant={'caption1'} fontWeight={500} mx={0.5}>
                ●
            </Typography>
            <TypographyLink
                variant={'footnote'}
                fontWeight={500}
                color={'disabled'}
                onPress={onReviewPress}
                style={!!onReviewPress && styles.textUnderline}
            >
                {t('reviewsNumber', { reviews: reviewsInfo.noOfReviews })}
            </TypographyLink>
        </>
    );
}
