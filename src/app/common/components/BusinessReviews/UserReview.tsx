import React from 'react';
import { ReviewModel } from '@bookr-technologies/api/models/ReviewModel';
import { formatDate } from '@bookr-technologies/core';
import { Avatar } from '~/components/ui/Avatar';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { Rating } from './Rating';

const formatName = (displayName: string): string => {
    const parts = displayName.trim().split(' ');
    if (parts.length > 1) {
        return `${parts[0]} ${parts[1].charAt(0)}.`;
    } else {
        return parts[0];
    }
};

export function UserReview({ review }: { review: ReviewModel }): JSX.Element {
    const { user } = review;

    return (
        <Grid mb={review.comment?.trim() ? 3 : 0} fullWidth>
            <HStack alignItems={'flex-start'} justifyContent={'space-between'} fullWidth>
                <HStack flex>
                    <Avatar source={user.photoURL} name={user.displayName} size={44} />
                    <VStack ml={2}>
                        <Typography variant={'subhead'} fontWeight={500} mb={0.5}>
                            {formatName(user.displayName)}
                        </Typography>
                        <Typography variant={'caption1'} fontWeight={500} color={'disabled'}>
                            {formatDate(review.dateTime ?? '', 'll')}
                        </Typography>
                    </VStack>
                </HStack>
                <Rating rating={review.rating} size={14} starMargin={0.25} />
            </HStack>
            <Typography
                variant={'caption1'}
                fontWeight={500}
                lineHeight={18}
                letterSpacing={0.14}
                mt={1}
                color={'contentTertiary'}
            >
                {review.comment}
            </Typography>
        </Grid>
    );
}
