import React, { ReactElement } from 'react';
import { View } from 'react-native';
import { useTheme } from 'styled-components/native';
import { Grid, HStack } from '~/components/ui/Grid';
import { Radio } from '~/components/ui/Radio';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { Rating } from './Rating';

type RatingFilterProps = {
    onFilterPress: (ratingValue?: number) => void;
    ratingCount: RatingsChart;
    selectedFilter?: number;
    totalNoOfReviews: number | undefined;
};

type RatingsChart = {
    noOfRatings: number;
    rating: number;
};

const useStyles = makeStyles(({ theme }) => ({
    ratingFilterBar: {
        height: 6,
        flex: 1,
        borderRadius: 28,
        marginHorizontal: 15,
        overflow: 'hidden',
        backgroundColor: theme.palette.backgroundTertiary.main,
    },
    ratingFilterFillingBar: {
        height: 6,
        borderRadius: 28,
    },
    radio: {
        height: 'auto',
    },
}));

export function RatingFilter({
    selectedFilter,
    onFilterPress,
    ratingCount,
    totalNoOfReviews,
}: RatingFilterProps): ReactElement | null {
    const theme = useTheme();
    const styles = useStyles();

    if (!totalNoOfReviews) {
        return null;
    }

    const fillingPercentage = Math.round((ratingCount.noOfRatings * 100) / totalNoOfReviews);

    const handleCheckboxPress = useEvent(() => {
        onFilterPress(selectedFilter === ratingCount.rating ? undefined : ratingCount.rating);
    });

    return (
        <HStack alignItems={'center'}>
            <Grid mr={2}>
                <Radio
                    onPress={handleCheckboxPress}
                    checked={selectedFilter === ratingCount.rating}
                    style={styles.radio}
                />
            </Grid>
            <Rating rating={ratingCount.rating} size={14} starMargin={0.25} />
            <View style={styles.ratingFilterBar}>
                <View
                    style={[
                        styles.ratingFilterFillingBar,
                        { backgroundColor: theme.palette.warning.main, width: `${fillingPercentage}%` },
                    ]}
                />
            </View>
            <Typography variant={'footnote'} fontWeight={500} color={'disabled'}>
                {ratingCount.noOfRatings}
            </Typography>
        </HStack>
    );
}
