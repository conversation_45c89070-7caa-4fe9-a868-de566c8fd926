import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import { useTheme } from 'styled-components/native';
import { Grid } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { STARS } from '~/lib/utils/review';

type RatingProps = {
    rating: number;
    size: number;
    starMargin: number;
};

export function Rating({ rating, size, starMargin }: RatingProps): JSX.Element {
    const theme = useTheme();

    return (
        <Grid flexDirection={'row'}>
            {STARS.map((_, index) => {
                const starColor = index < rating ? theme.palette.warning.main : theme.palette.backgroundTertiary.main;
                return (
                    <IconButton disablePadding key={`start${index}`} mr={starMargin} disabled>
                        <MaterialIcons name={'star'} size={size} color={starColor} />
                    </IconButton>
                );
            })}
        </Grid>
    );
}
