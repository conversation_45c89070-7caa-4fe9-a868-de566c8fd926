import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { WorkingHoursBottomSheet } from '~/app/common/components/WorkingHoursBottomSheet';
import { Button } from '~/components/ui/Button';
import { Grid, HStack } from '~/components/ui/Grid';

export function BusinessProfileActions({ business }: { business: BusinessModel }) {
    const { t } = useTranslation();
    const [scheduleOpen, setScheduleOpen] = useState(false);
    const { navigate } = useNavigation();

    const handlePressCall = () => {
        Linking.openURL(`tel:${business.phoneNumber}`);
    };

    const handlePressLocation = () => navigate('BusinessLocationScreen', { business });

    const handlePressWorkingHours = () => setScheduleOpen(true);

    const handleClose = () => setScheduleOpen(false);

    return (
        <HStack px={3} mt={2} justifyContent={'space-between'} alignItems={'center'} width={'100%'}>
            <Grid xs mr={1.5}>
                <Button
                    startIcon={<MaterialCommunityIcons name={'calendar-text'} color={'accent'} />}
                    color={'secondary'}
                    size={'small'}
                    label={t('schedule')}
                    onPress={handlePressWorkingHours}
                />
            </Grid>
            <Grid xs mr={1.5}>
                <Button
                    startIcon={<MaterialIcons name={'map'} color={'accent'} />}
                    color={'secondary'}
                    size={'small'}
                    label={t('location')}
                    onPress={handlePressLocation}
                />
            </Grid>
            <Grid xs>
                <Button
                    startIcon={<MaterialCommunityIcons name={'phone-in-talk-outline'} color={'accent'} />}
                    color={'secondary'}
                    size={'small'}
                    label={t('call')}
                    onPress={handlePressCall}
                />
            </Grid>
            <WorkingHoursBottomSheet
                hours={business.workingHours}
                zoneId={business.zoneId}
                open={scheduleOpen}
                onClose={handleClose}
            />
        </HStack>
    );
}
