import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useFormikContext } from 'formik';
import { uniqBy } from 'lodash';
import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, LayoutAnimation } from 'react-native';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api';
import { breaksEndpoint } from '@bookr-technologies/api/endpoints/breaksEndpoint';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { PageableResponse } from '@bookr-technologies/api/types/PageableResponse';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { RecentBreak } from '~/app/common/components/RecentBreak';
import emptyAppointments from '~/assets/emptyBackgrounds/emptyAppointments.png';
import { EmptyStateCard } from '~/components/EmptyStateCard';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { Loader } from './Loader';

export interface RecentBreaksValues {
    active: boolean;
    breaks: BreakModel[];
}

type RecentBreaksProps = {
    businessId: string | undefined;
    staffId: string | undefined;
};

export function RecentBreaks({ businessId, staffId }: RecentBreaksProps): ReactElement {
    const { t } = useTranslation();
    const notifications = useNotifications();
    const { values, setFieldValue } = useFormikContext<{ recentBreaks: RecentBreaksValues }>();
    const [page, setPage] = useState(0);
    const [breaks, setBreaks] = useState<BreakModel[]>([]);

    const handleNewBreakPress = useEvent(() => {
        setFieldValue('recentBreaks.active', false);
    });

    const onError = (e: Error): void => {
        setBreaks([]);
        notifications.error(getErrorMessage(e));
    };

    const onSuccess = (data: PageableResponse<BreakModel>): void => {
        setBreaks((breaks) => uniqBy(page === 0 ? data.content : [...breaks, ...data.content], 'id'));
    };

    const fetchBreaks = (page = 0): Promise<PageableResponse<BreakModel>> => {
        if (!businessId) {
            return Promise.reject(new Error('No business ID!'));
        }
        return businessEndpoint.getBreaks(businessId, {
            page,
            size: 100,
            staffIds: staffId ? [staffId] : undefined,
        });
    };

    const {
        isPreviousData,
        isLoading,
        data: breaksResponse,
        refetch,
    } = useQuery(['breaks', page, staffId], () => fetchBreaks(page), {
        keepPreviousData: true,
        enabled: values.recentBreaks.active,
        onError,
        onSuccess,
    });

    useEffect(() => {
        setBreaks([]);
        setPage(0);
    }, [staffId]);

    const handleOnEndReached = useEvent(() => {
        if (!isPreviousData && !breaksResponse?.last) {
            setPage((old) => old + 1);
        }
    });

    const refreshControl = useRefreshControl(() => refetch());

    const handleBreakDelete = useEvent(async (id) => {
        try {
            await breaksEndpoint.destroy(id);
            // refetch breaks for either user or business
            refetch();

            LayoutAnimation.easeInEaseOut();
        } catch (e) {
            notifications.error(t('somethingWentWrongError', { error: 'breakCouldNotBeDeleted' }));
        }
    });

    return (
        <VStack flex>
            <VStack flex px={2} bgColor={'backgroundSecondary'}>
                {isLoading && <Loader p={2} />}
                <FlatList
                    refreshControl={refreshControl}
                    disableScrollViewPanResponder
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingTop: 16 }}
                    data={breaks}
                    initialNumToRender={10}
                    maxToRenderPerBatch={10}
                    renderItem={({ item }): ReactElement => (
                        <RecentBreak key={item.id} breakItem={item} onDelete={handleBreakDelete} />
                    )}
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={(item): string => String(item.id)}
                    onEndReachedThreshold={0.8}
                    onEndReached={handleOnEndReached}
                    ListEmptyComponent={<EmptyStateCard title={t('noBreaksYet')} source={emptyAppointments} />}
                />
            </VStack>
            <VStack px={2} pt={2}>
                <Button
                    variant={'subtle'}
                    size={'large'}
                    color={'accent'}
                    startIcon={<MaterialIcons name={'add'} />}
                    label={t('addNewBreak')}
                    onPress={handleNewBreakPress}
                />
            </VStack>
        </VStack>
    );
}
