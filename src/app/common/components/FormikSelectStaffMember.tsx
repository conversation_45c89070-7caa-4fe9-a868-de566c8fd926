import { useFormikContext } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Avatar } from '~/components/ui/Avatar';
import { MenuItem } from '~/components/ui/MenuItem';
import { Select, SelectProps } from '~/components/ui/Select';
import { useUserBusiness } from '~/hooks/useUserBusiness';

interface Props extends Omit<SelectProps, 'value' | 'children'> {
    name: string;
}

export function FormikSelectStaffMember({ name, onChange, ...rest }: Props) {
    const { t } = useTranslation();
    const formik = useFormikContext();
    const { value } = formik.getFieldMeta<string | null>(name);

    const currentBusiness = useUserBusiness();
    const members = currentBusiness?.data?.staffMembers;

    const handleChange = useCallback<Exclude<Props['onChange'], undefined>>(
        (item, value, data) => {
            formik.setFieldValue(name, value);
            onChange?.(item, value, data);
        },
        [formik, name, onChange],
    );

    return (
        <Select value={value} onChange={handleChange} {...rest}>
            {members && members.length > 0 ? (
                members.map((member) => (
                    <MenuItem
                        key={member.uid}
                        value={member.uid}
                        avatar={<Avatar size={32} source={member.photoURL} name={member.displayName} />}
                    >
                        {member.displayName}
                    </MenuItem>
                ))
            ) : (
                <MenuItem value={null}>{t('noStaffMembersFound')}</MenuItem>
            )}
        </Select>
    );
}
