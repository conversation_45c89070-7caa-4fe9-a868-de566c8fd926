import * as React from 'react';
import Svg, { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Path, Rect } from 'react-native-svg';

export const BarberIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <G stroke="#2F80FB" strokeWidth={1.714} clipPath="url(#a)">
            <Circle cx={12.133} cy={3.8} r={2.617} />
            <Path d="M9.06 6.833h6.147v14.112a2.198 2.198 0 0 1-2.199 2.198h-1.75a2.198 2.198 0 0 1-2.199-2.198V6.833ZM8.773 10.897l6.718-2.926M8.773 14.705l6.718-2.927M8.773 18.513l6.718-2.927" />
            <Path strokeLinecap="round" d="M7.095 19.677H17.17M7.095 6.628H17.17" />
        </G>
        <Defs>
            <ClipPath id="a">
                <Rect width={24} height={24} x={0.133} fill="#fff" rx={3.429} />
            </ClipPath>
        </Defs>
    </Svg>
);

export const BeautyIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Path
            fill="#2F80FB"
            d="M19.458 7.675a.508.508 0 0 1-.287-.075.566.566 0 0 1-.188-.225l-.7-1.65-1.775-.775a.605.605 0 0 1-.237-.213.531.531 0 0 1-.063-.262c0-.1.021-.192.063-.275a.446.446 0 0 1 .237-.2l1.775-.75.7-1.575c.05-.1.113-.175.188-.225a.508.508 0 0 1 .287-.075c.1 0 .192.025.275.075a.53.53 0 0 1 .2.225l.7 1.575 1.775.75c.************.225.2a.524.524 0 0 1 0 .537.794.794 0 0 1-.225.213l-1.775.775-.7 1.65a.53.53 0 0 1-.2.225.524.524 0 0 1-.275.075Zm0 14.9a.524.524 0 0 1-.275-.075.53.53 0 0 1-.2-.225l-.7-1.575-1.75-.75a.605.605 0 0 1-.237-.212.531.531 0 0 1-.063-.263c0-.1.021-.192.063-.275a.446.446 0 0 1 .237-.2l1.75-.75.7-1.675c.05-.1.113-.175.188-.225a.508.508 0 0 1 .287-.075c.1 0 .192.025.275.075a.53.53 0 0 1 .2.225l.7 1.675 1.75.75c.************.225.2a.524.524 0 0 1 0 .538.794.794 0 0 1-.225.212l-1.75.75-.7 1.575a.55.55 0 0 1-.475.3Zm-10.925-4.4c-.183 0-.358-.05-.525-.15a.961.961 0 0 1-.375-.4L6.108 14.35l-3.3-1.475c-.183-.1-.325-.23-.425-.387a.942.942 0 0 1-.15-.513c0-.183.05-.354.15-.512.1-.159.242-.288.425-.388l3.3-1.475 1.525-3.25c.084-.183.209-.33.375-.438a.949.949 0 0 1 .525-.162c.167 0 .334.054.5.162.167.109.3.246.4.413l1.55 3.275 3.25 1.475c.2.1.35.23.45.388a.942.942 0 0 1 0 1.025c-.1.158-.25.287-.45.387l-3.25 1.475-1.55 3.275c-.1.183-.233.32-.4.413a1.03 1.03 0 0 1-.5.137Zm0-2.725 1.2-2.4 2.45-1.075-2.45-1.075-1.2-2.4-1.175 2.4-2.475 1.075 2.475 1.075 1.175 2.4Z"
        />
    </Svg>
);

export const MassageAndSpaIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Path
            fill="#2F80FB"
            d="M11.308 22.25a12.206 12.206 0 0 1-3.575-1.288 10.834 10.834 0 0 1-3.05-2.475C3.8 17.463 3.096 16.233 2.571 14.8c-.525-1.433-.788-3.075-.788-4.925v-.45a.86.86 0 0 1 .25-.613.773.773 0 0 1 .575-.262h.45c.817 0 1.713.162 2.688.487.975.325 1.829.73 2.562 1.213.15-1.317.509-2.675 1.075-4.075a19.57 19.57 0 0 1 2.025-3.8c.2-.283.442-.425.725-.425.284 0 .517.142.7.425a20.064 20.064 0 0 1 2.038 3.8c.575 1.4.937 2.758 1.087 4.075a14.34 14.34 0 0 1 2.6-1.2c.95-.333 1.867-.5 2.75-.5h.375a.79.79 0 0 1 .575.25c.167.167.25.35.25.55v.4c0 1.883-.262 3.546-.787 4.988-.525 1.441-1.23 2.679-2.113 3.712a10.737 10.737 0 0 1-3.05 2.488 12.206 12.206 0 0 1-3.575 1.287c-.233.05-.512.075-.837.075-.325 0-.605-.017-.838-.05Zm.875-1.925c-.216-2.983-1.117-5.246-2.7-6.787C7.9 11.995 6 11.017 3.783 10.6c-.033 0-.033 0 0 0 .034 0 .034 0 0 0 .234 3.033 1.154 5.33 2.763 6.888 1.608 1.558 3.487 2.504 5.637 2.837 0 .017-.008.02-.025.012-.016-.008-.008-.012.025-.012Zm-2-8.925c.35.283.704.654 1.063 1.112.358.459.654.88.887 1.263.234-.433.525-.854.875-1.263.35-.408.709-.779 1.075-1.112a7.983 7.983 0 0 0-.5-3.175A32.089 32.089 0 0 0 12.108 4.9c-.017-.033-.017-.038 0-.013s.017.03 0 .013a29.089 29.089 0 0 0-1.437 3.287 8.285 8.285 0 0 0-.488 3.213Zm3.05 4.325c.2.633.383 1.263.55 1.888.167.624.292 1.345.375 2.162a10.584 10.584 0 0 0 2.15-1.112 8.27 8.27 0 0 0 1.938-1.813c.575-.733 1.062-1.613 1.462-2.638.4-1.024.65-2.229.75-3.612.017-.017.021-.012.013.013-.009.024-.013.02-.013-.013-1.633.3-3.083.896-4.35 1.787-1.267.892-2.225 2.005-2.875 3.338Z"
        />
    </Svg>
);

export const DentistryIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Path
            fill="#2F80FB"
            d="M16.527 3.043c1.025 0 1.896.36 2.613 1.077.718.717 1.076 1.588 1.076 2.613 0 .161-.01.377-.032.648-.022.27-.055.589-.1.955l-1.207 8.85c-.088.63-.373 1.124-.857 1.483-.483.358-1.003.538-1.559.538-.337 0-.662-.07-.977-.209a1.898 1.898 0 0 1-.802-.692l-2.35-3.426c-.029 0-.058-.014-.087-.044-.03-.029-.073-.043-.132-.043l-.22.131-2.284 3.317c-.22.336-.494.581-.823.735-.33.154-.67.23-1.021.23-.557 0-1.073-.179-1.549-.537-.475-.359-.75-.86-.823-1.505L4.207 8.336a9.356 9.356 0 0 1-.11-.955 12.63 12.63 0 0 1-.022-.648c0-1.025.355-1.896 1.065-2.613a3.496 3.496 0 0 1 2.58-1.077c.557 0 1.004.07 1.34.21.337.138.652.296.945.471.278.161.574.308.89.44.314.131.728.197 1.24.197s.926-.066 1.24-.198a7.74 7.74 0 0 0 .89-.439c.293-.175.608-.333.944-.472.337-.139.776-.209 1.318-.209Zm0 2.087c-.22 0-.417.029-.593.088a2.663 2.663 0 0 0-.549.263 9.948 9.948 0 0 1-1.482.703c-.476.176-1.065.263-1.768.263-.703 0-1.288-.087-1.757-.263-.469-.176-.959-.41-1.471-.703a2.567 2.567 0 0 0-.56-.263 1.973 1.973 0 0 0-.604-.088c-.44 0-.82.157-1.142.472a1.453 1.453 0 0 0-.461 1.13c.014.177.025.37.032.583.008.212.026.435.055.67l1.142 8.191c.015.161.08.282.198.363.117.08.234.12.351.12a.424.424 0 0 0 .22-.066c.073-.044.146-.102.22-.175l1.822-2.68c.22-.336.505-.589.857-.757a2.511 2.511 0 0 1 1.098-.253 2.358 2.358 0 0 1 1.954 1.032l1.823 2.68c.**************.**************.157.055.23.055a.56.56 0 0 0 .33-.132.555.555 0 0 0 .22-.329l1.141-8.235c.03-.293.052-.546.066-.758.015-.212.022-.377.022-.494 0-.44-.157-.816-.472-1.131a1.543 1.543 0 0 0-1.13-.472Z"
        />
    </Svg>
);

export const OphthalmologyIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <G clipPath="url(#a)">
            <Path
                fill="#2F80FB"
                d="M12.132 15.75c1.184 0 2.188-.413 3.013-1.238.825-.825 1.237-1.829 1.237-3.012 0-1.184-.412-2.188-1.237-3.013-.825-.825-1.83-1.237-3.013-1.237-1.183 0-2.187.412-3.012 1.237-.825.825-1.238 1.83-1.238 3.013 0 1.183.413 2.187 1.238 3.012.825.825 1.829 1.238 3.012 1.238Zm0-1.85a2.315 2.315 0 0 1-1.7-.7 2.315 2.315 0 0 1-.7-1.7c0-.667.234-1.233.7-1.7a2.315 2.315 0 0 1 1.7-.7c.667 0 1.234.233 1.7.7.467.466.7 1.033.7 1.7 0 .666-.233 1.233-.7 1.7-.466.466-1.033.7-1.7.7Zm0 5.675c-2.4 0-4.583-.659-6.55-1.975-1.966-1.317-3.5-3.042-4.6-5.175a1.416 1.416 0 0 1-.15-.438 2.616 2.616 0 0 1-.05-.487c0-.167.017-.334.05-.5.034-.167.084-.317.15-.45a13.699 13.699 0 0 1 4.6-5.15c1.967-1.317 4.15-1.975 6.55-1.975 2.4 0 4.584.658 6.55 1.975a13.699 13.699 0 0 1 4.6 5.15c.067.133.117.283.*************.05.333.05.5 0 .15-.016.312-.05.487-.033.175-.083.321-.15.438-1.1 2.133-2.633 3.858-4.6 5.175-1.966 1.316-4.15 1.975-6.55 1.975Zm0-2.075c2 0 3.842-.546 5.525-1.638a10.655 10.655 0 0 0 3.85-4.362 10.685 10.685 0 0 0-3.837-4.363A9.933 9.933 0 0 0 12.132 5.5c-2 0-3.841.546-5.525 1.637A10.835 10.835 0 0 0 2.732 11.5c.9 1.816 2.188 3.27 3.863 4.362a9.932 9.932 0 0 0 5.537 1.638Z"
            />
        </G>
        <Defs>
            <ClipPath id="a">
                <Path fill="#fff" d="M.133 0h24v24h-24z" />
            </ClipPath>
        </Defs>
    </Svg>
);

export const PsychologistIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M12.133 8c-.55 0-1.021-.196-1.413-.588A1.926 1.926 0 0 1 10.133 6c0-.55.196-1.021.587-1.413A1.926 1.926 0 0 1 12.133 4c.55 0 1.02.196 1.412.587.392.392.588.863.588 1.413s-.196 1.02-.588 1.412A1.926 1.926 0 0 1 12.133 8Zm-5.2 12c-.5 0-.925-.175-1.275-.525a1.736 1.736 0 0 1-.525-1.275c0-.35.1-.68.3-.988.2-.308.466-.529.8-.662l3.9-1.55v-2.25c-.8.916-1.7 1.646-2.7 2.187-1 .542-2.084.88-3.25 1.013a.87.87 0 0 1-.738-.25 1.027 1.027 0 0 1-.312-.775.91.91 0 0 1 .262-.65c.175-.184.396-.292.663-.325a6.702 6.702 0 0 0 2.562-.85 8.074 8.074 0 0 0 2.113-1.8l1.35-1.6c.2-.234.433-.409.7-.525.267-.117.55-.175.85-.175h1c.3 0 .583.058.85.175.266.116.5.291.7.525l1.35 1.6a8.073 8.073 0 0 0 2.112 1.8c.775.45 1.63.733 2.563.85.267.033.487.146.662.337a.953.953 0 0 1 .263.663c0 .3-.104.55-.313.75a.87.87 0 0 1-.737.25 8.874 8.874 0 0 1-3.25-1.013c-1-.541-1.9-1.27-2.7-2.187V15l3.9 1.55c.333.133.6.354.8.662.2.309.3.638.3.988 0 .5-.175.925-.525 1.275-.35.35-.775.525-1.275.525h-7.2v-.5c0-.434.142-.792.425-1.075.283-.284.642-.425 1.075-.425h3a.49.49 0 0 0 .362-.138.49.49 0 0 0 .138-.362.49.49 0 0 0-.138-.363.49.49 0 0 0-.362-.137h-3c-.7 0-1.292.241-1.775.725-.484.483-.725 1.075-.725 1.775v.5h-2.2Z"
            />
        </G>
    </Svg>
);

export const PetGroomingIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M4.982 22v-9.875h2V20h7v-4.825l2.625-2.625a5.367 5.367 0 0 0 1.125-1.625c.267-.6.4-1.241.4-1.925 0-.666-.138-1.3-.413-1.9a5.811 5.811 0 0 0-1.112-1.625l-.625-.65L12.807 8h-4L7.732 9.075l-1.425-1.4L7.982 6h4l4-4 2.05 2.05a7.3 7.3 0 0 1 1.55 2.263c.366.841.55 1.737.55 2.687 0 .95-.184 1.846-.55 2.688a7.3 7.3 0 0 1-1.55 2.262L15.982 16v6h-11Zm4.925-4.675-5.2-5.2a1.912 1.912 0 0 1-.425-.65c-.1-.25-.15-.508-.15-.775a1.975 1.975 0 0 1 .575-1.4l2.1-2.125 3.1 3.075c.466.467.829 1.004 1.087 1.613.259.608.388 1.246.388 1.912a4.972 4.972 0 0 1-1.475 3.55Z"
            />
        </G>
    </Svg>
);

export const MakeupIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Path
            stroke="#2F80FB"
            strokeLinecap="round"
            strokeWidth={0.857}
            d="M12.596 4.996 8.69 7.58M14.93 6.084 9.334 9.667M15.494 8.178l-3.542 2.282"
        />
        <Path
            stroke="#2F80FB"
            strokeWidth={1.714}
            d="M18.877 7.876c0 1.283-.677 2.504-1.89 3.432-1.213.929-2.927 1.527-4.855 1.527-1.927 0-3.64-.598-4.854-1.527-1.213-.928-1.89-2.149-1.89-3.432 0-1.284.677-2.505 1.89-3.433 1.213-.928 2.927-1.527 4.854-1.527 1.928 0 3.642.599 4.855 1.527 1.213.928 1.89 2.15 1.89 3.433ZM19.66 15.632c0 .155-.073.387-.39.689-.32.303-.828.615-1.527.896-1.393.559-3.376.924-5.61.924-2.233 0-4.216-.365-5.609-.924-.699-.28-1.207-.593-1.526-.896-.318-.302-.392-.534-.392-.689 0-.154.074-.386.392-.688.32-.303.827-.615 1.526-.896 1.393-.559 3.376-.924 5.61-.924 2.233 0 4.216.365 5.609.924.699.28 1.207.593 1.526.896.318.301.392.534.392.688Z"
        />
        <Mask id="a" fill="#fff">
            <Path d="M20.518 18.864c0 .893-.883 1.749-2.456 2.38-1.572.63-3.705.986-5.929.986-2.223 0-4.356-.355-5.928-.986-1.573-.631-2.456-1.487-2.456-2.38h16.769Z" />
        </Mask>
        <Path
            stroke="#2F80FB"
            strokeWidth={3.429}
            d="M20.518 18.864c0 .893-.883 1.749-2.456 2.38-1.572.63-3.705.986-5.929.986-2.223 0-4.356-.355-5.928-.986-1.573-.631-2.456-1.487-2.456-2.38h16.769Z"
            mask="url(#a)"
        />
        <Path
            fill="#2F80FB"
            d="M3.757 15.454v3.388L4.693 20l5.75 1.694 5.973-.647 4.123-2.206v-3.388l-1.025.98-4.77 2.073-7.533-.891-3.454-2.162Z"
        />
        <Ellipse cx={12.132} cy={15.803} fill="#2F80FB" rx={4.625} ry={0.816} />
    </Svg>
);

export const PersonalTrainerIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="m20.783 9.375-1.4-1.4.75-.775-3.2-3.2-.775.75-1.425-1.425.75-.775c.383-.384.858-.571 1.425-.563.567.009 1.042.204 1.425.588L21.558 5.8c.384.383.575.854.575 1.412a1.92 1.92 0 0 1-.575 1.413l-.775.75Zm-12 12.025a1.92 1.92 0 0 1-1.412.575 1.92 1.92 0 0 1-1.413-.575l-3.225-3.225a1.92 1.92 0 0 1-.575-1.413c0-.558.192-1.029.575-1.412l.75-.75 1.425 1.425-.775.75L7.358 20l.75-.775 1.425 1.425-.75.75Zm9.925-8.4 1.425-1.425L12.558 4l-1.425 1.425L18.708 13Zm-7 7 1.425-1.45L5.583 11l-1.45 1.425L11.708 20Zm-.15-5.85 2.75-2.725-1.6-1.6-2.725 2.75 1.575 1.575Zm1.575 7.25c-.383.383-.858.575-1.425.575-.566 0-1.042-.192-1.425-.575l-7.55-7.55c-.383-.383-.575-.858-.575-1.425 0-.567.192-1.042.575-1.425l1.425-1.425A1.92 1.92 0 0 1 5.571 9c.558 0 1.029.191 1.412.575l1.575 1.575 2.75-2.75-1.575-1.55c-.383-.384-.575-.859-.575-1.425 0-.567.192-1.042.575-1.425l1.425-1.425A1.92 1.92 0 0 1 12.571 2c.558 0 1.029.191 1.412.575l7.575 7.575c.384.383.575.854.575 1.412a1.92 1.92 0 0 1-.575 1.413L20.133 14.4c-.383.383-.858.575-1.425.575-.566 0-1.041-.192-1.425-.575l-1.55-1.575-2.75 2.75 1.575 1.575c.383.383.575.854.575 1.412a1.92 1.92 0 0 1-.575 1.413L13.133 21.4Z"
            />
        </G>
    </Svg>
);

export const DermatologyIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M3.134 21a.968.968 0 0 1-.713-.287.968.968 0 0 1-.287-.713v-8c0-.55.196-1.02.587-1.412A1.926 1.926 0 0 1 4.134 10h4c.133 0 .262.025.387.075a.956.956 0 0 1 .538.538c.05.125.075.254.075.387v1c0 .833.291 1.542.875 2.125a2.893 2.893 0 0 0 2.125.875c.833 0 1.541-.292 2.125-.875A2.893 2.893 0 0 0 15.134 12v-1c0-.133.025-.262.075-.387a.955.955 0 0 1 .537-.538c.125-.05.254-.075.388-.075h4c.55 0 1.02.196 1.412.588.392.391.588.862.588 1.412v8c0 .283-.096.52-.288.713a.968.968 0 0 1-.712.287h-18Zm1-2h16v-7h-3c0 1.383-.488 2.563-1.463 3.538-.975.975-2.154 1.462-3.537 1.462s-2.563-.487-3.538-1.462c-.975-.975-1.462-2.155-1.462-3.538h-3v7Zm8-6a.968.968 0 0 1-.713-.287.968.968 0 0 1-.287-.713c0-1.817.208-3.608.625-5.375a7.847 7.847 0 0 1 2.725-4.375.948.948 0 0 1 .725-.237.978.978 0 0 1 .675.362.948.948 0 0 1 .237.725.978.978 0 0 1-.362.675c-1.167.983-1.896 2.217-2.188 3.7A23.395 23.395 0 0 0 13.134 12c0 .283-.096.52-.288.713a.968.968 0 0 1-.712.287Zm-6.25 2.5a.729.729 0 0 0 .75-.75.728.728 0 0 0-.75-.75.728.728 0 0 0-.75.75.729.729 0 0 0 .75.75Zm1 2.5a.729.729 0 0 0 .75-.75.728.728 0 0 0-.75-.75.728.728 0 0 0-.75.75.729.729 0 0 0 .75.75Zm11.5-2.5a.729.729 0 0 0 .75-.75.728.728 0 0 0-.75-.75.728.728 0 0 0-.75.75.729.729 0 0 0 .75.75Z"
            />
        </G>
    </Svg>
);

export const ManicureIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M6.134 21c-.75 0-1.492-.183-2.225-.55-.734-.366-1.325-.85-1.775-1.45.433 0 .875-.17 1.325-.512.45-.342.675-.838.675-1.488 0-.833.291-1.541.875-2.125A2.893 2.893 0 0 1 7.134 14c.833 0 1.541.292 2.125.875.583.584.875 1.292.875 2.125 0 1.1-.392 2.042-1.175 2.825C8.175 20.61 7.234 21 6.134 21Zm0-2c.55 0 1.02-.196 1.412-.587.392-.392.588-.863.588-1.413a.968.968 0 0 0-.288-.712.967.967 0 0 0-.712-.288.967.967 0 0 0-.713.288.968.968 0 0 0-.287.712c0 .384-.046.733-.138 1.05a4.725 4.725 0 0 1-.362.9.67.67 0 0 0 .25.05h.25Zm5.75-4-2.75-2.75 8.95-8.95a.977.977 0 0 1 .687-.287.93.93 0 0 1 .713.287l1.35 1.35c.2.2.3.434.3.7 0 .267-.1.5-.3.7L11.884 15Z"
            />
        </G>
    </Svg>
);

export const SurgeryIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="m11.132 21 2-2h8c.283 0 .52.096.712.288.192.191.288.429.288.712s-.096.52-.288.712a.967.967 0 0 1-.712.288h-10Zm1.4-5.65-3.75-3.75 8.3-8.3c.2-.2.437-.3.712-.3.275 0 .513.1.713.3l2.325 2.325c.2.2.3.438.3.713 0 .274-.1.512-.3.712l-8.3 8.3Zm0-2.85 6.2-6.175-.925-.925-6.175 6.2.9.9ZM5.207 21c-.767 0-1.504-.15-2.213-.45a5.813 5.813 0 0 1-1.862-1.25l6.625-6.6 2.6 2.6c.233.233.417.5.55.8a2.4 2.4 0 0 1 0 1.912c-.133.309-.317.58-.55.813l-.475.475a5.813 5.813 0 0 1-1.863 1.25c-.708.3-1.446.45-2.212.45h-.6Zm0-2h.6c.5 0 .983-.096 1.45-.288a3.704 3.704 0 0 0 1.225-.812l.475-.475a.48.48 0 0 0 0-.7l-1.2-1.2-3.4 3.375a3.85 3.85 0 0 0 .85.1Z"
            />
        </G>
    </Svg>
);

export const PlasticSurgeryIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="m11.132 21 2-2h8c.283 0 .52.096.712.288.192.191.288.429.288.712s-.096.52-.288.712a.967.967 0 0 1-.712.288h-10Zm1.4-5.65-3.75-3.75 8.3-8.3c.2-.2.437-.3.712-.3.275 0 .513.1.713.3l2.325 2.325c.2.2.3.438.3.713 0 .274-.1.512-.3.712l-8.3 8.3Zm0-2.85 6.2-6.175-.925-.925-6.175 6.2.9.9ZM5.207 21c-.767 0-1.504-.15-2.213-.45a5.813 5.813 0 0 1-1.862-1.25l6.625-6.6 2.6 2.6c.233.233.417.5.55.8a2.4 2.4 0 0 1 0 1.912c-.133.309-.317.58-.55.813l-.475.475a5.813 5.813 0 0 1-1.863 1.25c-.708.3-1.446.45-2.212.45h-.6Zm0-2h.6c.5 0 .983-.096 1.45-.288a3.704 3.704 0 0 0 1.225-.812l.475-.475a.48.48 0 0 0 0-.7l-1.2-1.2-3.4 3.375a3.85 3.85 0 0 0 .85.1Z"
            />
        </G>
    </Svg>
);

export const NutritionDieateticsIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M12.134 21c-1.95 0-3.604-.68-4.963-2.038C5.813 17.604 5.134 15.95 5.134 14c0-1.567.462-2.97 1.387-4.212.925-1.242 2.138-2.08 3.638-2.513a4.754 4.754 0 0 1-.975-.362 3.25 3.25 0 0 1-.85-.613A3.825 3.825 0 0 1 7.27 4.338a7.41 7.41 0 0 1-.112-2.313 7.41 7.41 0 0 1 2.312.113A3.825 3.825 0 0 1 11.434 3.2c.383.383.662.817.837 1.3.175.483.288.992.338 1.525A8.511 8.511 0 0 1 14.434 3.3a.948.948 0 0 1 .7-.275c.283 0 .516.092.7.275a.948.948 0 0 1 .275.7.948.948 0 0 1-.275.7c-.367.367-.692.77-.975 1.213a4.976 4.976 0 0 0-.625 1.412c1.466.467 2.65 1.313 3.55 2.538A6.816 6.816 0 0 1 19.134 14c0 1.95-.68 3.604-2.038 4.962C15.738 20.322 14.084 21 12.134 21Zm0-2c1.383 0 2.562-.488 3.537-1.462.975-.976 1.463-2.155 1.463-3.538s-.488-2.563-1.463-3.537C14.696 9.488 13.517 9 12.134 9s-2.563.488-3.538 1.463c-.975.975-1.462 2.154-1.462 3.537s.487 2.563 1.462 3.538c.975.974 2.155 1.462 3.538 1.462Z"
            />
        </G>
    </Svg>
);

export const BodyRemodelingIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M9.133 20v-4a39.534 39.534 0 0 1-2.5-.275 31.588 31.588 0 0 1-2.45-.45 1.087 1.087 0 0 1-.675-.475.914.914 0 0 1-.125-.8.988.988 0 0 1 .512-.625c.259-.133.53-.167.813-.1 1.216.283 2.446.475 3.687.575a46.539 46.539 0 0 0 7.475 0c1.242-.1 2.471-.292 3.688-.575.3-.067.575-.033.825.1s.416.342.5.625a.893.893 0 0 1-.138.8c-.175.25-.404.408-.687.475-.817.183-1.634.333-2.45.45-.817.117-1.642.208-2.475.275v4c0 .283-.096.52-.288.712a.968.968 0 0 1-.712.288h-4a.967.967 0 0 1-.713-.288.968.968 0 0 1-.287-.712Zm3-7c-.567 0-1.042-.192-1.425-.575-.383-.383-.575-.858-.575-1.425 0-.55.192-1.02.575-1.412A1.914 1.914 0 0 1 12.133 9c.55 0 1.02.196 1.412.588.392.391.588.862.588 1.412 0 .567-.196 1.042-.588 1.425a1.947 1.947 0 0 1-1.412.575Zm-7.5-3c-.434 0-.792-.142-1.075-.425-.284-.283-.425-.642-.425-1.075 0-.417.141-.77.425-1.063C3.84 7.146 4.199 7 4.633 7c.416 0 .77.146 1.062.438.292.291.438.645.438 1.062 0 .433-.146.792-.438 1.075A1.468 1.468 0 0 1 4.633 10Zm15 0c-.433 0-.792-.142-1.075-.425-.284-.283-.425-.642-.425-1.075 0-.417.142-.77.425-1.063C18.84 7.146 19.2 7 19.633 7c.416 0 .77.146 1.062.438.292.291.438.645.438 1.062 0 .433-.146.792-.438 1.075a1.468 1.468 0 0 1-1.062.425ZM7.383 6.25c-.434 0-.792-.142-1.075-.425-.284-.283-.425-.642-.425-1.075 0-.417.141-.77.425-1.063.283-.291.641-.437 1.075-.437.416 0 .77.146 1.062.438.292.291.438.645.438 1.062 0 .433-.146.792-.438 1.075a1.468 1.468 0 0 1-1.062.425Zm9.5 0c-.433 0-.792-.142-1.075-.425-.284-.283-.425-.642-.425-1.075 0-.417.142-.77.425-1.063.283-.291.642-.437 1.075-.437.416 0 .77.146 1.062.438.292.291.438.645.438 1.062 0 .433-.146.792-.438 1.075a1.468 1.468 0 0 1-1.062.425ZM12.133 5c-.433 0-.792-.142-1.075-.425-.284-.283-.425-.642-.425-1.075 0-.417.142-.77.425-1.063C11.34 2.147 11.7 2 12.133 2c.417 0 .77.146 1.062.438.292.291.438.645.438 1.062 0 .433-.146.792-.438 1.075A1.468 1.468 0 0 1 12.133 5Z"
            />
        </G>
    </Svg>
);

export const HairImplant = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Path
            stroke="#2F80FB"
            strokeLinecap="round"
            strokeWidth={1.714}
            d="M6.935 11.676c-.935.374-1.645 2.337.374 2.836.935 2.556 1.647 5.704 4.925 5.704 3.52 0 4.72-3.51 5.281-5.442 1.36-.262 1.832-2.563.463-3.098-.187-2.276.236-5.036-3.22-5.766"
        />
        <Path
            stroke="#2F80FB"
            strokeLinecap="round"
            strokeWidth={1.714}
            d="M13.554 8.22c.535.951 1.109 2.471 4.116 1.772M9.429 9.993c.176-.686.947-1.864 3.116-1.84 2.17.026 2.4-1.37 2.182-2.212-.146-.564-.654-2.026-2.805-1.527M6.904 4.279v4.135M8.97 6.346H4.837"
        />
        <Rect width={1.683} height={1.683} x={9.459} y={12.05} fill="#2F80FB" rx={0.841} />
        <Rect width={1.683} height={1.683} x={13.668} y={12.05} fill="#2F80FB" rx={0.841} />
    </Svg>
);

export const PodiatryIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M5.633 8a.968.968 0 0 1-.713-.287A.968.968 0 0 1 4.633 7c0-.283.096-.52.287-.713A.968.968 0 0 1 5.633 6c.283 0 .52.096.712.287.192.192.288.43.288.713s-.096.52-.288.713A.968.968 0 0 1 5.633 8Zm4.5 14c-1.1 0-2.042-.392-2.825-1.175C6.524 20.042 6.133 19.1 6.133 18v-6c0-1.667.583-3.083 1.75-4.25C9.049 6.583 10.466 6 12.133 6h1.825c1.15 0 2.133.388 2.95 1.162.816.776 1.225 1.73 1.225 2.863 0 .767-.204 1.47-.613 2.112a3.932 3.932 0 0 1-1.662 1.463c-.534.25-.954.62-1.263 1.113a2.977 2.977 0 0 0-.462 1.612V18c0 1.117-.388 2.063-1.163 2.837-.775.776-1.72 1.163-2.837 1.163Zm-2-16a.968.968 0 0 1-.713-.287A.968.968 0 0 1 7.133 5v-.5c0-.283.096-.52.287-.712a.968.968 0 0 1 .713-.288c.283 0 .52.096.712.288.192.191.288.429.288.712V5c0 .283-.096.52-.288.713A.968.968 0 0 1 8.133 6Zm2 14c.55 0 1.02-.196 1.412-.587.392-.392.588-.863.588-1.413v-1.675c0-.967.254-1.858.762-2.675a4.834 4.834 0 0 1 2.088-1.85c.35-.167.629-.413.837-.738A1.93 1.93 0 0 0 16.133 10c0-.583-.217-1.063-.65-1.438A2.257 2.257 0 0 0 13.958 8h-1.825c-1.1 0-2.042.392-2.825 1.175C8.524 9.958 8.133 10.9 8.133 12v6c0 .55.196 1.02.587 1.413.392.391.863.587 1.413.587Zm1-15a.968.968 0 0 1-.713-.287.967.967 0 0 1-.287-.713v-.5c0-.283.096-.52.287-.712a.968.968 0 0 1 .713-.288c.283 0 .52.096.712.288.192.191.288.429.288.712V4c0 .283-.096.52-.288.713a.968.968 0 0 1-.712.287Zm3 0a.968.968 0 0 1-.713-.287.967.967 0 0 1-.287-.713V3c0-.283.096-.52.287-.712A.968.968 0 0 1 14.133 2c.283 0 .52.096.712.288.192.191.288.429.288.712v1c0 .283-.096.52-.288.713a.968.968 0 0 1-.712.287Zm3.5 1c-.417 0-.771-.146-1.063-.438a1.446 1.446 0 0 1-.437-1.062v-1c0-.417.146-.77.437-1.063A1.447 1.447 0 0 1 17.633 2c.416 0 .77.146 1.062.438.292.291.438.645.438 1.062v1c0 .417-.146.77-.438 1.063A1.447 1.447 0 0 1 17.633 6Z"
            />
        </G>
    </Svg>
);

export const OrlIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M12.408 15.525c1.167 0 2.158-.375 2.975-1.125.817-.75 1.225-1.658 1.225-2.725 0-.95-.304-1.754-.913-2.413-.608-.658-1.345-.987-2.212-.987-.784 0-1.446.25-1.988.75a2.418 2.418 0 0 0-.812 1.85c0 .317.054.63.162.938.109.308.296.579.563.812.267.233.533.33.8.287.267-.041.483-.154.65-.337.166-.183.262-.404.287-.662.025-.259-.079-.496-.312-.713a.818.818 0 0 1-.1-.125.308.308 0 0 1-.05-.175c0-.183.075-.33.225-.438a.958.958 0 0 1 .575-.162c.333 0 .608.137.825.412.216.276.325.605.325.988 0 .517-.213.954-.638 1.313-.425.358-.945.537-1.562.537-.784 0-1.446-.317-1.988-.95-.541-.633-.812-1.408-.812-2.325a3.8 3.8 0 0 1 .112-.925c.075-.3.188-.583.338-.85.133-.25.2-.513.2-.787 0-.276-.1-.513-.3-.713a.946.946 0 0 0-.725-.287.755.755 0 0 0-.65.362 5.274 5.274 0 0 0-.75 1.5 5.583 5.583 0 0 0-.25 1.675c0 1.467.466 2.713 1.4 3.738.933 1.024 2.067 1.537 3.4 1.537ZM6.133 17.7a9.233 9.233 0 0 1-2.213-3.037A8.771 8.771 0 0 1 3.133 11c0-2.5.875-4.625 2.625-6.375S9.633 2 12.133 2c2.083 0 3.929.612 5.537 1.837 1.609 1.225 2.654 2.821 3.138 4.788l1.3 5.125a.947.947 0 0 1-.175.863.96.96 0 0 1-.8.387h-2v3c0 .55-.196 1.02-.588 1.413a1.926 1.926 0 0 1-1.412.587h-2v1c0 .283-.096.52-.288.712a.968.968 0 0 1-.712.288.968.968 0 0 1-.713-.288.968.968 0 0 1-.287-.712v-2c0-.283.096-.52.287-.712a.968.968 0 0 1 .713-.288h3v-4c0-.283.096-.52.287-.713a.968.968 0 0 1 .713-.287h1.7l-.95-3.875a6.533 6.533 0 0 0-2.45-3.7C15.183 4.475 13.749 4 12.133 4c-1.933 0-3.584.675-4.95 2.025-1.367 1.35-2.05 2.992-2.05 4.925 0 1 .204 1.95.612 2.85a7.5 7.5 0 0 0 1.738 2.4l.65.6V21c0 .283-.096.52-.288.712a.967.967 0 0 1-.712.288.967.967 0 0 1-.713-.288.968.968 0 0 1-.287-.712v-3.3Z"
            />
        </G>
    </Svg>
);

export const GinecologyIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M6.132 14h12v-3h-8V6h-2c-.55 0-1.021.196-1.413.588A1.926 1.926 0 0 0 6.132 8v6Zm6 6c.333 0 .667-.02 1-.063a8.443 8.443 0 0 0 1-.187V16h-4v3.75a8.45 8.45 0 0 0 1 .188 8.05 8.05 0 0 0 1 .062Zm0 2a9.758 9.758 0 0 1-3.325-.575 10.298 10.298 0 0 1-2.95-1.65.889.889 0 0 1-.363-.7c-.008-.283.104-.542.338-.775a.903.903 0 0 1 .662-.275c.259 0 .488.083.688.25.15.133.304.25.462.35.159.1.321.2.488.3V16h-2c-.55 0-1.021-.196-1.413-.588A1.926 1.926 0 0 1 4.132 14V8c0-1.1.392-2.042 1.175-2.825C6.09 4.392 7.032 4 8.132 4h2c.55 0 1.02.196 1.412.588.392.391.588.862.588 1.412v3h6c.55 0 1.02.196 1.412.588.392.391.588.862.588 1.412v3c0 .55-.196 1.02-.588 1.412a1.926 1.926 0 0 1-1.412.588h-2v2.925c.166-.1.329-.2.487-.3.159-.1.313-.217.463-.35.2-.167.433-.246.7-.237a.963.963 0 0 1 .675.287c.216.217.32.467.312.75a.888.888 0 0 1-.362.7c-.9.717-1.884 1.267-2.95 1.65a9.758 9.758 0 0 1-3.325.575Z"
            />
        </G>
    </Svg>
);

export const MentoringIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M11.832 21.3c-.316.317-.679.387-1.087.212-.409-.175-.613-.487-.613-.937V14H3.557c-.45 0-.762-.204-.937-.613-.175-.408-.104-.77.212-1.087l7.725-7.725a1.975 1.975 0 0 1 1.4-.575h6.175c.55 0 1.021.196 1.413.588.391.391.587.862.587 1.412v6.175a1.975 1.975 0 0 1-.575 1.4L11.832 21.3Zm4.3-12.3v5.175l2-2V6h-6.175l-2 2h5.175c.284 0 .521.096.713.287.191.192.287.43.287.713Zm-4 4v5.175l2-2V10H7.957l-2 2h5.175c.284 0 .521.096.713.287.191.192.287.43.287.713Z"
            />
        </G>
    </Svg>
);

export const CoachingIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M11.134 19c-1.667 0-3.084-.583-4.25-1.75-1.167-1.167-1.75-2.583-1.75-4.25a6.07 6.07 0 0 1 .1-1.1 1.982 1.982 0 0 1-.6.1c-.7 0-1.292-.242-1.775-.725-.484-.483-.725-1.075-.725-1.775s.229-1.292.687-1.775C3.28 7.242 3.86 7 4.56 7c.55 0 1.046.154 1.487.463.442.308.754.704.938 1.187.55-.5 1.179-.9 1.887-1.2.709-.3 1.463-.45 2.263-.45h10c.283 0 .52.096.712.287.192.192.288.43.288.713v2c0 .283-.096.52-.288.713a.968.968 0 0 1-.712.287h-4v2c0 1.667-.584 3.083-1.75 4.25C14.217 18.417 12.8 19 11.134 19Zm-6.5-8.5c.283 0 .52-.096.712-.287a.968.968 0 0 0 .288-.713.968.968 0 0 0-.288-.713.968.968 0 0 0-.712-.287.968.968 0 0 0-.713.287.968.968 0 0 0-.287.713c0 .283.096.52.287.713.192.191.43.287.713.287Zm6.5 6c.966 0 1.791-.342 2.475-1.025A3.372 3.372 0 0 0 14.634 13c0-.967-.342-1.792-1.025-2.475A3.372 3.372 0 0 0 11.134 9.5c-.967 0-1.792.342-2.475 1.025A3.372 3.372 0 0 0 7.634 13c0 .967.341 1.792 1.025 2.475a3.372 3.372 0 0 0 2.475 1.025Zm0-1.5c.55 0 1.02-.196 1.412-.588.392-.391.588-.862.588-1.412 0-.55-.196-1.02-.588-1.412A1.926 1.926 0 0 0 11.134 11c-.55 0-1.021.196-1.413.588A1.926 1.926 0 0 0 9.134 13c0 .55.196 1.02.587 1.412.392.392.863.588 1.413.588Z"
            />
        </G>
    </Svg>
);

export const ConsultancyIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M3.133 9a.968.968 0 0 1-.713-.287A.968.968 0 0 1 2.133 8V6c0-.55.196-1.02.587-1.412A1.926 1.926 0 0 1 4.133 4h16c.55 0 1.02.196 1.412.588.392.391.588.862.588 1.412v2c0 .283-.096.52-.288.713a.968.968 0 0 1-.712.287.968.968 0 0 1-.713-.287.967.967 0 0 1-.287-.713V6h-16v2c0 .283-.096.52-.288.713A.968.968 0 0 1 3.133 9Zm1 11c-.55 0-1.021-.196-1.413-.587A1.926 1.926 0 0 1 2.133 18v-2c0-.283.096-.52.287-.713A.967.967 0 0 1 3.133 15c.283 0 .52.096.712.287.192.192.288.43.288.713v2h16v-2c0-.283.096-.52.287-.713a.968.968 0 0 1 .713-.287c.283 0 .52.096.712.287.192.192.288.43.288.713v2c0 .55-.196 1.02-.588 1.413a1.926 1.926 0 0 1-1.412.587h-16Zm6-3c.183 0 .358-.046.525-.137a.863.863 0 0 0 .375-.413l3.1-6.2 1.1 2.2c.083.183.208.32.375.413.166.091.341.137.525.137h5c.283 0 .52-.096.712-.287a.968.968 0 0 0 .288-.713.968.968 0 0 0-.288-.713.968.968 0 0 0-.712-.287h-4.375l-1.725-3.45c-.183-.333-.483-.5-.9-.5-.417 0-.717.167-.9.5l-3.1 6.2-1.1-2.2a.863.863 0 0 0-.375-.413A1.074 1.074 0 0 0 8.133 11h-5a.967.967 0 0 0-.713.287.968.968 0 0 0-.287.713c0 .283.096.52.287.713.192.191.43.287.713.287h4.375l1.725 3.45c.083.183.208.32.375.413.166.091.341.137.525.137Z"
            />
        </G>
    </Svg>
);

export const PermanentHairRemovalIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Mask
            id="a"
            width={25}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: 'alpha',
            }}
        >
            <Path fill="#D9D9D9" d="M.133 0h24v24h-24z" />
        </Mask>
        <G mask="url(#a)">
            <Path
                fill="#2F80FB"
                d="M3.133 21V6c0-.833.291-1.542.875-2.125A2.893 2.893 0 0 1 6.133 3h4c.833 0 1.542.292 2.125.875s.875 1.292.875 2.125v15h-10Zm7-6h1v-5h-1v5Zm-5 4h6v-2h-1c-.55 0-1.021-.196-1.413-.587A1.926 1.926 0 0 1 8.133 15v-5c0-.55.196-1.02.587-1.412A1.926 1.926 0 0 1 10.133 8h1V6a.967.967 0 0 0-.288-.713.968.968 0 0 0-.712-.287h-4a.968.968 0 0 0-.713.287.968.968 0 0 0-.287.713v13Zm12.2-5.425c-.433 0-.858-.058-1.275-.175a8.328 8.328 0 0 1-1.225-.45l.625-1.9c.333.15.662.275.987.375.325.1.63.15.913.15.2 0 .4-.033.6-.1.2-.067.408-.167.625-.3.4-.283.8-.475 1.2-.575.4-.1.783-.15 1.15-.15.416 0 .846.054 1.287.163.442.108.855.254 1.238.437l-.625 1.9a31.1 31.1 0 0 0-1.063-.35c-.325-.1-.604-.15-.837-.15-.2 0-.421.038-.663.113a2.866 2.866 0 0 0-.762.387c-.35.233-.704.396-1.063.488a4.47 4.47 0 0 1-1.112.137Zm.025-3.9c-.434 0-.867-.058-1.3-.175a7.127 7.127 0 0 1-1.225-.45l.625-1.9c.433.183.8.317 1.1.4.3.083.567.125.8.125.2 0 .4-.03.6-.087.2-.059.408-.163.625-.313.417-.283.82-.475 1.212-.575.392-.1.771-.15 1.138-.15.416 0 .833.054 1.25.163.416.108.841.254 1.275.437l-.625 1.9c-.433-.15-.8-.27-1.1-.363a2.778 2.778 0 0 0-.8-.137c-.217 0-.438.033-.663.1-.225.067-.479.2-.762.4-.3.217-.638.375-1.013.475-.375.1-.754.15-1.137.15Zm0 7.8c-.434 0-.863-.058-1.288-.175a8.138 8.138 0 0 1-1.237-.45l.625-1.9c.367.167.708.296 1.025.388.316.091.608.137.875.137.2 0 .4-.03.6-.088.2-.058.408-.162.625-.312a3.705 3.705 0 0 1 1.225-.563 4.814 4.814 0 0 1 1.15-.162c.417 0 .841.058 1.275.175.433.117.841.258 1.225.425l-.625 1.9a27.11 27.11 0 0 0-1.113-.363 2.842 2.842 0 0 0-.787-.137c-.233 0-.471.037-.713.112-.241.076-.479.205-.712.388a3.27 3.27 0 0 1-.988.462 4.169 4.169 0 0 1-1.162.163Z"
            />
        </G>
    </Svg>
);

export const BookrIcon = (props) => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
        <Path
            fill="#2F80FB"
            d="m17.718 13.422-1.288-1.344a4.26 4.26 0 0 0-2.643-7.602H5.549v15.048h9.562a3.612 3.612 0 0 0 3.607-3.609c0-.965-.38-1.845-1-2.493Zm-9.78-6.556h5.849a1.874 1.874 0 0 1 .973 3.47l-.3-.313-.012-.013-.155-.162-.009-.009a3.083 3.083 0 0 0-2.353-.857 3.088 3.088 0 0 0-2.032.96l-.151.15-1.81 1.827V6.866Zm7.173 10.268H7.937v-1.82l1.582-1.597 1.624-1.638.473-.478a.704.704 0 0 1 .987-.063l.002.002.09.094.944.986h.001l.36.375.736.768 1.236 1.29.022.023c.209.219.336.515.336.839a1.22 1.22 0 0 1-1.22 1.219Z"
        />
    </Svg>
);

export const OtherIcon = (props) => <BookrIcon {...props} />;
