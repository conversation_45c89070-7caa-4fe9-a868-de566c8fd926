import { useFormikContext } from 'formik';
import React, { useCallback } from 'react';
import { MenuItem } from '~/components/ui/MenuItem';
import { Select, SelectProps } from '~/components/ui/Select';

interface Props extends Omit<SelectProps, 'value' | 'children'> {
    items: Array<{ label: string; value: any }>;
    name: string;
}

export function FormikSelect({ name, items, onChange, ...rest }: Props) {
    const formik = useFormikContext();
    const { value } = formik.getFieldMeta<string | null>(name);

    const handleChange = useCallback<Exclude<Props['onChange'], undefined>>(
        (item, value, data) => {
            formik.setFieldValue(name, value);
            onChange?.(item, value, data);
        },
        [formik, name, onChange],
    );

    return (
        <Select value={value} onChange={handleChange} {...rest}>
            {items.map((item) => (
                <MenuItem key={String(item.value)} value={item.value}>
                    {item.label}
                </MenuItem>
            ))}
        </Select>
    );
}
