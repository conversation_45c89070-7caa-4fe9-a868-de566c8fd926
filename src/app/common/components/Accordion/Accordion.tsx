import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactNode, useState } from 'react';
import { Pressable } from 'react-native';
import { HStack } from '~/components/ui/Grid';
import { Paper, PaperProps } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography/Typography';

interface AccordionProps extends PaperProps {
    children: ReactNode;
    openedDefault?: boolean;
    title: string;
}

export function Accordion({ children, title, openedDefault, ...props }: AccordionProps): JSX.Element {
    const [isOpen, setIsOpen] = useState(openedDefault || false);

    const handlePress = (): void => {
        setIsOpen(!isOpen);
    };

    return (
        <Pressable onPress={handlePress}>
            <Paper bgColor={'backgroundPrimary'} p={2} flexWrap={'nowrap'} {...props}>
                <HStack alignItems={'center'} justifyContent={'space-between'} mb={isOpen ? 1 : 0}>
                    <Typography variant={'subhead'} fontWeight={700} lineHeight={22}>
                        {title}
                    </Typography>
                    <MaterialIcons size={24} name={isOpen ? 'keyboard-arrow-up' : 'keyboard-arrow-down'} />
                </HStack>
                {isOpen && children}
            </Paper>
        </Pressable>
    );
}
