/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useFormikContext } from 'formik';
import { PropsWithChildren, ReactElement, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { GooglePlaceData, GooglePlaceDetail, GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import MapView, { MapPressEvent, Region } from 'react-native-maps';
import { useTheme } from 'styled-components/native';
import { Box } from '~/components/ui/Box';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { VStack } from '~/components/ui/Grid';
import { makeStyles } from '~/components/ui/makeStyles';
import { useDebouncedEvent } from '~/hooks/useDebouncedEvent';
import { useDeviceLocation } from '~/hooks/useDeviceLocation';
import { useEvent } from '~/hooks/useEvent';
import { useLRUCache } from '~/hooks/useLRUCache';
import { useLayout } from '~/hooks/useLayout';
import { useNotifications } from '~/hooks/useNotifications';
import { useSafeState } from '~/hooks/useSafeState';
import { useWatchValue } from '~/hooks/useWatch';
import { googleMapsEndpoint, GoogleMapsKey } from '~/lib/map';
import { computeCoordsWithDelta } from '~/lib/map/utils';

const useStyles = makeStyles(({ theme }) => ({
    container: {},
    listView: {
        borderRadius: theme.mixins.spacingValue(1),
        position: 'absolute',
        top: theme.mixins.spacingValue(7),
        ...theme.shadows.medium,
    },
    map: {
        flex: 1,
    },
    textInput: {
        backgroundColor: 'transparent',
        height: 48,
        marginBottom: 0,
        marginTop: 0,
    },

    textInputContainer: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: theme.mixins.spacingValue(1),
        borderStyle: 'solid',
        borderWidth: 1,
        height: theme.mixins.spacingValue(6),
        marginBottom: theme.mixins.spacingValue(2),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(1),
    },
}));

interface Props {
    formattedAddressKey?: string;
    isLoading?: boolean;
    latitudeDeltaKey?: string;
    latitudeKey?: string;
    longitudeDeltaKey?: string;
    longitudeKey?: string;
    onLoading?: (loading: boolean) => void;
}

const getInitialCords = ({
    latitude,
    latitudeDelta,
    longitude,
    longitudeDelta,
    location,
}: Record<'latitude' | 'latitudeDelta' | 'longitude' | 'longitudeDelta' | 'location', any>): Region => ({
    latitude: latitude ?? location.coords?.latitude,
    latitudeDelta: latitudeDelta,
    longitude: longitude ?? location.coords?.longitude,
    longitudeDelta: longitudeDelta,
});

export function FormikLocationMapView({
    children,
    isLoading,
    onLoading,
    formattedAddressKey = 'formattedAddress',
    latitudeKey = 'latitude',
    latitudeDeltaKey = 'latitudeDelta',
    longitudeKey = 'longitude',
    longitudeDeltaKey = 'longitudeDelta',
}: PropsWithChildren<Props>): ReactElement {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { getFieldMeta, getFieldHelpers } = useFormikContext<Record<string, any>>();

    const styles = useStyles();
    const theme = useTheme();
    const { t } = useTranslation();
    const layout = useLayout();
    const location = useDeviceLocation();
    const notifications = useNotifications();
    const lru = useLRUCache();
    const [fetchingAddress, setFetchingAddress] = useSafeState(false);

    const { value: formattedAddress } = getFieldMeta(formattedAddressKey);
    const { value: latitude } = getFieldMeta<number>(latitudeKey);
    const { value: latitudeDelta } = getFieldMeta<number>(latitudeDeltaKey);
    const { value: longitude } = getFieldMeta<number>(longitudeKey);
    const { value: longitudeDelta } = getFieldMeta<number>(longitudeDeltaKey);

    const formattedAddressHelpers = getFieldHelpers(formattedAddressKey);
    const latitudeHelpers = getFieldHelpers<number>(latitudeKey);
    const latitudeDeltaHelpers = getFieldHelpers<number>(latitudeDeltaKey);
    const longitudeHelpers = getFieldHelpers<number>(longitudeKey);
    const longitudeDeltaHelpers = getFieldHelpers<number>(longitudeDeltaKey);

    const coords = useMemo(
        () =>
            computeCoordsWithDelta(
                getInitialCords({
                    latitude,
                    latitudeDelta,
                    longitude,
                    longitudeDelta,
                    location,
                }),
                layout,
            ),
        [latitude, latitudeDelta, layout, location, longitude, longitudeDelta],
    );

    const mapRef = useRef<MapView>(null);

    const setFormikRegion = useEvent((region: Region) => {
        latitudeHelpers.setValue(region.latitude);
        latitudeDeltaHelpers.setValue(region.latitudeDelta);
        longitudeHelpers.setValue(region.longitude);
        longitudeDeltaHelpers.setValue(region.longitudeDelta);
    });

    const handleAutocompletePress = useEvent(async (data: GooglePlaceData, detail: GooglePlaceDetail | null) => {
        if (!detail) {
            return;
        }

        const region = computeCoordsWithDelta(
            {
                latitude: detail.geometry.location.lat,
                longitude: detail.geometry.location.lng,
            },
            layout,
        );

        if (region) {
            setFormikRegion(region);
            mapRef.current?.animateToRegion(region, 500);
        }
    });

    const handleChangeFormatAddress = useEvent((text: string) => formattedAddressHelpers.setValue(text));

    const handleFetchAddress = useDebouncedEvent(async (cords: Region) => {
        const normalizedCords = [cords.latitude.toFixed(4), cords.longitude.toFixed(4)];
        const fetchKey = `formattedAddress:${normalizedCords.join(',')}`;
        const formattedAddress = await lru.getOrSet(fetchKey, async () => {
            setFetchingAddress(true);
            try {
                const { results, status } = await googleMapsEndpoint.geocode({
                    latitude: cords.latitude,
                    longitude: cords.longitude,
                });

                if (results.length === 0 || status !== 'OK') {
                    notifications.warning(t('locationInformationCouldNotBeFetched'));
                    return null;
                }

                // eslint-disable-next-line camelcase
                return results[0].formatted_address;
            } finally {
                setFetchingAddress(false);
            }
        });

        formattedAddressHelpers.setValue(formattedAddress);
    }, 500);

    const handleRegionChange = useEvent((region: Region) => {
        setFormikRegion(region);
        handleFetchAddress(region);
    });

    const handleMapViewPress = useEvent((event: MapPressEvent) => {
        const region = computeCoordsWithDelta(event.nativeEvent.coordinate, layout);
        if (region) {
            setFormikRegion(region);
            handleFetchAddress(region);
            mapRef.current?.animateToRegion(region, 500);
        }
    });

    const googlePlacesAutocompleteProps = useMemo(
        () => ({
            query: {
                key: GoogleMapsKey,
                language: 'en',
            },
            textInputProps: {
                onChangeText: handleChangeFormatAddress,
                value: formattedAddress,
            },
            renderLeftButton: (): ReactElement => {
                if (fetchingAddress) {
                    return <CircularProgress size={24} />;
                }
                return <MaterialIcons name="search" size={24} />;
            },
        }),
        [fetchingAddress, formattedAddress, handleChangeFormatAddress],
    );

    const isMapLoading = isLoading || location.fetching || !coords;

    useWatchValue(location.fetching, (value) => onLoading?.(value));

    return (
        <>
            <VStack height={48} zIndex={3}>
                <GooglePlacesAutocomplete
                    fetchDetails
                    enablePoweredByContainer={false}
                    styles={styles}
                    placeholder={t('typeBusinessLocation')}
                    onPress={handleAutocompletePress}
                    {...googlePlacesAutocompleteProps}
                />
            </VStack>

            <VStack flex safeBottom={2}>
                <VStack flex bgColor={'backgroundSecondary'} my={2} mx={-3}>
                    <VStack flex onLayout={layout.handler}>
                        {!isMapLoading ? (
                            <>
                                <MapView
                                    ref={mapRef}
                                    style={styles.map}
                                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                                    initialRegion={coords!}
                                    onRegionChangeComplete={handleRegionChange}
                                    userInterfaceStyle={theme.mode}
                                    onPress={handleMapViewPress}
                                />

                                <Box
                                    pointerEvents={'none'}
                                    position={'absolute'}
                                    top={layout.height / 2}
                                    left={layout.width / 2}
                                    width={40}
                                    height={40}
                                    mt={-2.5}
                                    ml={-2.5}
                                >
                                    <Box mt={-2.5} ml={0}>
                                        <MaterialIcons name={'location-on'} size={40} />
                                    </Box>
                                </Box>
                            </>
                        ) : (
                            <VStack flex alignItems={'center'} justifyContent={'center'} p={3}>
                                <CircularProgress />
                            </VStack>
                        )}
                    </VStack>
                </VStack>

                {children}
            </VStack>
        </>
    );
}
