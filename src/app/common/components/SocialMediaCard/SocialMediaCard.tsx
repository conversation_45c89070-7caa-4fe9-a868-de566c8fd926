import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { ReactElement } from 'react';
import { useTheme } from 'styled-components';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { Box } from '~/components/ui/Box';
import { HStack, VStack } from '~/components/ui/Grid';
import { Link } from '~/components/ui/Link/Link';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { MaterialCommunityIconsName } from '~/types/icons';

export function SocialMediaCard({ business }: { business: BusinessModel }): ReactElement | null {
    const { facebookURL, instagramURL, websiteURL, virtualTourURL } = business;

    if (!facebookURL && !instagramURL && !websiteURL && !virtualTourURL) {
        return null;
    }

    return (
        <Paper bgColor={'backgroundPrimary'} px={3} py={2} borderRadius={0}>
            <Typography variant={'title3'} fontWeight={700} fontSize={20} lineHeight={25.2}>
                Social Media
            </Typography>
            <VStack mt={2}>
                <SocialMediaCardItem url={websiteURL} icon={'web'} title={'Website'} />
                <SocialMediaCardItem url={facebookURL} icon={'facebook'} title={'Facebook'} />
                <SocialMediaCardItem url={instagramURL} icon={'instagram'} title={'Instagram'} />
            </VStack>
        </Paper>
    );
}

interface SocialMediaCardProps {
    color?: string;
    icon: string;
    title: string;
    url: string | null;
}

function SocialMediaCardItem({ url, icon, title, color }: SocialMediaCardProps): ReactElement | null {
    const theme = useTheme();

    if (!url) {
        return null;
    }

    return (
        <Link href={url}>
            <HStack alignItems={'center'} py={1}>
                <Box
                    bgColor={'backgroundTertiary'}
                    height={32}
                    width={32}
                    alignItems={'center'}
                    justifyContent={'center'}
                    borderRadius={8}
                >
                    <MaterialCommunityIcons
                        name={icon as MaterialCommunityIconsName}
                        size={20}
                        color={color || theme.palette.primary.main}
                    />
                </Box>
                <Typography ml={2} variant={'footnote'} fontWeight={500}>
                    {title}
                </Typography>
            </HStack>
        </Link>
    );
}
