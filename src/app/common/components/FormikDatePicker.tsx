import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';
import { useFormikContext } from 'formik';
import React, { useState } from 'react';
import { Platform } from 'react-native';
import { formatDate } from '@bookr-technologies/core';
import { HStack } from '~/components/ui/Grid';
import { FormikTextField, TextFieldProps } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';

interface Props extends Omit<TextFieldProps, 'value' | 'defaultValue' | 'error'> {
    name: string;
}
export function FormikDatePicker({ name, onChange, ...rest }: Props) {
    const formik = useFormikContext();
    const [date, setDate] = useState(new Date());

    const handleSelect = useEvent((event: any, date?: Date): void => {
        if (!date) return;

        formik.setFieldValue(name, formatDate(date, 'YYYY-MM-DD'));
        setDate(date);
        // setOpen(false);
    });
    const handleOpenAndroid = useEvent(() => {
        DateTimePickerAndroid.open({
            maximumDate: new Date(),
            value: date,
            mode: 'date',
            onChange: handleSelect,
        });
    });

    return (
        <>
            {Platform.OS === 'ios' && (
                <HStack
                    bgColor={'backgroundSecondary'}
                    mt={2}
                    borderRadius={10}
                    p={1.5}
                    borderColor={'borderOpaque'}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                >
                    <Typography variant={'body'} color={'contentSecondary'} ml={1}>
                        {rest.label}
                    </Typography>
                    <DateTimePicker maximumDate={new Date()} value={date} mode={'date'} onChange={handleSelect} />
                </HStack>
            )}
            {Platform.OS === 'android' && <FormikTextField name={name} onPressIn={handleOpenAndroid} {...rest} />}
        </>
    );
}
