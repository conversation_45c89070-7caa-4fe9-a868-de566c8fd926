import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ImageGallery, ImageObject } from '@georstat/react-native-image-gallery';
import { setStatusBarStyle } from 'expo-status-bar';
import React, { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ImageStyle, Pressable, TouchableOpacity } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import Swiper from 'react-native-swiper';
import { Grid, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';

interface Props {
    photos: string[];
}

export function Photos({ photos }: Props): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const [galleryOpen, setGalleryOpen] = useState(false);
    const [activeIndex, setActiveIndex] = useState(0);

    const handleGalleryPress = useEvent(() => {
        setStatusBarStyle('light', false);

        setGalleryOpen(true);
    });

    const handleGalleryClose = useEvent(() => {
        setStatusBarStyle('dark', false);
        setGalleryOpen(false);
    });

    const galleryImages = useMemo<ImageObject[]>(
        () =>
            photos.map((photo, index) => {
                return {
                    id: index,
                    url: photo,
                };
            }),
        [photos],
    );

    const photos$ = useMemo(() => {
        if (photos.length === 0) {
            return (
                <Grid flex justifyContent={'center'} alignItems={'center'} bgColor={'#eaeaea'} borderRadius={20}>
                    <Typography variant={'caption1'} textAlign="center">
                        {t('noPreviewAvailable')}
                    </Typography>
                </Grid>
            );
        }

        return photos.map((photoUrl) => (
            <Pressable key={photoUrl} onPress={handleGalleryPress}>
                <Image tint={'dark'} uri={photoUrl} style={styles.image as ImageStyle} />
            </Pressable>
        ));
    }, [handleGalleryPress, photos, styles.image, t]);

    const renderCustomImage = useEvent(
        (image: ImageObject): ReactElement => (
            <VStack justifyContent={'center'} flex>
                <Image tint={'dark'} uri={image.url} style={styles.customImage} />
            </VStack>
        ),
    );

    const renderCustomThumb = useEvent(
        (image: ImageObject, index: number, active: boolean): ReactElement => (
            <Image style={sx(styles.thumb, active && styles.thumbActive)} uri={image.thumbUrl || image.url} />
        ),
    );

    return (
        <Grid>
            <Swiper
                loadMinimal={true}
                activeDotColor={'#fff'}
                dotColor={'#afafaf'}
                style={{ height: 250 }}
                dotStyle={{ overflow: 'hidden' }}
                onIndexChanged={setActiveIndex}
                renderPagination={(index, total): ReactElement => (
                    <Grid
                        alignItems={'center'}
                        justifyContent={'center'}
                        borderRadius={10}
                        bgColor={'backgroundPrimary'}
                        py={1}
                        width={48}
                        style={styles.pagination}
                    >
                        <Typography
                            textAlign={'center'}
                            fontSize={12}
                            lineHeight={15}
                            fontWeight={500}
                            variant={'caption1'}
                        >
                            {index + 1}/{total}
                        </Typography>
                    </Grid>
                )}
            >
                {photos$}
            </Swiper>
            {photos && photos.length > 0 && (
                <ImageGallery
                    close={handleGalleryClose}
                    isOpen={galleryOpen}
                    images={galleryImages}
                    initialIndex={activeIndex}
                    renderCustomImage={renderCustomImage}
                    renderCustomThumb={renderCustomThumb}
                    renderHeaderComponent={() => {
                        return (
                            <TouchableOpacity style={styles.closeButton} onPress={handleGalleryClose}>
                                <MaterialIcons name={'close'} size={24} color={'white'} />
                            </TouchableOpacity>
                        );
                    }}
                />
            )}
        </Grid>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    image: {
        height: 250,
        width: '100%',
    },
    pagination: {
        bottom: 30,
        position: 'absolute',
        right: 24,
    },
    customImage: {
        width: '100%',
        height: '50%',
    },
    thumb: {
        borderRadius: 8,
        height: 48,
        margin: theme.mixins.spacingValue(0.5),
        width: 48,
        borderWidth: 2,
        borderStyle: 'solid',
        borderColor: theme.palette.contentSecondary.main,
    },
    thumbActive: {
        borderColor: theme.palette.accent.main,
    },
    closeButton: {
        height: 48,
        width: 48,
        display: 'flex',
        justifyContent: 'center',
        alignSelf: 'flex-end',
        marginTop: 80,
    },
}));
