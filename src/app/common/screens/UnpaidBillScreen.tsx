import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { StripeCheckoutModal } from '~/components/StripeCheckoutModal';
import Button from '~/components/ui/Button';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { PaymentCommissionCancel, PaymentCommissionSuccess } from '~/data/applicationData';
import { useNavigateToMainScreen } from '~/hooks/useNavigateToMainScreen';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';

export function UnpaidBillScreen() {
    const { t } = useTranslation();
    const user = useUser();
    const notifications = useNotifications();
    const navigateToMainScreen = useNavigateToMainScreen();
    const payMutation = useMutation(
        'payBill',
        () =>
            paymentEndpoint.createCheckoutSessionCommission({
                successUrl: PaymentCommissionSuccess,
                cancelUrl: PaymentCommissionCancel,
            }),
        {
            onSuccess: (data) => setSessionId(data?.sessionId || ''),
            onError: function (error) {
                notifications.error(getErrorMessage(error));
            },
        },
    );

    const [sessionId, setSessionId] = useState<string | null>(null);

    const handleClick = () => payMutation.mutateAsync();

    const handleSuccessPayment = useCallback(() => {
        navigateToMainScreen();
    }, [navigateToMainScreen]);

    if (!user) {
        return null;
    }

    return (
        <Screen px={3} bgColor={'backgroundPrimary'} safeArea>
            <VStack flex alignItems={'center'} mt={5} p={1} justifyContent={'space-between'}>
                <Grid width={'100%'}>
                    <Grid
                        width={116}
                        height={116}
                        borderRadius={58}
                        justifyContent={'center'}
                        alignItems={'center'}
                        bgColor={'backgroundSecondary'}
                        alignSelf={'center'}
                        mb={5}
                    >
                        <MaterialIcons name={'receipt-long'} size={48} />
                    </Grid>
                    <Typography variant={'title1'} fontWeight={700} lineHeight={35}>
                        {t('unpaidBillTitle')}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500} lineHeight={22.5} color={'textSecondary'} mt={1}>
                        {t('unpaidBillMessage')}
                    </Typography>
                    <Paper bgColor={'backgroundSecondary'} width={'100%'} p={2} mt={4}>
                        <HStack width={'100%'} alignItems={'center'}>
                            <Typography variant={'subhead'} fontWeight={700} fontSize={15}>
                                {t('activityMonth') as string}:
                            </Typography>
                            <Typography
                                variant={'subhead'}
                                color={'textSecondary'}
                                fontWeight={500}
                                pl={0.5}
                                textTransform={'capitalize'}
                            >
                                {moment().subtract(1, 'month').format('MMMM, YYYY')}
                            </Typography>
                        </HStack>
                    </Paper>
                    {user?.accountType === AccountType.Employee && (
                        <Paper mt={2} width={'100%'} p={2} flexDirection={'row'}>
                            <MaterialIcons name={'warning'} size={20} />
                            <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} pl={1}>
                                {t('onlyAdminCanPay')}
                            </Typography>
                        </Paper>
                    )}
                </Grid>
                {user?.accountType === AccountType.BusinessOwner && (
                    <Button
                        loading={payMutation.isLoading}
                        label={t('payNow')}
                        onPress={handleClick}
                        fullWidth
                        size={'large'}
                        color={'accent'}
                    />
                )}
            </VStack>
            <StripeCheckoutModal
                visible={!!sessionId}
                sessionId={sessionId}
                onClose={(): void => setSessionId(null)}
                onSuccess={handleSuccessPayment}
            />
        </Screen>
    );
}
