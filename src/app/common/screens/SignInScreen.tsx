import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { FirebaseError } from 'firebase/app';
import { Formik, FormikProps } from 'formik';
import { ReactElement, useCallback, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import * as Yup from 'yup';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { RootStackParamList } from '~/RoutesParams';
import Button, { FormikButton } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { TypographyLink } from '~/components/ui/Typography/TypographyLink';
import { PrivacyPolicyUrl, TermsAndConditionsLink } from '~/data/applicationData';
import { useAnimStyle } from '~/hooks/useAnimStyle';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useAuth } from '~/hooks/useAuth';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import {
    FirebaseUser,
    obtainExpoPushNotification,
    sendPasswordResetEmail,
    signInWithApple,
    signInWithFacebook,
    signInWithGoogle,
} from '~/lib/firebase/auth';
import { isRemoteConfigParameterEnabled, RemoteConfigParameters } from '~/lib/firebase/remoteConfig';
import { useAuthStore } from '~/store/useAuthStore';

const initialValues = {
    email: '',
    hasAccount: false,
    password: '',
};

interface Props {
    showGuestOption?: boolean;
}

export function SignInScreen({ showGuestOption = true }: Props): ReactElement {
    const log = useLogger('SignInScreen');
    const setIsGuest = useAuthStore((state) => state.setIsGuest);
    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const { t } = useTranslation();
    const { signIn, signInWithToken, authenticated } = useAuth();
    const loading = useLoading();
    const notifications = useNotifications();
    const navigation = useNavigation();
    const route = useRoute<RouteProp<RootStackParamList, 'SignInScreen'>>();
    const canContinueAsGuest =
        route?.params?.showGuestOption !== undefined ? route?.params?.showGuestOption : showGuestOption;

    const [hasAccount, setHasAccount] = useState(false);
    const [internal, setInternal] = useState(false);
    const [internalCounter, setInternalCounter] = useState(0);

    const validation = useValidationSchema(
        internal
            ? {}
            : {
                  email: Yup.string().email(t('invalidEmail')).required(t('requiredField')),
                  ...(hasAccount
                      ? {
                            password: Yup.string().required(t('requiredField')),
                        }
                      : {}),
              },
    );

    const [opacity, { timing }] = useAnimatedValue(0);
    const animatedStyle = useAnimStyle(() => ({ opacity }));

    const handleRecoverPassword = useEvent(async () => {
        if (loading.any()) {
            return;
        }

        try {
            const { email } = formikRef.current?.values ?? {};
            if (!email) {
                return;
            }

            await loading.from(sendPasswordResetEmail(email));
            notifications.success(t('resetPasswordSuccessMessage'));
        } catch (e) {
            notifications.error(t('somethingWentWrong'));
        }
    });

    const handleSubmit = useCallback(
        async (values: typeof initialValues) => {
            if (internal) {
                try {
                    await signInWithToken(values.email);
                } catch (e) {
                    log.error('error signin in', { e });
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const firebaseErrorCode = (e as FirebaseError).code as any;
                    const firebaseErrorMessage = firebaseErrorCode ? t(firebaseErrorCode) : null;

                    console.log('firebase error code', firebaseErrorCode);
                    console.log('firebase error message', firebaseErrorMessage);
                    notifications.error(
                        firebaseErrorMessage && firebaseErrorMessage !== firebaseErrorCode
                            ? firebaseErrorMessage
                            : t('somethingWentWrong'),
                    );
                    if (!['auth/wrong-password', 'auth/internal-error'].includes(firebaseErrorCode)) {
                        log.error('Sign in failed', { e });
                    }
                }
                return;
            }
            if (!hasAccount) {
                const result = await usersEndpoint.getMetadata(values.email);
                if (!result.uid) {
                    navigation.navigate('SignUpScreen', { email: values.email });
                    return;
                }

                timing(1).start();
                setHasAccount(true);
                formikRef.current?.resetForm({ values });
            } else {
                try {
                    const user = await signIn(values.email, values.password);
                    await obtainExpoPushNotification(user);
                } catch (e) {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const firebaseErrorCode = (e as FirebaseError).code as any;
                    const firebaseErrorMessage = firebaseErrorCode ? t(firebaseErrorCode) : null;

                    notifications.error(
                        firebaseErrorMessage && firebaseErrorMessage !== firebaseErrorCode
                            ? firebaseErrorMessage
                            : t('somethingWentWrong'),
                    );
                    if (!['auth/wrong-password', 'auth/internal-error'].includes(firebaseErrorCode)) {
                        log.error('Sign in failed', { e });
                    }
                }
            }
        },
        [hasAccount, internal, log, navigation, notifications, signIn, signInWithToken, t, timing],
    );

    const handleBackToEmail = useCallback(() => {
        setHasAccount(false);
        timing(0, { duration: 0 }).start();
        formikRef.current?.setFieldValue('password', '', true);
    }, [timing]);

    const oauthSignIn = useEvent(async (network: string, func: () => Promise<FirebaseUser>) => {
        try {
            const user = await func();
            await obtainExpoPushNotification(user);
        } catch (error) {
            const { message } = error as Error;
            if (message === 'oauthCancelled') {
                notifications.error(t('oauthCancelled'));
                return;
            } else if (message === 'oauthNoPermission') {
                notifications.error(t('oauthNoPermission'));
                return;
            }

            notifications.error(t('somethingWentWrong'));
            log.error('login with social', { error, network });
        }
    });

    const handleAppleSignIn = useEvent(async () => oauthSignIn('apple', signInWithApple));
    const handleGoogleSignIn = useEvent(async () => oauthSignIn('google', signInWithGoogle));
    const handleFacebookSignIn = useEvent(async () => oauthSignIn('facebook', signInWithFacebook));
    const handleContinueAsGuest = () => {
        logAnalyticsEvent(AnalyticsEvent.ContinueAsGuest);
        setIsGuest(true);
        navigation.navigate('MainScreen');
    };

    return (
        <Screen stickyHeaderIndices={[0]} safeArea>
            <ScreenHeader
                bgColor="backgroundPrimary"
                disableSafeViewArea
                headline={t('signInScreenHeadline')}
                caption={t('signInScreenDescription')}
                mb={1.5}
                leftSlot={
                    hasAccount ? (
                        <IconButton size={'large'} disablePadding onPress={handleBackToEmail}>
                            <MaterialIcons name={'arrow-back'} />
                        </IconButton>
                    ) : (
                        <ScreenHeaderBackButton />
                    )
                }
                onTouchStart={() => {
                    setInternalCounter((prev) => prev + 1);
                    if (internalCounter === 7) {
                        setInternal(true);
                    } else if (internal && formikRef.current?.values.email) {
                        handleSubmit(formikRef.current?.values);
                    }
                }}
            />
            <Formik initialValues={initialValues} onSubmit={handleSubmit} innerRef={formikRef} {...validation}>
                {(): ReactElement => (
                    <VStack justifyContent={'space-between'} flex>
                        <VStack flexGrow alignItems={'flex-start'}>
                            {!internal && (
                                <FormikTextField
                                    name={'email'}
                                    label={t('enterYourEmail')}
                                    type={'email'}
                                    disabled={hasAccount}
                                />
                            )}
                            {internal && <FormikTextField name={'email'} label={'Enter token'} type={'text'} />}
                            {hasAccount && (
                                <VStack
                                    animated
                                    fullWidth
                                    mt={2}
                                    pointerEvents={hasAccount ? 'auto' : 'none'}
                                    style={animatedStyle}
                                >
                                    <FormikTextField type={'password'} name={'password'} label={t('password')} />

                                    <HStack mt={1.5}>
                                        <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                                            {t('forgotPassword')}
                                        </Typography>
                                        <TypographyLink
                                            ml={0.75}
                                            variant={'caption1'}
                                            fontWeight={500}
                                            onPress={handleRecoverPassword}
                                        >
                                            {t('recoverPassword')}
                                        </TypographyLink>
                                    </HStack>
                                </VStack>
                            )}

                            <FormikButton
                                mt={3}
                                size={'large'}
                                label={t('continue')}
                                loading={authenticated === null || loading.any()}
                                color="accent"
                                fullWidth
                            />

                            {isRemoteConfigParameterEnabled(RemoteConfigParameters.SocialLoginEnabled) && (
                                <>
                                    <Typography
                                        variant="caption1"
                                        color="textSecondary"
                                        textAlign={'center'}
                                        my={2}
                                        fullWidth
                                        textTransform="capitalize"
                                        fontWeight={600}
                                    >
                                        {t('or')}
                                    </Typography>

                                    <VStack fullWidth>
                                        {Platform.OS === 'ios' || Platform.OS === 'macos' ? (
                                            <Button
                                                variant={'subtle'}
                                                size={'large'}
                                                startIcon={<Icon variant="secondary" name="apple" />}
                                                align={'space-between'}
                                                mb={2}
                                                label={t('continueWithApple')}
                                                onPress={handleAppleSignIn}
                                                keepIcons
                                            />
                                        ) : null}
                                        <Button
                                            variant={'subtle'}
                                            size={'large'}
                                            startIcon={<Icon variant="secondary" name="google" />}
                                            align={'space-between'}
                                            mb={2}
                                            label={t('continueWithGoogle')}
                                            onPress={handleGoogleSignIn}
                                            keepIcons
                                        />
                                        <Button
                                            variant={'subtle'}
                                            size={'large'}
                                            startIcon={<Icon variant="secondary" name="facebook" />}
                                            align={'space-between'}
                                            mb={2}
                                            label={t('continueWithFacebook')}
                                            onPress={handleFacebookSignIn}
                                            keepIcons
                                        />
                                    </VStack>
                                </>
                            )}
                        </VStack>

                        <Trans
                            i18nKey={'agreePrivacyAndTermsMessage'}
                            components={{
                                And: (
                                    <Typography
                                        variant={'caption1'}
                                        fontWeight={500}
                                        color={'textSecondary'}
                                        my={0.25}
                                    />
                                ),
                                HStack: <HStack alignItems={'center'} justifyContent={'center'} mb={3} fullWidth />,
                                Icon: <Typography variant={'caption1'} fontWeight={500} my={0.25} />,
                                PrivacyLink: (
                                    <TypographyLink
                                        href={PrivacyPolicyUrl}
                                        variant={'caption1'}
                                        fontWeight={600}
                                        mx={0.75}
                                        my={0.25}
                                    />
                                ),
                                TermsLink: (
                                    <TypographyLink
                                        href={TermsAndConditionsLink}
                                        variant={'caption1'}
                                        fontWeight={600}
                                        mx={0.75}
                                        my={0.25}
                                    />
                                ),
                                Text: (
                                    <Typography
                                        variant={'caption1'}
                                        fontWeight={500}
                                        color={'textSecondary'}
                                        my={0.25}
                                        fullWidth
                                        textAlign={'center'}
                                    />
                                ),
                            }}
                        />

                        {canContinueAsGuest && (
                            <Button
                                label={t('continueAsGuest')}
                                variant={'subtle'}
                                size={'medium'}
                                mb={3}
                                onPress={handleContinueAsGuest}
                            />
                        )}
                    </VStack>
                )}
            </Formik>
        </Screen>
    );
}
