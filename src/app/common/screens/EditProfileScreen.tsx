import * as Localization from 'expo-localization';
import { Formik, FormikProps } from 'formik';
import React, { ReactElement, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { useTheme } from 'styled-components/native';
import * as Yup from 'yup';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { UpsertUserDto } from '@bookr-technologies/api/types/UpsertUserDto';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { ProfileAvatar } from '~/app/business/components/ProfileAvatar';
import { FormikButton } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { Spacer } from '~/components/ui/Spacer';
import { FormikTextField } from '~/components/ui/TextField';
import { FormikLanguageField } from '~/components/ui/TextField/FormikLanguageField';
import { FormikPhoneField } from '~/components/ui/TextField/FormikPhoneField';
import { Typography } from '~/components/ui/Typography';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { useAuthStore } from '~/store/useAuthStore';

type UpdateUserValues = {
    displayName: string;
    language: string;
    phoneNumber: string;
};

const getButtonDisabledState = (initialValues: UpdateUserValues, updatedValues: UpdateUserValues): boolean => {
    const initialValuesKeys = Object.keys(initialValues);

    for (const key of initialValuesKeys) {
        if (initialValues[key as keyof UpdateUserValues] !== updatedValues?.[key as keyof typeof updatedValues]) {
            return false;
        }
    }
    return true;
};

export function EditProfileScreen() {
    const user = useUser();
    const theme = useTheme();
    const { t } = useTranslation();
    const notifications = useNotifications();
    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const resolveUser = useAuthStore((state) => state.resolveUser);

    const updateUserMutation = useMutation(
        'updateUser',
        (data: UpsertUserDto) => {
            if (!user?.uid) {
                return Promise.reject(new Error('User id missing'));
            }
            return usersEndpoint.update(user?.uid, data);
        },
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
            onSuccess: () => {
                notifications.success(t('profileUpdateSuccess'));
            },
        },
    );

    const initialValues = useMemo(
        () =>
            ({
                displayName: user?.displayName,
                email: user?.email,
                language: Localization.locale,
                phoneNumber: user?.phoneNumber,
            } as UpdateUserValues),
        [user?.displayName, user?.email, user?.phoneNumber],
    );

    const handleOnSubmit = useCallback(
        async (values: typeof initialValues) => {
            await updateUserMutation.mutateAsync({
                displayName: values.displayName,
                language: values.language,
                phoneNumber: values.phoneNumber,
            });
            await resolveUser();
        },
        [resolveUser, updateUserMutation],
    );

    const validation = useValidationSchema({
        displayName: Yup.string().required(t('requiredField')),
        language: Yup.string().required(t('requiredField')),
        phoneNumber: Yup.string().required(t('requiredField')),
    });

    return (
        <Screen safeBottom={2} stickyHeaderIndices={[0]}>
            <ScreenHeader
                bgColor="backgroundPrimary"
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('editProfile')}
                headlineActions={<ProfileAvatar />}
            />
            <Formik initialValues={initialValues} onSubmit={handleOnSubmit} innerRef={formikRef} {...validation}>
                {({ values }): ReactElement => (
                    <VStack flex>
                        <Typography
                            variant={'footnote'}
                            color={theme.palette.contentTertiary.main}
                            fontWeight={500}
                            mb={1.5}
                        >
                            {t('personalDetails')}
                        </Typography>
                        <FormikTextField name={'displayName'} label={t('firstNameAndLastName')} mb={2} />
                        <FormikPhoneField name={'phoneNumber'} />
                        <FormikTextField type={'email'} name={'email'} label={t('enterYourEmail')} disabled mb={4} />
                        <Typography
                            variant={'footnote'}
                            color={theme.palette.contentTertiary.main}
                            fontWeight={500}
                            mb={1.5}
                        >
                            {t('language')}
                        </Typography>
                        <FormikLanguageField name={'language'} />

                        <Spacer />

                        <FormikButton
                            size={'large'}
                            label={t('saveChanges')}
                            loading={false}
                            mb={2}
                            disabled={getButtonDisabledState(initialValues, values)}
                        />
                    </VStack>
                )}
            </Formik>
        </Screen>
    );
}
