import moment from 'moment';
import { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, LayoutAnimation, RefreshControl } from 'react-native';
import { activityEndpoint } from '@bookr-technologies/api/endpoints/activityEndpoint';
import { ActivityModel } from '@bookr-technologies/api/models/ActivityModel';
import { PageableResponse } from '@bookr-technologies/api/types/PageableResponse';
import { ActivityCard } from '~/app/common/components/ActivityCard/ActivityCard';
import emptyNotifications from '~/assets/emptyBackgrounds/emptyNotifications.png';
import { EmptyStateCard } from '~/components/EmptyStateCard';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { makeStyles } from '~/components/ui/makeStyles';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { navigate } from '~/lib/utils/navigation';
import { useApplicationStore } from '~/store/useApplicationStore';

const useStyles = makeStyles(() => ({
    image: {
        height: 200,
        marginTop: -20,
    },
    list: { flex: 1 },
    listContent: { alignSelf: 'center', paddingTop: 10, width: '100%' },
}));

export function NotificationsScreen(): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const notifications = useNotifications();
    const log = useLogger('NotificationsScreen');

    const [refreshing, setRefreshing] = useState(false);
    const [activity, setActivity] = useState<PageableResponse<ActivityModel>>();
    const [displayedActivity, setDisplayedActivity] = useState<ActivityModel[]>([]);
    const setNotificationsLastChecked = useApplicationStore((state) => state.setNotificationsLastChecked);

    const handleOnRefresh = async (page: number): Promise<void> => {
        if (refreshing) {
            return;
        }

        setRefreshing(true);

        try {
            const data = await activityEndpoint.fetchData({ page, size: 10 });
            LayoutAnimation.easeInEaseOut();
            setActivity(data);
            setDisplayedActivity((displayedActivity) =>
                page === 0 ? data.content : [...displayedActivity, ...data.content],
            );
        } catch (e) {
            log.error('Failed to fetch notifications', { e });
            notifications.error(t('notificationsError'));
        } finally {
            setRefreshing(false);
        }
    };

    const handleBack = () => {
        setNotificationsLastChecked(moment().format());
    };

    const handleOnPress = (activity: ActivityModel) => {
        const { appointment } = activity;
        navigate('AppointmentDetailsScreen', { appointment, businessView: true });
    };

    useEffect(() => {
        handleOnRefresh(0);
        // eslint-disable-next-line
    }, []);

    return (
        <Screen disableScroll bgColor={'backgroundSecondary'}>
            <ScreenHeader
                headline={t('notifications')}
                leftSlot={<ScreenHeaderBackButton onPress={handleBack} />}
                bgColor={'transparent'}
            />
            {!displayedActivity || displayedActivity.length === 0 ? (
                <EmptyStateCard
                    title={t('noNotifications')}
                    source={emptyNotifications}
                    imageProps={{
                        style: styles.image,
                    }}
                />
            ) : (
                <FlatList
                    refreshControl={<RefreshControl onRefresh={() => handleOnRefresh(0)} refreshing={refreshing} />}
                    showsVerticalScrollIndicator={false}
                    style={styles.list}
                    contentContainerStyle={styles.listContent}
                    data={displayedActivity}
                    initialNumToRender={5}
                    maxToRenderPerBatch={3}
                    renderItem={({ item }) => <ActivityCard activity={item} onPress={handleOnPress} />}
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={(item) => `${item.id}`}
                    onEndReachedThreshold={0.8}
                    onEndReached={async () => {
                        if (!activity) {
                            return;
                        }
                        if (!activity.last) {
                            handleOnRefresh(activity.pageable.pageNumber + 1);
                        }
                    }}
                />
            )}
        </Screen>
    );
}
