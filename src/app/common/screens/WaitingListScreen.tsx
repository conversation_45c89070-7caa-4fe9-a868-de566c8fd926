import { RouteProp, useRoute } from '@react-navigation/native';
import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList } from 'react-native';
import { RootStackParamList } from '~/RoutesParams';
import { ClientCard } from '~/app/business/components/ClientCard';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';

export function WaitingListScreen(): ReactElement {
    const route = useRoute<RouteProp<RootStackParamList, 'WaitingListScreen'>>();
    const { t } = useTranslation();
    const { waitingList } = route.params;

    return (
        <Screen px={3} bgColor={'backgroundSecondary'} disableScroll>
            <ScreenHeader
                headline={t('waitingClients')}
                bgColor={'backgroundSecondary'}
                leftSlot={<ScreenHeaderBackButton />}
            />
            <VStack flex>
                <FlatList
                    disableScrollViewPanResponder
                    showsVerticalScrollIndicator={false}
                    data={waitingList}
                    initialNumToRender={10}
                    maxToRenderPerBatch={10}
                    renderItem={({ item }): ReactElement => <ClientCard mb={3} client={item} />}
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={(item): string => item.uid}
                />
            </VStack>
        </Screen>
    );
}
