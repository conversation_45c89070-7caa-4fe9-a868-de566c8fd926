import { Fontisto } from '@expo/vector-icons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useRoute } from '@react-navigation/native';
import { ReactElement, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { useTheme } from 'styled-components/native';
import { RootStackParamList } from '~/RoutesParams';
import { Box } from '~/components/ui/Box';
import { Button } from '~/components/ui/Button';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useBusiness } from '~/hooks/useBusiness';
import { useEvent } from '~/hooks/useEvent';
import { useLayout } from '~/hooks/useLayout';
import { Features } from '~/lib/Features';
import { computeCoordsWithDelta } from '~/lib/map/utils';
import { num } from '~/lib/utils/number';
import { str } from '~/lib/utils/string';

const useStyles = makeStyles(() => ({
    map: {
        flex: 1,
    },
}));

export function BusinessLocationScreen(): ReactElement {
    const { t } = useTranslation();
    const theme = useTheme();
    const styles = useStyles();
    const layout = useLayout();
    const mapRef = useRef<MapView>(null);

    const { params } = useRoute<RouteProp<RootStackParamList, 'BusinessLocationScreen'>>();
    const { data: business, isLoading } = useBusiness(params?.business);

    const coords = useMemo(
        () => ({
            latitude: num(business?.latitude),
            latitudeDelta: num(business?.latitudeDelta),
            longitude: num(business?.longitude),
            longitudeDelta: num(business?.longitudeDelta),
        }),
        [business],
    );
    const initialRegion = useMemo(() => computeCoordsWithDelta(coords, layout), [coords, layout]);

    const handlePressLocation = useEvent(() =>
        Linking.openURL(`https://www.google.com/maps/search/?api=1&query=${business?.formattedAddress}`),
    );

    return (
        <Screen bgColor={'backgroundPrimary'} disableScroll disablePadding>
            <ScreenHeader
                leftSlot={
                    <HStack flex={1} justifyContent={'space-between'} alignItems={'center'}>
                        <ScreenHeaderBackButton />
                        <Typography variant={'title3'} fontWeight={700} fontSize={20} ml={-3}>
                            {t('location')}
                        </Typography>
                        <Grid />
                    </HStack>
                }
                pl={2}
            />

            <VStack flex safeBottom={3} onLayout={layout.handler}>
                {isLoading || !initialRegion ? (
                    <VStack alignItems={'center'} justifyContent={'center'} flex bgColor="backgroundSecondary">
                        <CircularProgress />
                    </VStack>
                ) : (
                    <MapView
                        ref={mapRef}
                        style={styles.map}
                        initialRegion={initialRegion}
                        userInterfaceStyle={theme.mode}
                    >
                        <Marker coordinate={coords}>
                            <Box>
                                <MaterialIcons name={'location-on'} size={40} />
                            </Box>
                        </Marker>
                    </MapView>
                )}
                <Paper bgColor={'backgroundPrimary'} px={3} pt={3} flexWrap={'nowrap'}>
                    <Typography variant={'title2'} fontWeight={700} mb={1}>
                        {str(business?.name)}
                    </Typography>
                    <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={3}>
                        {str(business?.formattedAddress)}
                    </Typography>
                    <VStack>
                        <Button
                            size={'large'}
                            color={'accent'}
                            label={t('getDirections')}
                            startIcon={<Icon name={'navigation'} />}
                            onPress={handlePressLocation}
                            mb={2}
                            loading={isLoading}
                            align={!isLoading ? 'space-between' : 'center'}
                            keepIcons
                        />
                        {!isLoading && Features.UberIntegration && (
                            <Button
                                size={'large'}
                                color={'primary'}
                                label={t('getThereWithUber')}
                                startIcon={<Fontisto name={'uber'} color={'secondary'} />}
                                align={'space-between'}
                                keepIcons
                            />
                        )}
                    </VStack>
                </Paper>
            </VStack>
        </Screen>
    );
}
