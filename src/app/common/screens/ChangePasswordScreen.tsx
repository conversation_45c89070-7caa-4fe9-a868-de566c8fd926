import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Formik, FormikProps } from 'formik';
import React, { ReactElement, useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components/native';
import * as Yup from 'yup';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { FormikButton } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { getFirebaseUser, changePassword } from '~/lib/firebase/auth';
import { getErrorMessageWithFirebase } from '~/lib/utils/errors';

type ChangePasswordFormValues = {
    confirmPassword: string;
    newPassword: string;
    oldPassword: string;
};

const initialValues = {
    confirmPassword: '',
    newPassword: '',
    oldPassword: '',
} as ChangePasswordFormValues;

export function ChangePasswordScreen(): ReactElement {
    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const theme = useTheme();
    const { t } = useTranslation();
    const notifications = useNotifications();
    const log = useLogger('ChangePasswordScreen');
    const user = getFirebaseUser();
    const [passwordChangedSuccess, setPasswordChangeSuccess] = useState(false);

    const validation = useValidationSchema({
        newPassword: Yup.string()
            .required(t('requiredField'))
            .min(8, t('minLength', { minLength: 8 })),
        oldPassword: Yup.string()
            .required(t('requiredField'))
            .min(8, t('minLength', { minLength: 8 })),
        passwordConfirmation: Yup.string()
            .required(t('requiredField'))
            .oneOf([Yup.ref('newPassword'), null], t('passwordNotMatch')),
    });

    const handleSubmit = useCallback(
        async (values: ChangePasswordFormValues) => {
            if (!user?.email) {
                return;
            }

            try {
                await changePassword(values.oldPassword, values.newPassword);
                formikRef.current?.resetForm();
                setPasswordChangeSuccess(true);
            } catch (e) {
                log.error('change password failed', { e });
                notifications.error(getErrorMessageWithFirebase(e as Error));
            }
        },
        [log, notifications, user],
    );

    const handleSheetAction = useEvent(() => {
        setPasswordChangeSuccess(false);
    });

    return (
        <Screen stickyHeaderIndices={[0]}>
            <ScreenHeader rightSlot={<ScreenHeaderBackButton icon={<MaterialIcons name={'close'} />} />} />
            <VStack mb={2}>
                <Typography variant={'title2'} fontWeight={700}>
                    {t('changePass')}
                </Typography>
                <Typography variant={'footnote'} fontWeight={500} mt={1} color={theme.palette.contentTertiary.main}>
                    {t('createPass')}
                </Typography>
            </VStack>
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                innerRef={formikRef}
                {...validation}
                validateOnMount
            >
                <>
                    <FormikTextField type={'password'} name={'oldPassword'} label={t('oldPass')} mb={1} />
                    <FormikTextField type={'password'} name={'newPassword'} label={t('newPass')} mb={1} />
                    <FormikTextField
                        type={'password'}
                        name={'passwordConfirmation'}
                        label={t('confirmYourPassword')}
                        mb={1}
                    />

                    <Spacer />

                    <FormikButton size={'large'} color={'accent'} label={t('changePass')} mb={4} />
                </>
            </Formik>
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Success}
                marginInset={3}
                icon={<MaterialIcons name={'check-circle-outline'} size={53} color={'accent'} />}
                open={passwordChangedSuccess}
                headline={t('passChangeSuccess')}
                primaryColor={'accent'}
                primaryText={t('gotIt')}
                onAction={handleSheetAction}
            />
        </Screen>
    );
}
