import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import * as Sentry from '@sentry/react-native';
import { Formik } from 'formik';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { useTheme } from 'styled-components/native';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { PaymentMethod } from '@bookr-technologies/api/models/PaymentMethod';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { RootStackParamList } from '~/RoutesParams';
import { StripeUrlModal } from '~/components/StripeUrlModal/StripeUrlModal';
import { FormikButton } from '~/components/ui/Button';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { FormikRadio } from '~/components/ui/Radio';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { PaymentAppointmentCancel, PaymentAppointmentSuccess } from '~/data/applicationData';
import { useNotifications } from '~/hooks/useNotifications';

const useStyles = makeStyles(({ theme }) => ({
    paymentMethod: {
        height: 'auto',
        flexDirection: 'row-reverse',
        justifyContent: 'space-between',
        width: '100%',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.backgroundSecondary.main,
        borderWidth: 1,
        marginBottom: 16,
        paddingVertical: theme.mixins.spacingValue(2),
        paddingHorizontal: theme.mixins.spacingValue(2),
    },
}));

export function AppointmentChoosePaymentScreen() {
    const theme = useTheme();
    const { t } = useTranslation();
    const { params } = useRoute<RouteProp<RootStackParamList, 'AppointmentChoosePaymentScreen'>>();
    const notifications = useNotifications();
    const { navigate } = useNavigation();
    const styles = useStyles();

    const { appointments } = params;
    const [sessionUrl, setSessionUrl] = useState<string | null>(null);
    const [loadingNavigation, setLoadingNavigation] = useState(false);
    const checkoutSessionMutation = useMutation(
        'createCheckoutSessionForAppointment',
        () =>
            paymentEndpoint.createCheckoutSessionAppointment({
                appointmentId: String(appointments[0].id),
                successUrl: PaymentAppointmentSuccess,
                cancelUrl: PaymentAppointmentCancel,
            }),
        {
            onSuccess: (data) => {
                setSessionUrl(data?.sessionUrl || null);
            },
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
        },
    );

    const handleSuccessPayment = useCallback(() => {
        setSessionUrl(null);
        setLoadingNavigation(true);
        setTimeout(() => {
            navigate('ClientApplication', {
                screen: 'Home' as any,
                params: {
                    actionAfterLoad: () => {
                        const { appointments, showAddToCalendar, addToCalendar, onGoToAppointment } = params;
                        navigate('AppointmentConfirmationScreen', {
                            appointments,
                            showAddToCalendar,
                            addToCalendar,
                            onGoToAppointment,
                        });
                    },
                } as any,
            });
        }, 1000);
    }, [navigate, params]);

    const handleSubmit = useCallback(
        async (values: { paymentMethod: PaymentMethod }) => {
            if (PaymentMethod.CASH === values.paymentMethod) {
                handleSuccessPayment();
            } else if (PaymentMethod.CARD === values.paymentMethod) {
                checkoutSessionMutation.mutate();
            } else {
                Sentry.captureMessage(`Wrong payment method: ${values.paymentMethod}`);
            }
        },
        [checkoutSessionMutation, handleSuccessPayment],
    );

    return (
        <Screen>
            <ScreenHeader disableSafeViewArea />

            <VStack flex justifyContent={'flex-end'} alignItems={'center'} alignContent={'center'} safeBottom={2}>
                <Grid
                    bgColor={'backgroundSecondary'}
                    justifyContent={'center'}
                    alignItems={'center'}
                    width={122}
                    height={122}
                    borderRadius={61}
                >
                    <MaterialIcons name={'account-balance-wallet'} size={48} color={theme.palette.accent.main} />
                </Grid>
                <Typography variant={'title1'} fontWeight={700} mt={5} textAlign={'center'}>
                    {t('choosePaymentMethod')}
                </Typography>

                <Typography
                    variant={'footnote'}
                    color={'textSecondary'}
                    textAlign={'center'}
                    fontWeight={500}
                    mt={2}
                    mb={9}
                    fullWidth
                >
                    {t('choosePaymentMethodDescription')}
                </Typography>

                <Formik
                    initialValues={{
                        paymentMethod: PaymentMethod.CASH,
                    }}
                    onSubmit={handleSubmit}
                >
                    {({ values }) => (
                        <Grid fullWidth>
                            <FormikRadio
                                style={{
                                    ...styles.paymentMethod,
                                    backgroundColor:
                                        values.paymentMethod === PaymentMethod.CASH
                                            ? theme.palette.accent.shade10
                                            : theme.palette.backgroundSecondary.main,
                                    borderColor:
                                        values.paymentMethod === PaymentMethod.CASH
                                            ? theme.palette.accent.main
                                            : theme.palette.backgroundSecondary.main,
                                }}
                                name={'paymentMethod'}
                                value={PaymentMethod.CASH}
                                label={
                                    <HStack flex alignItems={'center'}>
                                        <MaterialIcons name={'payments'} size={24} color={theme.palette.primary.main} />
                                        <Typography fontSize={15} variant={'subhead'} fontWeight={500} ml={1.4}>
                                            {t('payAtLocation')}
                                        </Typography>
                                    </HStack>
                                }
                            />
                            <FormikRadio
                                style={{
                                    ...styles.paymentMethod,
                                    backgroundColor:
                                        values.paymentMethod === PaymentMethod.CARD
                                            ? theme.palette.accent.shade10
                                            : theme.palette.backgroundSecondary.main,
                                    borderColor:
                                        values.paymentMethod === PaymentMethod.CARD
                                            ? theme.palette.accent.main
                                            : theme.palette.backgroundSecondary.main,
                                }}
                                name={'paymentMethod'}
                                value={PaymentMethod.CARD}
                                label={
                                    <HStack flex alignItems={'center'}>
                                        <MaterialCommunityIcons
                                            name={'credit-card'}
                                            size={24}
                                            color={theme.palette.primary.main}
                                        />
                                        <Typography fontSize={15} variant={'subhead'} fontWeight={500} ml={1.4}>
                                            {t('payOnline')}
                                        </Typography>
                                    </HStack>
                                }
                            />

                            <FormikButton
                                fullWidth
                                mt={8}
                                color={'accent'}
                                label={t('continue')}
                                size={'large'}
                                loading={checkoutSessionMutation.isLoading || loadingNavigation}
                            />
                        </Grid>
                    )}
                </Formik>
            </VStack>
            {sessionUrl ? (
                <StripeUrlModal
                    uri={sessionUrl}
                    onClose={(): void => setSessionUrl(null)}
                    onSuccess={handleSuccessPayment}
                />
            ) : null}
        </Screen>
    );
}
