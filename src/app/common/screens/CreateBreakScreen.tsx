import { Route<PERSON>rop, useRoute } from '@react-navigation/native';
import { Formik, FormikProps } from 'formik';
import moment from 'moment';
import React, { ReactElement, useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation, ScrollView } from 'react-native';
import { breaksEndpoint } from '@bookr-technologies/api/endpoints/breaksEndpoint';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { RootStackParamList } from '~/RoutesParams';
import { RecentBreaks, RecentBreaksValues } from '~/app/common/components/RecentBreaks';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { BottomSheetDatePicker } from '~/components/BottomSheetDatePicker';
import { BottomSheetTimePicker } from '~/components/BottomSheetTimePicker';
import { Avatar } from '~/components/ui/Avatar';
import { FormikButton } from '~/components/ui/Button';
import { Chip } from '~/components/ui/Chip';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { useBusiness } from '~/hooks/useBusiness';
import { useEvent } from '~/hooks/useEvent';
import { useUser } from '~/hooks/useUser';
import { CreateBreakForm } from '../components/CreateBreakForm';

export function CreateBreakScreen(): ReactElement {
    const { t } = useTranslation();
    const { params } = useRoute<RouteProp<RootStackParamList, 'CreateBreakScreen'>>();
    const [showInfo, setShowInfo] = useState(false);
    const user = useUser();

    const { data: currentBusiness } = useBusiness(user?.business?.id);
    const staffMembers = currentBusiness?.staffMembers ?? [];
    const [selectedMember, setSelectedMember] = useState<string | undefined>(params?.staffMemberId ?? user?.uid);

    const initialValues = useMemo(() => {
        const start = moment(params?.date);
        const end = moment(params?.date).add(1, 'hour');

        return {
            title: '',
            staffMemberId: user?.uid,
            endDay: end.format(BottomSheetDatePicker.DateFormat),
            endTime: end.format(BottomSheetTimePicker.DateFormat),
            recentBreaks: {
                active: true,
            } as RecentBreaksValues,
            startDay: start.format(BottomSheetDatePicker.DateFormat),
            startTime: start.format(BottomSheetTimePicker.DateFormat),
        };
    }, [params?.date, user]);

    const formikRef = useRef<FormikProps<typeof initialValues>>(null);

    const handleSubmit = useCallback(async (values: typeof initialValues) => {
        const start = moment(
            `${values.startDay} ${values.startTime}`,
            `${BottomSheetDatePicker.DateFormat} ${BottomSheetTimePicker.DateFormat}`,
        );

        const end = moment(
            `${values.endDay} ${values.endTime}`,
            `${BottomSheetDatePicker.DateFormat} ${BottomSheetTimePicker.DateFormat}`,
        );

        const request: Partial<BreakModel> = {
            fromDateTime: start.toISOString(),
            toDateTime: end.toISOString(),
            title: values.title,
            staffUid: values.staffMemberId,
        };
        await breaksEndpoint.create(request);
        setShowInfo(true);
    }, []);

    const handleStaffPress = useEvent((id): void => {
        LayoutAnimation.easeInEaseOut();
        if (selectedMember === id || !id) {
            setSelectedMember(undefined);
        } else {
            setSelectedMember(id);
        }
    });

    const shouldShowForm = !formikRef?.current?.values.recentBreaks.active;

    const title = useMemo<string>(() => {
        if (shouldShowForm) {
            return t('createBreakScreen.headline');
        } else {
            return t('membersBreaks');
        }
    }, [shouldShowForm, t]);

    return (
        <Screen disableScroll disablePadding>
            <ScreenHeader headline={title} leftSlot={<ScreenHeaderBackButton />} px={2}>
                {!shouldShowForm && staffMembers.length > 0 && (
                    <VStack pt={2}>
                        <HStack fullWidth>
                            <ScrollView horizontal={true} bounces={false} showsHorizontalScrollIndicator={false}>
                                {staffMembers?.map((staffMember) => (
                                    <Chip
                                        key={staffMember.uid}
                                        label={staffMember.displayName}
                                        value={staffMember.uid}
                                        checked={selectedMember === staffMember.uid}
                                        onChecked={handleStaffPress}
                                        avatar={
                                            <Avatar
                                                bgColor={'backgroundSecondary'}
                                                source={staffMember.photoURL}
                                                name={staffMember.displayName}
                                            />
                                        }
                                    />
                                ))}
                            </ScrollView>
                        </HStack>
                    </VStack>
                )}
            </ScreenHeader>

            <Formik initialValues={initialValues} onSubmit={handleSubmit} innerRef={formikRef}>
                {({ values }) => (
                    <VStack safeBottom={2} flex>
                        {!values.recentBreaks.active ? (
                            <VStack px={2} flex>
                                <CreateBreakForm />
                                <Spacer />
                                <FormikButton label={t('addBreak')} size={'large'} />
                            </VStack>
                        ) : (
                            <RecentBreaks businessId={user?.business?.id} staffId={selectedMember} />
                        )}
                    </VStack>
                )}
            </Formik>

            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Info}
                open={showInfo}
                headline={t('breakWasSuccessfullyAdded')}
                primaryText={t('gotIt')}
                onAction={() => {
                    formikRef.current?.setFieldValue('recentBreaks.active', true);
                    LayoutAnimation.easeInEaseOut();
                }}
                onClose={() => {
                    setShowInfo(false);
                    formikRef.current?.setFieldValue('recentBreaks.active', true);
                    formikRef.current?.setFieldValue('title', '');
                    LayoutAnimation.easeInEaseOut();
                }}
            />
        </Screen>
    );
}
