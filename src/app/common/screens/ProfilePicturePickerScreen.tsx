/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { AxiosRequestConfig } from 'axios';
import { ReactElement, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { UploadFile } from '@bookr-technologies/api/types/UploadFile';
import { Optional } from '@bookr-technologies/core/types';
import { RootStackParamList } from '~/RoutesParams';
import { Avatar } from '~/components/ui/Avatar';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useImagePicker } from '~/hooks/useImagePicker';
import { useLoading } from '~/hooks/useLoading';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { useAuthStore } from '~/store/useAuthStore';
import { useBusinessStore } from '~/store/useBusinessStore';

export function ProfilePicturePickerScreen(): ReactElement {
    const log = useLogger('ProfilePicturePickerScreen');
    const { t } = useTranslation();
    const profile = useImagePicker();
    const loader = useLoading();
    const user = useUser();
    const { params } = useRoute<RouteProp<RootStackParamList, 'ProfilePicturePickerScreen'>>();
    const notifications = useNotifications();
    const { navigate, goBack, canGoBack } = useNavigation();
    const userBusiness = useUserBusiness({
        enabled: !!params.forBusiness,
    });

    const resolveUser = useAuthStore((state) => state.resolveUser);
    const createBusiness = useBusinessStore((state) => state.createBusiness);

    const accountType = useMemo(
        () => params?.accountType ?? user?.accountType,
        [params?.accountType, user?.accountType],
    );

    const photoURL = useMemo(
        () => (params.forBusiness ? userBusiness.data?.profilePicture : user?.photoURL),
        [params.forBusiness, user?.photoURL, userBusiness.data?.profilePicture],
    );

    const handlePicture = useEvent(async (file: Optional<UploadFile>) => {
        if (!file) {
            return;
        }

        // TODO: find a better way to send files within the attachProfilePicture method.
        const data = new FormData();
        data.append('file', file as any, file.name);

        const args: [UploadFile, AxiosRequestConfig] = [
            file,
            {
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'multipart/form-data',
                },
                transformRequest: () => data,
            },
        ];

        if (params.forBusiness) {
            await businessEndpoint.attachProfilePicture(...args);
        } else {
            await usersEndpoint.attachProfilePicture(...args);
        }
    });

    const handleBusiness = useEvent(async () => {
        if (accountType !== AccountType.BusinessOwner || !params?.createBusiness) {
            return;
        }

        await createBusiness();
        logAnalyticsEvent(AnalyticsEvent.LeadApp, {
            email: user?.email || '',
            phoneNumber: user?.phoneNumber || '',
            accountType: 'BUSINESS_OWNER',
        });
    });

    const handlePressFinishAccount = useEvent(async () => {
        loader.start();

        const file = profile.asUploadFile();

        try {
            await handleBusiness();
            await handlePicture(file);

            await resolveUser();

            if (params.forBusiness) {
                await userBusiness.refetch();
            }

            if (params?.createBusiness) {
                navigate('ChooseSubscriptionPlanScreen', {
                    comingFromCreateBusiness: true,
                });
            } else if (params?.navigateBack && canGoBack()) {
                goBack();
            } else {
                navigate('MainScreen');
            }
        } catch (error) {
            log.error('something went wrong with user profile picture', { error });
            notifications.error(t('somethingWentWrong'));
        }

        loader.stop();
    });

    const handlePressNotNow = useCallback(async () => {
        if (params?.navigateBack && canGoBack()) {
            goBack();
            return;
        }

        await handlePressFinishAccount();
    }, [canGoBack, goBack, handlePressFinishAccount, params?.navigateBack]);

    return (
        <Screen disableScroll bgColor={'backgroundPrimary'}>
            <VStack safeTop={2} safeBottom={2} flex justifyContent={'space-between'}>
                <VStack alignItems={'flex-start'}>
                    <ScreenHeaderBackButton />
                    <Typography variant={'title1'} fontWeight={700} mt={2}>
                        {t('profilePicturePickerScreenHeadline')}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'} mt={1}>
                        {t('profilePicturePickerScreenDescription')}
                    </Typography>
                </VStack>

                <VStack alignItems={'center'}>
                    <Avatar
                        size={142}
                        source={profile.image ?? photoURL}
                        icon={<MaterialIcons name={'photo-camera'} />}
                    />
                    <Button
                        variant={'subtle'}
                        size={'small'}
                        color={'accent'}
                        label={t('uploadImage')}
                        mt={3}
                        onPress={profile.pick}
                        loading={loader.isLoading() || profile.loading}
                    />
                </VStack>

                <VStack>
                    <Button
                        label={params?.createBusiness ? t('finishAccount') : t('saveChanges')}
                        size={'large'}
                        color={'accent'}
                        mb={1}
                        disabled={!profile.image}
                        onPress={handlePressFinishAccount}
                        loading={loader.isLoading()}
                    />
                    <VStack opacity={loader.isLoading() ? 0 : 1}>
                        <Button label={t('notNow')} size={'large'} variant={'subtle'} onPress={handlePressNotNow} />
                    </VStack>
                </VStack>
            </VStack>
        </Screen>
    );
}
