import { RouteProp, StackActions, useNavigation, useRoute } from '@react-navigation/native';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from '~/RoutesParams';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { SelectableImageCard } from '../components/SelectableImageCard';

export function SelectEventTypeScreen() {
    const { t } = useTranslation();
    const navigation = useNavigation();
    const { params } = useRoute<RouteProp<RootStackParamList, 'SelectEventTypeScreen'>>();

    const handleCreateAppointment = useCallback(() => {
        navigation.dispatch(StackActions.replace('CreateAppointmentScreen', params || {}));
    }, [navigation, params]);

    const handleCreateBreak = useCallback(() => {
        navigation.dispatch(StackActions.replace('CreateBreakScreen', params || {}));
    }, [navigation, params]);

    return (
        <Screen>
            <ScreenHeader
                headline={t('selectEventTypeScreen.headline')}
                caption={t('selectEventTypeScreen.description')}
                leftSlot={<ScreenHeaderBackButton />}
            />

            <SelectableImageCard
                onPress={handleCreateAppointment}
                headline={t('selectEventTypeScreen.appointment')}
                description={t('selectEventTypeScreen.appointmentDescription')}
                image={require('~/assets/selectEventTypeScreen/appointmentBanner.png')}
            />

            <SelectableImageCard
                onPress={handleCreateBreak}
                headline={t('selectEventTypeScreen.break')}
                description={t('selectEventTypeScreen.breakDescription')}
                image={require('~/assets/selectEventTypeScreen/breakBanner.png')}
            />
        </Screen>
    );
}
