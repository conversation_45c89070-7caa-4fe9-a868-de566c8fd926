/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation } from '@react-navigation/native';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useQuery } from 'react-query';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { checkoutEndpoint } from '@bookr-technologies/api/endpoints/checkoutEndpoint';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { SubscriptionPlanPresentation } from '~/app/common/components/SubscriptionPlanPresentation';
import { StripeCheckoutModal } from '~/components/StripeCheckoutModal';
import { Button } from '~/components/ui/Button';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { makeStyles } from '~/components/ui/makeStyles';
import { PaymentSubscriptionCancel, PaymentSubscriptionSuccess } from '~/data/applicationData';
import * as subscriptionPlansData from '~/data/subscriptionPlansData';
import { useLoading } from '~/hooks/useLoading';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { useApplicationStore } from '~/store/useApplicationStore';

export function SubscriptionPlansScreen() {
    const styles = useStyles();
    const { t } = useTranslation();
    const notifications = useNotifications();
    const loading = useLoading();
    const { navigate } = useNavigation();
    const log = useLogger('SubscriptionPlansScreen');

    const [period] = useState<PeriodType>(PeriodType.Monthly);
    const [sessionId, setSessionId] = useState<string | null>(null);
    const [selectedPlan, setSelectedPlan] = useState(1);
    const pagerRef = useRef<PagerView>(null);

    const plans$ = useQuery('subscription/plans', () => checkoutEndpoint.getSubscriptionPlans({}));
    const plans = useMemo(
        () => SubscriptionPlanModel.getSortedPlansByPeriod(plans$.data ?? {}, period),
        [plans$.data, period],
    );

    const selectedPlanName = useMemo(() => plans[selectedPlan]?.planName ?? '', [plans, selectedPlan]);

    const subscriptions = useMemo(
        () =>
            plans.map((plan) => {
                const key = plan.planName.toLowerCase();

                return {
                    data: subscriptionPlansData[key as keyof typeof subscriptionPlansData] || {},
                    key,
                    plan,
                };
            }),
        [plans],
    );

    const handlePlanSelect = (page: number) => () => {
        setSelectedPlan(page);
        pagerRef.current?.setPage(page);
    };

    const handleSuccessPayment = useCallback(() => {
        useApplicationStore.getState().setSkipChooseSubscriptionPlan();
        navigate('MainScreen');
    }, [navigate]);

    const handlePress = useCallback(async () => {
        logAnalyticsEvent(AnalyticsEvent.PlanSelected, { plan: selectedPlanName });
        const { priceId } = plans[selectedPlan]?.price || {};
        if (!priceId) {
            notifications.error(t('somethingWentWrong'));
            return;
        }

        loading.start();

        try {
            const { sessionId } = await paymentEndpoint.createCheckoutSession({
                cancelUrl: PaymentSubscriptionCancel,
                priceId,
                successUrl: PaymentSubscriptionSuccess,
            });

            if (!sessionId) {
                handleSuccessPayment();
                return;
            }

            logAnalyticsEvent(AnalyticsEvent.CheckoutSessionCreated, { plan: selectedPlanName });

            setSessionId(sessionId);
        } catch (error) {
            log.error('Error creating session', { error });
            notifications.error(t('somethingWentWrong'));
        } finally {
            loading.stop();
        }
    }, [handleSuccessPayment, loading, log, notifications, plans, selectedPlan, selectedPlanName, t]);

    return (
        <>
            <Screen disablePadding bgColor={'backgroundSecondary'}>
                <ScreenHeader
                    px={3}
                    bgColor={'backgroundSecondary'}
                    headline={t('chooseSubscriptionPlan')}
                    leftSlot={<ScreenHeaderBackButton />}
                />
                <HStack py={2} px={3}>
                    {subscriptions.map(({ key }, index) => (
                        <Button
                            onPress={handlePlanSelect(index)}
                            key={key}
                            size={'small'}
                            variant={selectedPlan === index ? 'contained' : 'text'}
                            color={selectedPlan === index ? 'primary' : 'typography.textSecondary'}
                            label={t(`subscriptionPlans.${key}.name` as any)}
                        />
                    ))}
                </HStack>
                {plans$.isLoading ? (
                    <VStack alignItems={'center'} p={3}>
                        <CircularProgress size={30} />
                    </VStack>
                ) : null}
                {subscriptions.length > 0 ? (
                    <>
                        <PagerView
                            style={styles.pagerView}
                            initialPage={1}
                            onPageSelected={(event) => setSelectedPlan(event.nativeEvent.position)}
                            ref={pagerRef}
                        >
                            {subscriptions.map(({ key, plan, data }) => (
                                <View key={key} collapsable style={styles.pagerView}>
                                    <SubscriptionPlanPresentation
                                        planName={t(`subscriptionPlans.${key}.name` as any)}
                                        plan={plan}
                                        data={data}
                                    />
                                </View>
                            ))}
                        </PagerView>

                        <VStack pt={2} bgColor={'backgroundPrimary'} px={3}>
                            <HStack safeBottom>
                                <Button
                                    fullWidth
                                    size={'large'}
                                    color={'accent'}
                                    label={t('choosePlan', { plan: selectedPlanName.toLowerCase() })}
                                    onPress={handlePress}
                                    loading={loading.isLoading()}
                                />
                            </HStack>
                        </VStack>
                    </>
                ) : null}
            </Screen>
            <StripeCheckoutModal
                visible={!!sessionId}
                sessionId={sessionId}
                onClose={(): void => setSessionId(null)}
                onSuccess={handleSuccessPayment}
            />
        </>
    );
}

const useStyles = makeStyles(() => ({
    pagerView: {
        flex: 1,
    },
}));
