import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import AppointmentsBackground from '~/assets/intro/AppointmentsBackground.jpg';
import CalendarBackground from '~/assets/intro/CalendarBackground.jpg';
import CategoriesBackground from '~/assets/intro/CategoriesBackground.jpg';
import ForBusinessesBackground from '~/assets/intro/ForBusinessesBackground.jpg';
import PerformanceBackground from '~/assets/intro/PerformanceBackground.jpg';
import WelcomeToBookrBackground from '~/assets/intro/WelcomeBackground.jpg';
import { LoadingView } from '~/components/LoadingView';
import { Screen } from '~/components/ui/Screen';
import { useApplicationStore } from '~/store/useApplicationStore';
import { AppointmentsStory } from '../components/IntroBookrStories/AppointmentsStory';
import { CalendarStory } from '../components/IntroBookrStories/CalendarStory';
import { CategoriesStory } from '../components/IntroBookrStories/CategoriesStory';
import { ForBusinessesStory } from '../components/IntroBookrStories/ForBusinessesStory';
import { PerformanceStory } from '../components/IntroBookrStories/PerformanceStory';
import { WelcomeToBookrStory } from '../components/IntroBookrStories/WelcomeToBookrStory';
import { Story } from '../components/Story';
import { StoryConfig } from '../components/Story/StoryConfig';

export function IntroScreen(): ReactElement {
    const { t } = useTranslation();

    const visitedIntro = useApplicationStore((state) => state.visitedIntro);

    const stories = useMemo<StoryConfig[]>(
        () => [
            new StoryConfig(t('welcomeToBookr'), WelcomeToBookrStory, WelcomeToBookrBackground),
            new StoryConfig(t('appointments'), AppointmentsStory, AppointmentsBackground),
            new StoryConfig(t('categoriesLabel'), CategoriesStory, CategoriesBackground),
            new StoryConfig(t('forBusiness'), ForBusinessesStory, ForBusinessesBackground),
            new StoryConfig(t('calendar'), CalendarStory, CalendarBackground),
            new StoryConfig(t('performance'), PerformanceStory, PerformanceBackground),
        ],
        [t],
    );

    if (visitedIntro) {
        return <LoadingView />;
    }

    return (
        <Screen safeArea disablePadding bgColor={'primary'} statusBarStyle={'light-content'}>
            <Story stories={stories} />
        </Screen>
    );
}
