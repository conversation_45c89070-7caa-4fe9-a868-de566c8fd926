/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation } from '@react-navigation/native';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useApplicationStore } from '~/store/useApplicationStore';
import { useAuthStore } from '~/store/useAuthStore';
import { SelectableImageCard } from '../components/SelectableImageCard';

const accountTypes = [
    {
        description: 'choosePersonalDescription',
        image: require('~/assets/chooseAccountTypePersonal.png'),
        name: AccountType.Client,
    },
    {
        description: 'chooseBusinessDescription',
        image: require('~/assets/chooseAccountTypeBusiness.png'),
        name: AccountType.BusinessOwner,
    },
];

export function ChooseAccountTypeScreen() {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const updateUser = useAuthStore((state) => state.updateUser);
    const setVisitedIntro = useApplicationStore((state) => state.setVisitedIntro);

    const handlers = useMemo<Partial<Record<AccountType, () => void>>>(
        () => ({
            [AccountType.Client]: () => {
                updateUser({ accountType: AccountType.Client });
                navigate('ProfilePicturePickerScreen', { accountType: AccountType.Client });
            },
            [AccountType.BusinessOwner]: () => {
                setVisitedIntro(false);
                navigate('IntroScreen');
            },
        }),
        [navigate, setVisitedIntro, updateUser],
    );

    const handleSelectAccount = useCallback(
        (accountType?: AccountType) => {
            if (accountType) {
                handlers[accountType]?.();
            }
        },
        [handlers],
    );

    return (
        <Screen safeTop disableScroll bgColor={'backgroundPrimary'}>
            <VStack>
                <Typography variant={'title1'} fontWeight={700} mt={5}>
                    {t('chooseAccountTypeHeadline')}
                </Typography>
                <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'} mt={2} mb={2}>
                    {t('chooseAccountTypeDescription')}
                </Typography>

                {accountTypes.map((account) => (
                    <SelectableImageCard<AccountType>
                        key={account.name}
                        onPress={handleSelectAccount}
                        value={account.name}
                        headline={t(`accountTypes.${account.name}` as any)}
                        description={t(account.description as any)}
                        image={account.image}
                    />
                ))}
            </VStack>
        </Screen>
    );
}
