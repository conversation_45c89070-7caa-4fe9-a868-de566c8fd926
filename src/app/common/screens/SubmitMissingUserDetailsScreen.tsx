import { useNavigation } from '@react-navigation/native';
import { Formik, FormikProps } from 'formik';
import React, { ReactElement, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from 'react-query';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { FormikCountryPicker } from '~/app/common/components/FormikCountryPicker';
import { FormikDatePicker } from '~/app/common/components/FormikDatePicker';
import { FormikSelectGender } from '~/app/common/components/FormikSelectGender';
import { FormikDebug } from '~/components/FormikDebug';
import { FormikButton } from '~/components/ui/Button';
import { UserInterestChip } from '~/components/ui/Chip';
import { HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useAuthStore } from '~/store/useAuthStore';

const initialValues = {
    dateOfBirth: '',
    country: '',
    city: '',
    gender: 'MALE',
    interests: [],
};

export function SubmitMissingUserDetailsScreen(): ReactElement {
    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const { t } = useTranslation();
    const user = useUser();
    const { navigate } = useNavigation();
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const logout = useAuthStore((state) => state.logout);
    const notifications = useNotifications();
    const { data, isLoading } = useQuery('userInterests', usersEndpoint.getInterests);
    const saveUserDetails = useMutation((values: typeof initialValues) => usersEndpoint.upsert(user?.uid, values), {
        onSuccess: async () => {
            notifications.success(t('thanksForHelpingUs'));
            await resolveUser();
            navigate('MainScreen');
        },
    });

    const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
    const [step, setStep] = useState<'userInterests' | 'demographics'>('userInterests');

    const handleInterestPress = (interest: string | null) => {
        if (!interest) return;
        let interests = selectedInterests;
        if (interests.includes(interest)) {
            interests = interests.filter((i) => i !== interest);
        } else {
            interests = [...interests, interest];
        }
        setSelectedInterests(interests);
        formikRef.current?.setFieldValue('interests', interests);
    };

    const handleSubmit = () => {
        if (step === 'userInterests') {
            setStep('demographics');
        } else {
            let values = formikRef.current?.values;
            if (!values) return;
            values = {
                ...values,
                dateOfBirth: values.dateOfBirth?.trim(),
                country: values.country?.trim(),
                city: values.city?.trim(),
                gender: values.gender?.trim(),
            };
            saveUserDetails.mutate(values);
        }
    };

    return (
        <Screen>
            <ScreenHeader
                disableSafeViewArea
                pt={4}
                leftSlot={
                    <IconButton mt={3} size={'large'} disablePadding onPress={logout}>
                        <Icon name={'logout'} color={'accent'} />
                    </IconButton>
                }
            />
            <Formik innerRef={formikRef} initialValues={initialValues} onSubmit={handleSubmit}>
                {({ values }) => (
                    <VStack flex>
                        <Typography variant={'title2'} fontWeight={700} pb={1}>
                            {t('helpUsImproveTheApp')}
                        </Typography>
                        <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} pb={3}>
                            {t('helpUsImproveTheAppDescription')}
                        </Typography>

                        {step === 'userInterests' && (
                            <>
                                <Typography variant={'callout'} fontWeight={700} pb={1.5}>
                                    {t('whyDoYouUseBookr')}
                                </Typography>

                                <HStack flex>
                                    {isLoading && <Typography>Loading...</Typography>}
                                    {!isLoading &&
                                        data &&
                                        data.map((interest) => (
                                            <UserInterestChip
                                                key={interest}
                                                label={t(`interests.${interest}` as any)}
                                                value={interest}
                                                checked={selectedInterests.includes(interest)}
                                                onChecked={handleInterestPress}
                                            />
                                        ))}
                                </HStack>
                            </>
                        )}
                        {step === 'demographics' && (
                            <VStack flex>
                                <FormikSelectGender name={'gender'} />
                                <FormikDatePicker label={t('dateOfBirth')} name={'dateOfBirth'} mt={2} />
                                <FormikCountryPicker my={2} name={'country'} label={t('country')} />
                                <FormikTextField label={t('city')} name={'city'} mb={2} />
                            </VStack>
                        )}
                        <FormikDebug />
                        <FormikButton
                            disabled={
                                (step === 'userInterests' && selectedInterests.length === 0) ||
                                (step === 'demographics' &&
                                    (!values.dateOfBirth || !values.country || !values.city || !values.gender)) ||
                                saveUserDetails.isLoading
                            }
                            color={'accent'}
                            size={'large'}
                            label={step === 'userInterests' ? t('continue') : t('save')}
                            mb={4}
                            loading={saveUserDetails.isLoading}
                        />
                    </VStack>
                )}
            </Formik>
        </Screen>
    );
}
