import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { TextInput } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { CreateReviewDTO } from '@bookr-technologies/api/dto/CreateReviewDTO';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { reviewsEndpoint } from '@bookr-technologies/api/endpoints/reviewsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { RootStackParamList } from '~/RoutesParams';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { Avatar } from '~/components/ui/Avatar';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { STARS } from '~/lib/utils/review';
import { useApplicationStore } from '~/store/useApplicationStore';

const REVIEW_TEXT_MAX_LENGTH = 255;

interface ReviewStarsProps {
    initialRating?: number;
    onRatingPress: (rating: number) => void;
}

interface ReviewStarProps {
    index: number;
    onPress: (index: number) => void;
    selected: boolean;
}

function ReviewStar({ onPress, index, selected }: ReviewStarProps) {
    const theme = useTheme();
    const starColor = selected ? theme.palette.warning.main : theme.palette.backgroundTertiary.main;

    const handleStarPress = useEvent(() => {
        onPress(index);
    });

    return (
        <IconButton disablePadding onPress={handleStarPress}>
            <MaterialIcons name={'star'} size={40} color={starColor} />
        </IconButton>
    );
}

function ReviewStars({ onRatingPress, initialRating }: ReviewStarsProps) {
    const { t } = useTranslation();

    const [rating, setRating] = useState(initialRating ?? 0);

    const handleStarPress = useEvent((index: number) => {
        setRating(index + 1);
        onRatingPress(index + 1);
    });

    return (
        <HStack justifyContent={'space-between'} fullWidth px={2} pt={2.25}>
            {STARS.map((_, index) => (
                <VStack key={`reviewStar${index}`}>
                    <ReviewStar onPress={handleStarPress} index={index} selected={index < rating} />
                    {(index === 0 || index === 4) && (
                        <Typography variant={'caption2'} textAlign={'center'} fontWeight={500} color={'textSecondary'}>
                            {index === 0 ? t('bad') : t('good')}
                        </Typography>
                    )}
                </VStack>
            ))}
        </HStack>
    );
}

export function ReviewScreen() {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const theme = useTheme();
    const route = useRoute<RouteProp<RootStackParamList, 'ReviewScreen'>>();
    const { appointmentId } = route.params;
    const [showReviewSuccess, setShowReviewSuccess] = useState(false);
    const [isTextAreaFocused, setTextAreaFocused] = useState(false);
    const [comment, setComment] = useState('');
    const [rating, setRating] = useState(0);
    const notifications = useNotifications();
    const { data: appointmentDetails } = useQuery('appointmentDetails', () =>
        appointmentsEndpoint.show<AppointmentModel>(appointmentId),
    );
    const textRef = useRef<TextInput>(null);

    const createReview = useMutation('createReview', (data: CreateReviewDTO) => reviewsEndpoint.createReview(data), {
        onError: (e) => {
            notifications.error(getErrorMessage(e));
        },
    });

    const handleButtonPress = useEvent(async () => {
        if (isTextAreaFocused) {
            textRef.current?.blur();
        } else {
            await createReview.mutateAsync({ appointmentId, comment, rating });
            setShowReviewSuccess(true);
        }
    });

    const handleFieldBlur = useEvent(() => {
        setTextAreaFocused(false);
    });

    const handleFieldFocus = useEvent(() => {
        setTextAreaFocused(true);
    });

    const handleSheetAction = useEvent(() => {
        setShowReviewSuccess(false);
        useApplicationStore.getState().deleteFromAppointmentsForReview(appointmentId);
        navigate('MainScreen');
    });

    return (
        <Screen>
            <ScreenHeader
                rightSlot={
                    <IconButton disablePadding onPress={handleSheetAction}>
                        <MaterialIcons name={'close'} />
                    </IconButton>
                }
            />
            <VStack pt={7.5} alignItems={'center'}>
                <Avatar size={128} source={appointmentDetails?.staff?.photoURL} />
                <Typography variant={'subhead'} fontWeight={500} mt={5} textAlign={'center'} color={'textSecondary'}>
                    {t('reservationDone')}
                </Typography>
                <Typography variant={'title1'} fontWeight={700} mt={1.5} px={2.5} textAlign={'center'}>
                    {t('yourExperienceWith', { displayName: appointmentDetails?.staff?.displayName })}
                </Typography>
                <ReviewStars onRatingPress={setRating} />
                <TextField
                    multiline
                    mt={4}
                    blurOnSubmit
                    textInputRef={textRef}
                    value={comment}
                    label={t('addComment')}
                    maxLength={REVIEW_TEXT_MAX_LENGTH}
                    onChangeText={setComment}
                    onBlur={handleFieldBlur}
                    onFocus={handleFieldFocus}
                    startAdornment={
                        <TextFieldAdornment variant={'start'}>
                            <MaterialIcons
                                name={'textsms'}
                                size={20}
                                color={
                                    isTextAreaFocused ? theme.palette.accent.main : theme.palette.contentTertiary.main
                                }
                            />
                        </TextFieldAdornment>
                    }
                />
            </VStack>
            {comment.length > 0 && (
                <Typography
                    variant={'footnote'}
                    fontWeight={500}
                    mt={0.75}
                    textAlign={'right'}
                    color={theme.palette.contentTertiary.main}
                >
                    {t('charactersLeft', { value: REVIEW_TEXT_MAX_LENGTH - comment.length })}
                </Typography>
            )}
            <VStack flex justifyContent={'flex-end'} safeBottom>
                <Button
                    loading={createReview.isLoading}
                    variant={'contained'}
                    size={'large'}
                    label={comment.length > 0 && isTextAreaFocused ? t('saveComment') : t('sendReview')}
                    onPress={handleButtonPress}
                    disabled={!rating}
                />
            </VStack>
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Success}
                marginInset={3}
                icon={<MaterialIcons name={'check-circle-outline'} size={53} color={'accent'} />}
                open={showReviewSuccess}
                headline={t('reviewSent')}
                primaryColor={'accent'}
                subHeadline={t('thanksBookr')}
                primaryText={t('gotIt')}
                onAction={handleSheetAction}
                onClose={handleSheetAction}
            />
        </Screen>
    );
}
