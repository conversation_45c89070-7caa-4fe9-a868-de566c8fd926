import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { WorkingProgramDay } from '~/components/WorkingProgramDay';
import { FormikButton } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { BusinessSignUpForm, useBusinessStore } from '~/store/useBusinessStore';

export function SignUpBusinessProgramScreen() {
    const signUpForm = useBusinessStore((state) => state.signUpForm);
    const patchSignUpForm = useBusinessStore((state) => state.patchSignUpForm);

    const { t } = useTranslation();
    const { navigate } = useNavigation();

    const handleSubmit = useCallback(
        async (values: Partial<BusinessSignUpForm>) => {
            patchSignUpForm(values);
            navigate('ProfilePicturePickerScreen', {
                accountType: AccountType.BusinessOwner,
                createBusiness: true,
                forBusiness: true,
            });
        },
        [navigate, patchSignUpForm],
    );

    return (
        <Formik enableReinitialize initialValues={signUpForm} onSubmit={handleSubmit}>
            <Screen disableScroll>
                <ScreenHeader
                    headline={t('signUpBusinessProgramHeadline')}
                    caption={t('signUpBusinessProgramDescription')}
                    leftSlot={<ScreenHeaderBackButton />}
                />

                <VStack flex safeBottom={2}>
                    <VStack flex mb={2} mx={-3}>
                        <VStack flex scrollable>
                            <WorkingProgramDay label={t('days.monday')} name={'workingProgram.monday'} divider />
                            <WorkingProgramDay label={t('days.tuesday')} name={'workingProgram.tuesday'} divider />
                            <WorkingProgramDay label={t('days.wednesday')} name={'workingProgram.wednesday'} divider />
                            <WorkingProgramDay label={t('days.thursday')} name={'workingProgram.thursday'} divider />
                            <WorkingProgramDay label={t('days.friday')} name={'workingProgram.friday'} divider />
                            <WorkingProgramDay label={t('days.saturday')} name={'workingProgram.saturday'} divider />
                            <WorkingProgramDay label={t('days.sunday')} name={'workingProgram.sunday'} />
                        </VStack>
                    </VStack>

                    <HStack>
                        <FormikButton color={'accent'} size={'large'} label={t('continue')} fullWidth />
                    </HStack>
                </VStack>
            </Screen>
        </Formik>
    );
}
