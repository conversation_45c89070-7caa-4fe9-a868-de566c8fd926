import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { AxiosError } from 'axios';
import { Formik } from 'formik';
import moment from 'moment';
import momentTimezone from 'moment-timezone';
import React, { ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking, Modal, Platform, RefreshControl, TouchableOpacity } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';
import { formatDate } from '@bookr-technologies/core';
import { BusinessApplicationNavigationProp, RootStackParamList } from '~/RoutesParams';
import { BottomSheet } from '~/components/BottomSheet';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { NotesList } from '~/components/NotesList/NotesList';
import { Avatar } from '~/components/ui/Avatar';
import { Badge } from '~/components/ui/Badge';
import { Button, FormikButton } from '~/components/ui/Button';
import { Container, Grid, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { FormikRadio } from '~/components/ui/Radio';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useBusiness } from '~/hooks/useBusiness';
import { useEvent } from '~/hooks/useEvent';
import { useLocalPushNotificationScheduler } from '~/hooks/useLocalPushNotificationScheduler';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { reactQueryClient } from '~/lib/clients/reactQuery';
import { AppointmentStatus, getAppointmentsBadgeProps, getAppointmentStatus } from '~/lib/utils/appointments';
import { deleteCalendarEvent } from '~/lib/utils/calendar';
import { str } from '~/lib/utils/string';
import { useBusinessStore } from '~/store/useBusinessStore';

const useStyles = makeStyles(({ theme }) => ({
    deleteAppointment: {
        height: 'auto',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.backgroundSecondary.main,
        borderWidth: 1,
        marginBottom: 16,
        paddingVertical: theme.mixins.spacingValue(2),
        paddingHorizontal: theme.mixins.spacingValue(2),
        marginHorizontal: theme.mixins.spacingValue(2),
        borderRadius: 12,
    },
}));

export function AppointmentDetailsScreen(): ReactElement {
    const logger = useLogger('AppointmentDetailsScreen');
    const { params } = useRoute<RouteProp<RootStackParamList, 'AppointmentDetailsScreen'>>();
    const { appointment, appointmentId, businessView, client } = params;
    const { t } = useTranslation();
    const theme = useTheme();
    const user = useUser();
    const { navigate, addListener } = useNavigation<BusinessApplicationNavigationProp<'ClientDetailsScreen'>>();
    const notifications = useNotifications();
    const styles = useStyles();
    const notificationScheduler = useLocalPushNotificationScheduler();
    const currentBusiness = useBusinessStore((state) => state.currentBusiness);

    const [isCancelling, setIsCancelling] = useState(false);
    const [isMoreOptionsOpen, setIsMoreOptionsOpen] = useState(false);

    const { data, isLoading, refetch } = useQuery(
        ['appointments', appointment?.id || appointmentId],
        () => {
            const id = appointment?.id || appointmentId;

            if (!id) {
                throw new Error('not_found');
            }

            return appointmentsEndpoint.show<AppointmentModel>(id);
        },
        {
            initialData: () => appointment || reactQueryClient.getQueryData(['appointments', appointmentId]),
        },
    );

    const business = useBusiness(data?.staff?.business.id);

    const createNoteMutation = useMutation(
        'createNote',
        async (text: string) => {
            const id = appointment?.id || appointmentId;

            if (!id) {
                notifications.error(t('somethingWentWrong'));
                return;
            }

            return appointmentsEndpoint.createNote(id, { text });
        },
        {
            onSuccess: () => {
                notifications.success(t('noteCreated'));
            },
        },
    );

    const updateNoteMutation = useMutation(
        'updateNote',
        async (note: NoteModel) => {
            const id = appointment?.id || appointmentId;

            if (!id) {
                notifications.error(t('somethingWentWrong'));
                return;
            }

            return appointmentsEndpoint.updateNote(id, note);
        },
        {
            onSuccess: () => {
                notifications.success(t('noteUpdated'));
            },
        },
    );

    const isCancelled = !!data?.cancelled;
    const isBusinessView = !!businessView;

    const appointmentStatus = useMemo(() => getAppointmentStatus(data), [data]);
    const statusBadge = useMemo(() => <Badge {...getAppointmentsBadgeProps(t, data)} />, [data, t]);

    const handleCancel = useEvent(() => {
        setIsMoreOptionsOpen(false);
        setIsCancelling(true);
    });

    const handleCloseCancel = useEvent(() => setIsCancelling(false));

    const cancelAppointmentMutation = useMutation(
        'cancelAppointment',
        async ({ everyRecurrence }: { everyRecurrence: boolean }) => {
            if (!data?.id) {
                throw new Error('Appointment not found');
            }
            return appointmentsEndpoint.cancelAppointment(data.id, everyRecurrence);
        },
        {
            onError: (error) => {
                logger.error('error cancelling appointment', { error });
                if ((error as AxiosError).response?.status === 403) {
                    notifications.error(t('cannotCancelAppointmentAnymore'));
                }
            },
            onSuccess: async () => {
                await refetch();
                await deleteCalendarEvent(String(data?.id));
                await notificationScheduler.cancelAppointmentReviewPushNotification(data!);
            },
        },
    );

    const handleSubmitAddNotes = useCallback(
        async (note: string) => {
            if (!data || !data.id) {
                return;
            }
            await createNoteMutation.mutateAsync(note);
            await refetch();
        },
        [createNoteMutation, data, refetch],
    );

    const handleSubmitEditNote = useCallback(
        async (note: NoteModel) => {
            updateNoteMutation.mutate(note);
            await refetch();
        },
        [refetch, updateNoteMutation],
    );

    const handlePressMoreOptions = useEvent(() => {
        setIsMoreOptionsOpen(true);
    });

    const handleCloseMoreOptions = useEvent(() => {
        setIsMoreOptionsOpen(false);
    });

    const handlePressCall = useEvent(() => {
        const { phoneNumber } = (isBusinessView ? data?.client : data?.staff) ?? {};

        if (!phoneNumber) {
            notifications.error(t('weCouldNotFindAnyPhoneNumberForThisAppointment'));
            return;
        }

        return Linking.openURL(`tel:${phoneNumber}`);
    });

    const handlePressMessage = useEvent(() => {
        const { phoneNumber } = (isBusinessView ? data?.client : data?.staff) ?? {};
        const separator = Platform.OS === 'ios' ? '&' : '?';

        if (!phoneNumber) {
            notifications.error(t('weCouldNotFindAnyPhoneNumberForThisAppointment'));
            return;
        }

        return Linking.openURL(`sms:${phoneNumber}${separator}body=`);
    });

    const handlePressMail = useEvent(() => {
        const { email, displayName } = (isBusinessView ? data?.client : data?.staff) ?? {};

        const subject = `BOOKR - ${displayName}`;
        const message = '';

        if (!email) {
            notifications.error(t('weCouldNotFindAnyEmailForThisAppointment'));
            return;
        }

        return Linking.openURL(`mailto:${email}?subject=${subject}&body=${message}`);
    });

    const handleVisitBusinessProfile = useEvent(() => {
        if (!data?.staff?.business) {
            notifications.error(t('weCouldNotFindAnyBusinessForThisAppointment'));
            return;
        }

        navigate('BusinessProfileScreen', {
            businessId: data.staff.business.id,
        });
    });

    const handleOnStaffOrClientPress = useCallback(() => {
        if (isBusinessView) {
            data?.client?.uid && navigate('ClientDetailsScreen', { clientId: data.client.uid });
        } else {
            handleVisitBusinessProfile();
        }
    }, [data?.client?.uid, handleVisitBusinessProfile, isBusinessView, navigate]);

    const handleViewLocationPress = useEvent(() => {
        if (!data?.staff?.business) {
            notifications.error(t('weCouldNotFindAnyBusinessForThisAppointment'));
            return;
        }

        navigate('BusinessLocationScreen', {
            business: data.staff.business,
        });
    });

    const handleMeetingLinkPress = useEvent(() => {
        if (!data?.onlineEvent?.joinUrl) {
            return;
        }
        Linking.openURL(data.onlineEvent.joinUrl);
    });

    const handleImReady = async () => {
        if (data?.id) {
            await appointmentsEndpoint.imReady(data.id);
            notifications.info(t('imReadySent'));
        }
    };

    const handleEditAppointment = useEvent(() => {
        if (!data?.staff?.business) {
            notifications.error(t('weCouldNotFindAnyBusinessForThisAppointment'));
            return;
        }

        handleCloseMoreOptions();
        if (
            user?.uid === data?.staff?.uid ||
            currentBusiness?.staffMembers?.find((staff) => staff.uid === data?.staff?.uid)
        ) {
            navigate('CreateAppointmentScreen', { appointment: data });
        } else {
            navigate('EditAppointmentScreen', { appointment: data });
        }
    });

    const handleAbsentClient = useEvent(async () => {
        if (data?.id) {
            await appointmentsEndpoint.noShow(data.id);
            refetch();
            handleCloseMoreOptions();
        }
    });

    useEffect(() => {
        return addListener('focus', () => !isLoading && refetch());
    }, [addListener, isLoading, refetch]);

    const staffMemberOrClient$ = (
        <>
            <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} mb={0.5}>
                {isBusinessView ? t('clientDetails') : t('staffMember')}
            </Typography>
            <TouchableOpacity activeOpacity={1} onPress={handleOnStaffOrClientPress}>
                <Paper direction={'row'} bgColor={'backgroundPrimary'} px={3} py={2}>
                    <Avatar
                        size={40}
                        source={isBusinessView ? data?.client?.photoURL : data?.staff?.photoURL}
                        name={isBusinessView ? data?.client?.displayName : data?.staff.displayName}
                    />
                    <VStack ml={2}>
                        <Typography fontWeight={700}>
                            {isBusinessView ? str(data?.client?.displayName) : str(data?.staff?.displayName)}
                        </Typography>
                        <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                            {isBusinessView
                                ? data?.client?.phoneNumber || data?.client?.email || ''
                                : data?.staff?.business?.name}
                        </Typography>
                    </VStack>
                </Paper>
            </TouchableOpacity>
            <HStack mt={1} mb={4} flexWrap={'nowrap'} mx={-0.5}>
                <Grid xs>
                    <Button
                        onPress={handlePressCall}
                        activeOpacity={0.75}
                        size={'small'}
                        color={'backgroundPrimary'}
                        iconColor={'accent'}
                        textColor={'typography.textPrimary'}
                        variant={'subtle'}
                        startIcon={<MaterialIcons name={'phone-in-talk'} />}
                        label={t('call')}
                        pr={1.5}
                        mx={0.5}
                    />
                </Grid>
                <Grid xs>
                    <Button
                        onPress={handlePressMessage}
                        activeOpacity={0.75}
                        size={'small'}
                        color={'backgroundPrimary'}
                        iconColor={'accent'}
                        textColor={'typography.textPrimary'}
                        variant={'subtle'}
                        startIcon={<MaterialIcons name={'textsms'} />}
                        label={t('message')}
                        pr={1.5}
                        mx={0.5}
                    />
                </Grid>
                <Grid xs>
                    <Button
                        onPress={handlePressMail}
                        activeOpacity={0.75}
                        size={'small'}
                        color={'backgroundPrimary'}
                        iconColor={'accent'}
                        textColor={'typography.textPrimary'}
                        variant={'subtle'}
                        startIcon={<MaterialIcons name={'email'} />}
                        label={t('mail')}
                        pr={1.5}
                        mx={0.5}
                    />
                </Grid>
            </HStack>
        </>
    );

    let appointmentDateTimeInBusinessTimezone = '';
    let headlineDateTime = '';
    if (!!business?.data?.zoneId && !!data?.dateTime) {
        appointmentDateTimeInBusinessTimezone = momentTimezone
            .tz(data.dateTime, 'UTC')
            .tz(business.data.zoneId)
            .format('lll');

        headlineDateTime = moment(appointmentDateTimeInBusinessTimezone, 'lll').format('DD MMM, HH:mm');
    }

    const appointmentDetails$ = (
        <>
            <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} mb={0.5}>
                {t('appointmentDetails')}
            </Typography>
            <Paper bgColor={'backgroundPrimary'} px={2} py={1} mb={2}>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('status')}:
                    </Typography>
                    {statusBadge}
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('businessName')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {str(data?.staff?.business?.name)}
                    </Typography>
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('serviceName')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {str(data?.service?.name)}
                    </Typography>
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('price')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {str(data?.service?.price)} {str(data?.service.currency)}
                    </Typography>
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('paidOnline')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {data?.paidOnline ? t('yes') : t('no')}
                    </Typography>
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('isRecurrence')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {data?.recurrent ? t('yes') : t('no')}
                    </Typography>
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('dateAndTime')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {appointmentDateTimeInBusinessTimezone || ''}
                    </Typography>
                </HStack>
                <HStack py={1} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700} mr={1}>
                        {t('createdAt')}:
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {formatDate(data?.createdAt ?? '', 'lll')}
                    </Typography>
                </HStack>
            </Paper>
        </>
    );

    const clientNotes$ = (
        <NotesList createNote={handleSubmitAddNotes} editNote={handleSubmitEditNote} notes={data?.notes} />
    );

    const businessControls$ = (
        <VStack bgColor={'backgroundPrimary'}>
            <Container safeBottom={2} pt={2} disableSafeAreaFlex container>
                <Grid xs mr={0.5}>
                    <Button
                        disabled={data?.cancelled || data?.noShow}
                        label={t('imReady')}
                        color={'accent'}
                        onPress={handleImReady}
                    />
                </Grid>
                <Grid xs ml={0.5}>
                    <Button
                        label={t('moreOptions')}
                        color={'accent'}
                        variant={'subtle'}
                        endIcon={<MaterialIcons name={'keyboard-arrow-down'} />}
                        onPress={handlePressMoreOptions}
                    />
                </Grid>
            </Container>
        </VStack>
    );

    if (isLoading || business.isLoading) {
        return <Screen loading />;
    }

    if (!client) {
        return <NoClientSelectedDisplay appointment={data} />;
    }

    return (
        <Screen bgColor={'backgroundSecondary'} disableScroll disablePadding>
            <Container>
                <ScreenHeader
                    leftSlot={<ScreenHeaderBackButton />}
                    headline={`${str(data?.service?.name)}, ${headlineDateTime}`}
                    headlineTypographyProps={{ variant: 'title2' }}
                    caption={data?.staff?.business?.name}
                    bgColor={'backgroundSecondary'}
                >
                    <HStack mt={1}>
                        {!isCancelled &&
                            !isBusinessView &&
                            appointmentStatus !== AppointmentStatus.Completed &&
                            !data?.onlineEvent?.joinUrl && (
                                <Button
                                    onPress={handleViewLocationPress}
                                    size={'small'}
                                    color={'accent'}
                                    startIcon={<MaterialIcons name={'location-on'} />}
                                    label={t('viewLocation')}
                                    pr={1.5}
                                    mr={1}
                                />
                            )}
                        {!isCancelled && data?.onlineEvent?.joinUrl && (
                            <Button
                                onPress={handleMeetingLinkPress}
                                size={'small'}
                                color={'accent'}
                                startIcon={<MaterialIcons name={'videocam'} />}
                                label={t('meetingLink')}
                                pr={1.5}
                                mr={1}
                            />
                        )}
                        {!isCancelled && appointmentStatus !== AppointmentStatus.Completed && (
                            <Button
                                size={'small'}
                                color={'accent'}
                                variant={'subtle'}
                                startIcon={<MaterialCommunityIcons name={'calendar-edit'} />}
                                label={t('editAppointment')}
                                pr={1.5}
                                onPress={handleEditAppointment}
                            />
                        )}

                        {!isBusinessView && (isCancelled || appointmentStatus === AppointmentStatus.Completed) && (
                            <Button
                                size={'small'}
                                color={'accent'}
                                startIcon={<MaterialIcons name={'replay-circle-filled'} />}
                                label={t('bookAgain')}
                                pr={1.5}
                                onPress={handleVisitBusinessProfile}
                            />
                        )}
                    </HStack>
                </ScreenHeader>
            </Container>

            <VStack flex>
                <VStack
                    flex
                    scrollable
                    scrollViewProps={{ refreshControl: <RefreshControl onRefresh={refetch} refreshing={isLoading} /> }}
                >
                    <Container flexShrink={0} flexWrap={false}>
                        {staffMemberOrClient$}
                        {appointmentDetails$}
                        {isBusinessView && clientNotes$}

                        {!isCancelled && appointmentStatus !== AppointmentStatus.Completed && !isBusinessView && (
                            <Button
                                label={t('cancel')}
                                variant={'subtle'}
                                color={'error'}
                                size={'large'}
                                onPress={handleCancel}
                            />
                        )}
                    </Container>
                </VStack>

                {isBusinessView && businessControls$}
            </VStack>

            {!isCancelled && !data?.recurrent && (
                <BottomSheetAlert
                    variant={BottomSheetAlertVariant.Error}
                    open={isCancelling}
                    onClose={handleCloseCancel}
                    headline={t('cancelAppointmentTitle')}
                    subHeadline={`${str(data?.staff?.business?.name)}, ${str(
                        data?.service?.name,
                    )}, ${headlineDateTime}`}
                    onCancel={handleCloseCancel}
                    onAction={() => cancelAppointmentMutation.mutateAsync({ everyRecurrence: false })}
                />
            )}

            {!isCancelled && data?.recurrent && (
                <Modal visible={isCancelling} animationType={'slide'}>
                    <ScreenHeader
                        pl={3}
                        pt={3}
                        leftSlot={
                            <IconButton disablePadding pt={3} onPress={handleCloseCancel}>
                                <MaterialIcons name={'arrow-back'} />
                            </IconButton>
                        }
                    ></ScreenHeader>
                    <VStack safeArea flex={1} justifyContent={'center'} alignItems={'center'}>
                        <Avatar
                            icon={<MaterialIcons name={'cancel'} />}
                            iconSize={40}
                            iconColor={'error'}
                            size={80}
                            borderRadius={24}
                            bgColor={'backgroundSecondary'}
                        />
                        <Typography
                            variant={'title2'}
                            fontWeight={700}
                            fontSize={22}
                            pt={2}
                            width={'75%'}
                            textAlign={'center'}
                        >
                            {t('deleteRecurrentAppointmentTitle')}
                        </Typography>
                        <Typography variant={'footnote'} color={'textSecondary'} pt={2}>
                            {data?.service.name}, {headlineDateTime}
                        </Typography>
                        <Formik
                            initialValues={{
                                everyRecurrence: false,
                            }}
                            onSubmit={(values) =>
                                cancelAppointmentMutation.mutateAsync({ everyRecurrence: values.everyRecurrence })
                            }
                        >
                            {() => (
                                <VStack pt={3} justifyContent={'center'}>
                                    <FormikRadio
                                        style={styles.deleteAppointment}
                                        name={'everyRecurrence'}
                                        value={false}
                                        label={
                                            <Typography fontSize={15} variant={'subhead'} fontWeight={500} ml={1.4}>
                                                {t('deleteThisAppointment')}
                                            </Typography>
                                        }
                                    />
                                    <FormikRadio
                                        style={styles.deleteAppointment}
                                        name={'everyRecurrence'}
                                        value={true}
                                        label={
                                            <Typography fontSize={15} variant={'subhead'} fontWeight={500} ml={1.4}>
                                                {t('deleteEveryRecurrence')}
                                            </Typography>
                                        }
                                    />

                                    <Grid mt={8} mx={2} flexDirection={'row'} alignSelf={'flex-end'}>
                                        <FormikButton
                                            fullWidth
                                            color={'primary'}
                                            label={t('delete')}
                                            size={'large'}
                                            loading={cancelAppointmentMutation.isLoading}
                                        />
                                    </Grid>
                                </VStack>
                            )}
                        </Formik>
                    </VStack>
                </Modal>
            )}

            <BottomSheet open={isMoreOptionsOpen} onClose={handleCloseMoreOptions} snapPoints={[380]}>
                <Container pt={2}>
                    <Typography variant={'title2'} fontWeight={700} mb={3}>
                        {t('moreOptions')}
                    </Typography>

                    <Button
                        startIcon={<MaterialCommunityIcons name={'account-cancel'} />}
                        label={t('absentClient')}
                        align={'flex-start'}
                        size={'large'}
                        color={'primary'}
                        variant={'subtle'}
                        mb={2}
                        disabled={data?.noShow}
                        onPress={handleAbsentClient}
                    />
                    <Button
                        startIcon={<MaterialCommunityIcons name={'calendar-edit'} />}
                        label={t('editAppointment')}
                        align={'flex-start'}
                        size={'large'}
                        color={'primary'}
                        variant={'subtle'}
                        mb={2}
                        onPress={handleEditAppointment}
                    />
                    <Button
                        startIcon={
                            <MaterialIcons
                                name={'cancel'}
                                color={!isCancelled ? theme.palette.error.main : theme.palette.typography.disabled}
                            />
                        }
                        label={t('cancelAppointment')}
                        align={'flex-start'}
                        size={'large'}
                        color={'primary'}
                        variant={'subtle'}
                        disabled={isCancelled}
                        onPress={handleCancel}
                    />
                </Container>
            </BottomSheet>
        </Screen>
    );
}

const NoClientSelectedDisplay = ({ appointment }: { appointment: AppointmentModel }) => {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const appointmentClients= appointment.appointmentClients;

    return (
    );
};
