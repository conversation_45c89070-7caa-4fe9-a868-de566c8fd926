import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Octicons from '@expo/vector-icons/Octicons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { transparentize } from 'polished';
import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { businessEndpoint } from '@bookr-technologies/api';
import { reviewsEndpoint } from '@bookr-technologies/api/endpoints/reviewsEndpoint';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { BottomSheet } from '~/components/BottomSheet';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Link } from '~/components/ui/Link/Link';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { getBusinessPhotos } from '~/components/utils/business';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { convertBusinessToSearchBusinessModel } from '~/lib/utils/business';
import { useApplicationStore } from '~/store/useApplicationStore';
import { useFavouritesStore } from '~/store/useFavouritesStore';
import { BusinessProfileActions } from '../components/BusinessProfileActions/BusinessProfileActions';
import { LottieFavouriteIcon } from '../components/LottieFavouriteIcon/LottieFavouriteIcon';
import { ReportBusiness } from '../components/ReportBusiness/ReportBusiness';
import { Services } from '../components/Services/Services';
import { SocialMediaCard } from '../components/SocialMediaCard/SocialMediaCard';
import { Photos } from '../components/SwiperPhotos/SwiperPhotos';
import { BusinessReviewsBlock } from './BusinessReviewsBlock';

interface BusinessDetailsScreenProps {
    businessId: string;
}

const useStyles = makeStyles(({ theme }) => ({
    favourite: {
        backgroundColor: transparentize(0, theme.palette.backgroundPrimary.main),
        borderRadius: 8,
        height: 36,
        width: 36,
    },
    readMore: {
        textDecorationColor: theme.palette.primary.main,
        textDecorationLine: 'underline',
    },
}));

export function BusinessProfileScreen(): ReactElement | null {
    const route = useRoute();
    const styles = useStyles();
    const theme = useTheme();
    const { t } = useTranslation();
    const logger = useLogger('BusinessProfileScreen');
    const { navigate, goBack } = useNavigation();
    const { businessId = '' } = route.params as BusinessDetailsScreenProps;
    const { data: business, isLoading } = useQuery(['business', businessId], () => businessEndpoint.show(businessId));
    const { data: reviews } = useQuery(
        'businessReviews',
        () => reviewsEndpoint.fetchData({ businessId: businessId, page: 0, size: 3 }),
        { enabled: !!businessId },
    );

    const favourites = useFavouritesStore((state) => state.favourites);
    const resolveFavourites = useFavouritesStore((state) => state.resolveFavourites);
    const isFavourite = favourites.find((f) => f.id === business?.id);
    const notifications = useNotifications();
    const addLatestBusinessVisited = useApplicationStore((state) => state.addLatestBusinessVisited);

    const [readMoreOpen, setReadMoreOpen] = useState(false);

    useEffect(() => {
        if (business && !business.hidden) {
            addLatestBusinessVisited(convertBusinessToSearchBusinessModel(business));
            businessEndpoint.visitBusiness(business.id);
            logAnalyticsEvent(AnalyticsEvent.BusinessViewed, {
                businessId: business.id,
                businessName: business.name,
                phoneNumber: business.phoneNumber,
            });
        } else if (business && business.hidden) {
            goBack();
            notifications.info(t('businessHidden'));
        }
    }, [addLatestBusinessVisited, business, goBack, notifications, t]);

    const favouriteMutation = useMutation(
        'favouriteBusiness',
        () => {
            if (!business) {
                return Promise.resolve([]);
            }
            if (isFavourite) {
                return usersEndpoint.removeFavourite(business.id);
            }
            return usersEndpoint.addFavourite(business.id);
        },
        {
            onError: (error) => {
                logger.error('error toggle favourite', { businessId: business?.id, error });
                notifications.error(t('favouriteError'));
            },
            onSuccess: () => {
                resolveFavourites();
                if (!isFavourite) {
                    notifications.success(t('favouriteAdded'));
                } else {
                    notifications.info(t('favouriteRemoved'));
                }
            },
        },
    );

    const handleOnFavouritePress = useEvent((): void => {
        favouriteMutation.mutate();
    });

    if (isLoading || !business) {
        return (
            <Screen disablePadding bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
                <ScreenHeader
                    px={3}
                    bgColor={'backgroundPrimary'}
                    leftSlot={
                        <Grid bgColor={'backgroundPrimary'} p={1} borderRadius={8}>
                            <ScreenHeaderBackButton />
                        </Grid>
                    }
                    rightSlot={
                        <Grid style={styles.favourite} alignItems={'center'} justifyContent={'center'}>
                            <LottieFavouriteIcon isFavourite={!!isFavourite} onPress={handleOnFavouritePress} />
                        </Grid>
                    }
                />
                <VStack alignItems={'center'} justifyContent={'center'} pt={3}>
                    <CircularProgress spinDuration={500} />
                </VStack>
            </Screen>
        );
    }

    if (business?.hidden) {
        return null;
    }

    const handleReadMore = (): void => setReadMoreOpen(true);

    const handleReadMoreClose = (): void => setReadMoreOpen(false);

    const handleSeeReviewsPress = (): void => {
        navigate('BusinessReviewsDetailsScreen', {
            businessDisplayName: business.name,
            businessImageURL: business.profilePicture,
            reviewsInfo: business.reviewInfo,
            businessId,
        });
    };

    const services = business.staffMembers
        .map((staffMember) => staffMember.services)
        .flat()
        .filter(({ hiddenFromClients }) => !hiddenFromClients);

    return (
        <Screen disablePadding bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
            <ScreenHeader
                px={3}
                bgColor={'backgroundPrimary'}
                leftSlot={
                    <Grid bgColor={'backgroundPrimary'} p={1} borderRadius={8}>
                        <ScreenHeaderBackButton />
                    </Grid>
                }
                rightSlot={
                    <Grid style={styles.favourite} alignItems={'center'} justifyContent={'center'}>
                        <LottieFavouriteIcon isFavourite={!!isFavourite} onPress={handleOnFavouritePress} />
                    </Grid>
                }
            />
            <Grid safeBottom>
                <Photos photos={getBusinessPhotos(business)} />
                <Grid flex px={3} pt={1}>
                    <Typography variant={'title3'} fontWeight={700} fontSize={22} lineHeight={28}>
                        {business.name}
                    </Typography>
                    {services.length > 0 && (
                        <HStack mt={1} alignItems={'center'}>
                            <HStack
                                borderRadius={8}
                                p={1}
                                justifyContent={'center'}
                                alignItems={'center'}
                                bgColor={'#e2e2e2'}
                                mr={1}
                            >
                                <MaterialIcons name={'event-available'} size={16} color={theme.palette.primary.main} />
                                <Typography
                                    textTransform={'capitalize'}
                                    pl={0.5}
                                    variant={'caption2'}
                                    fontWeight={500}
                                    color={'primary'}
                                >
                                    {t('instantBooking')}
                                </Typography>
                            </HStack>
                            {business?.reviewInfo?.noOfReviews > 0 && (
                                <HStack
                                    borderRadius={8}
                                    p={1}
                                    justifyContent={'center'}
                                    alignItems={'center'}
                                    bgColor={'#e2e2e2'}
                                    mr={1}
                                >
                                    <IconButton disablePadding mr={1} disabled>
                                        <MaterialIcons name={'star'} size={16} color={`primary`} />
                                    </IconButton>
                                    <Typography variant={'caption2'} fontWeight={500}>
                                        {business.reviewInfo.averageRating.toFixed(2).slice(0, 3)}
                                    </Typography>
                                    <Typography
                                        fontSize={4}
                                        color={'primary'}
                                        variant={'caption2'}
                                        fontWeight={500}
                                        mx={0.5}
                                    >
                                        ●
                                    </Typography>
                                    <TypographyLink
                                        variant={'caption2'}
                                        fontWeight={500}
                                        color={'primary'}
                                        onPress={handleSeeReviewsPress}
                                        style={{ textDecorationLine: 'underline' }}
                                    >
                                        {t('reviewsNumber', { reviews: business.reviewInfo.noOfReviews })}
                                    </TypographyLink>
                                </HStack>
                            )}
                        </HStack>
                    )}
                    <Grid flexDirection={'row'} alignItems={'center'} mt={2}>
                        <Octicons name={'location'} size={16} color={'#757575'} />
                        <Typography ml={1} fontWeight={500} fontSize={13} variant={'footnote'} color={'textSecondary'}>
                            {business.formattedAddress}
                        </Typography>
                    </Grid>
                    {business.description ? (
                        <Paper mt={2} bgColor={'backgroundPrimary'} p={2}>
                            <Grid flexDirection={'row'}>
                                <Typography
                                    color={'textSecondary'}
                                    variant={'footnote'}
                                    fontSize={13}
                                    fontWeight={500}
                                    lineHeight={19}
                                    numberOfLines={3}
                                >
                                    {business.description}
                                </Typography>
                            </Grid>
                            <TypographyLink
                                fontWeight={500}
                                mt={2}
                                fontSize={13}
                                color={'primary'}
                                style={styles.readMore}
                                onPress={handleReadMore}
                            >
                                {t('readMore')}
                            </TypographyLink>
                        </Paper>
                    ) : null}
                </Grid>
                <BusinessProfileActions business={business} />
                {/* virtualTourURL must be add on BE on SearchBusinessModel */}
                {business.virtualTourURL ? (
                    <Link href={business.virtualTourURL}>
                        <Paper
                            bgColor={'backgroundPrimary'}
                            flex
                            mt={2}
                            mx={3}
                            py={1.5}
                            px={2}
                            flexDirection={'row'}
                            alignItems={'center'}
                        >
                            <Icon variant={'secondary'} name={'cube-scan'} color={'accent'} />
                            <Typography variant={'footnote'} fontWeight={500} ml={1.5}>
                                {t('explore3DTour')}
                            </Typography>
                        </Paper>
                    </Link>
                ) : null}

                <Services business={business} />
                {business?.reviewInfo?.noOfReviews > 0 && (
                    <BusinessReviewsBlock
                        reviews={reviews?.content}
                        reviewsInfo={business.reviewInfo}
                        handleSeeReviewsPress={handleSeeReviewsPress}
                        importedReviews={services.length === 0}
                    />
                )}
                <SocialMediaCard business={business} />
                <ReportBusiness business={business} />
                <BottomSheet snapPoints={['90%']} open={readMoreOpen} onClose={handleReadMoreClose}>
                    <Paper bgColor={'backgroundPrimary'} flex safeBottom={2}>
                        <Grid pb={2} px={3}>
                            <Typography variant={'title1'} fontWeight={700}>
                                {t('aboutUs')}
                            </Typography>
                        </Grid>
                        <Grid scrollable flex flexDirection={'row'} px={3}>
                            <Typography color={'textSecondary'} variant={'subhead'} fontWeight={500} lineHeight={24}>
                                {business.description}
                            </Typography>
                        </Grid>
                    </Paper>
                </BottomSheet>
            </Grid>
        </Screen>
    );
}
