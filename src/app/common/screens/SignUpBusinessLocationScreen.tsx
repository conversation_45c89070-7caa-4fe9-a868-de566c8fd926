import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import { ReactElement, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';
import { FormikLocationMapView } from '~/app/common/components/FormikLocationMapView';
import { FormikButton } from '~/components/ui/Button';
import { HStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { BusinessSignUpForm, useBusinessStore } from '~/store/useBusinessStore';

export function SignUpBusinessLocationScreen(): ReactElement {
    const signUpForm = useBusinessStore((state) => state.signUpForm);
    const patchSignUpForm = useBusinessStore((state) => state.patchSignUpForm);
    const [loading, setLoading] = useState(false);
    const { navigate } = useNavigation();

    const { t } = useTranslation();
    const validation = useValidationSchema({
        formattedAddress: Yup.string().required(t('requiredField')),
    });

    const handleSubmit = useCallback(
        async (values: Partial<BusinessSignUpForm>) => {
            patchSignUpForm(values);
            navigate('SignUpBusinessProgramScreen');
        },
        [navigate, patchSignUpForm],
    );

    return (
        <Formik validateOnMount enableReinitialize initialValues={signUpForm} onSubmit={handleSubmit} {...validation}>
            <Screen disableScroll>
                <ScreenHeader headline={t('signUpBusinessLocationHeadline')} leftSlot={<ScreenHeaderBackButton />} />
                <FormikLocationMapView onLoading={setLoading}>
                    <HStack>
                        <FormikButton
                            color={'accent'}
                            size={'large'}
                            label={t('continue')}
                            fullWidth
                            loading={loading}
                        />
                    </HStack>
                </FormikLocationMapView>
            </Screen>
        </Formik>
    );
}
