import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { sendEmailVerification } from 'firebase/auth';
import { ReactElement, useCallback, useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components/native';
import { RootStackParamList } from '~/RoutesParams';
import { AccountVerificationBottomSheet } from '~/app/common/components/AccountVerificationBottomSheet';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Screen } from '~/components/ui/Screen';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { useAnimStyle } from '~/hooks/useAnimStyle';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useLoading } from '~/hooks/useLoading';
import { useLogger } from '~/hooks/useLogger';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { getFirebaseUser } from '~/lib/firebase/auth';
import { useAuthStore } from '~/store/useAuthStore';

const TimeBetweenRetries = 0;

export function AccountVerificationScreen(): ReactElement {
    const log = useLogger('AccountVerificationScreen');
    const { params } = useRoute<RouteProp<RootStackParamList, 'AccountVerificationScreen'>>();
    const theme = useTheme();
    const user = useUser();
    const accountVerificationModal = useModal();
    const resolveMetadata = useAuthStore((state) => state.resolveMetadata);
    const logout = useAuthStore((state) => state.logout);
    const { t } = useTranslation();
    const notifications = useNotifications();
    const { navigate } = useNavigation();
    const [timeToWaitForResend, setTimeToWaitForResend] = useState(TimeBetweenRetries);

    const loaders = useLoading();

    const [opacity, opacityHelpers] = useAnimatedValue(1);
    const opacityStyle = useAnimStyle({ opacity });

    const handleConfirmation = useCallback(async () => {
        loaders.start();
        log.log('request user metadata');

        try {
            const metadata = await resolveMetadata();
            log.log('result of user metadata', {
                uid: metadata?.uid,
                verified: metadata?.verified,
            });
            if (metadata?.verified) {
                navigate('MainScreen');
            } else {
                notifications.info(t('yourAccountIsNotVerified'));
            }
        } catch (error) {
            log.error('error requesting user metadata', { error });
            notifications.error(t('somethingWentWrong'));
        } finally {
            loaders.stop();
        }
    }, [loaders, log, navigate, notifications, resolveMetadata, t]);

    const handleResend = useCallback(async () => {
        loaders.start();
        log.log('request resend verification email');
        setTimeToWaitForResend(TimeBetweenRetries);
        accountVerificationModal.open();

        try {
            const currentUser = getFirebaseUser();
            if (currentUser) {
                await sendEmailVerification(currentUser);
                notifications.info(t('verificationEmailSent'));
            }
        } catch (error) {
            log.error('error send verification', { error });
            notifications.error(t('somethingWentWrong'));
        } finally {
            loaders.stop();
        }
    }, [accountVerificationModal, loaders, log, notifications, t]);

    const handleNewAccount = useCallback(async () => {
        loaders.start();
        log.log('request new account');
        const firebaseUser = getFirebaseUser();

        try {
            if (firebaseUser) {
                await firebaseUser?.delete();
            }
        } catch (error) {
            log.error('error delete account', { error });
        }

        await logout();
        loaders.stop();
    }, [loaders, log, logout]);

    useEffect(() => {
        const timeout = setTimeout(() => setTimeToWaitForResend((prev) => Math.max(prev - 1, 0)), 1000);

        return () => clearTimeout(timeout);
    }, [timeToWaitForResend]);

    useEffect(() => {
        opacityHelpers.timing(loaders.default ? 0 : 1).start();
    }, [loaders.default, opacityHelpers]);

    return (
        <Screen disableScroll safeArea bgColor={'backgroundPrimary'}>
            <VStack safeArea flex alignItems={'center'} alignContent={'center'} justifyContent={'flex-end'}>
                <MaterialIcons name={'check-circle'} color={theme.palette.accent.main} size={60} />
                <Typography variant={'title1'} fontWeight={700} mt={3} mb={1.25}>
                    {t('confirmationSent')}
                </Typography>
                <Typography variant={'subhead'} color={'disabled'} textAlign={'center'} fontWeight={500}>
                    {t('accountVerificationScreenMessage')}
                </Typography>

                <Paper alignItems={'center'} py={2.25} px={4} mt={4} mb={10}>
                    <Typography variant={'callout'} fontWeight={500} color={'textSecondary'}>
                        {t('verificationLinkSendOn')}
                    </Typography>
                    <Typography variant={'callout'} fontWeight={700} mb={3}>
                        {params?.email || user?.email || ''}
                    </Typography>
                    <Typography variant={'callout'} fontWeight={500} color={'textSecondary'}>
                        {t('haveYouTypedAWrongEmail')}
                    </Typography>
                    <HStack justifyContent={'center'}>
                        <Trans
                            i18nKey={'needANewAccount'}
                            t={t}
                            components={{
                                Link: (
                                    <TypographyLink onPress={handleNewAccount} variant={'callout'} fontWeight={500} />
                                ),
                                Typography: (
                                    <Typography variant={'callout'} fontWeight={500} color={'textSecondary'} mr={1} />
                                ),
                            }}
                        />
                    </HStack>
                </Paper>

                <VStack fullWidth>
                    <Button
                        size={'large'}
                        mb={1}
                        label={t('iHaveConfirmed')}
                        onPress={handleConfirmation}
                        loading={loaders.isLoading()}
                    />
                    <VStack animated style={opacityStyle}>
                        <Button
                            variant={'subtle'}
                            size={'large'}
                            mt={1}
                            disabled={timeToWaitForResend > 0}
                            onPress={handleResend}
                            label={
                                timeToWaitForResend > 0
                                    ? t('resendIn', { value: timeToWaitForResend + 's' })
                                    : t('resend')
                            }
                        />
                    </VStack>
                </VStack>
            </VStack>
            <AccountVerificationBottomSheet {...accountVerificationModal.props} />
        </Screen>
    );
}
