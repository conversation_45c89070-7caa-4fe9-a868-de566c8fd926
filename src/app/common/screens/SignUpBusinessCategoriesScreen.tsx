/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import { ReactElement, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import * as Yup from 'yup';
import { categoriesEndpoint } from '@bookr-technologies/api/endpoints/categoriesEndpoint';
import { FormikButton } from '~/components/ui/Button';
import { FormikCheckbox } from '~/components/ui/Checkbox/FormikCheckbox';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { BusinessSignUpForm, useBusinessStore } from '~/store/useBusinessStore';

export function SignUpBusinessCategoriesScreen(): ReactElement {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const signUpForm = useBusinessStore((state) => state.signUpForm);
    const patchSignUpForm = useBusinessStore((state) => state.patchSignUpForm);
    const categories = useQuery('business/categories/available', () => categoriesEndpoint.fetchAvailableCategories());

    const validation = useValidationSchema({
        categories: Yup.array().required(t('requiredField')).min(1, t('requiredField')),
    });

    const handleSubmit = useCallback(
        async (values: Partial<BusinessSignUpForm>) => {
            patchSignUpForm(values);
            navigate('SignUpBusinessLocationScreen');
        },
        [navigate, patchSignUpForm],
    );

    return (
        <Formik validateOnMount enableReinitialize initialValues={signUpForm} onSubmit={handleSubmit} {...validation}>
            <Screen disableScroll>
                <ScreenHeader
                    headline={t('signUpBusinessCategoriesHeadline')}
                    caption={t('signUpBusinessCategoriesDescription')}
                    leftSlot={<ScreenHeaderBackButton />}
                />

                <VStack flex safeBottom={2}>
                    <VStack flex mb={2} mx={-3}>
                        <VStack flex scrollable px={1}>
                            {categories.data?.map((category) => (
                                <FormikCheckbox
                                    key={category.id}
                                    name={'categories'}
                                    label={t(`categories.${category.code.toLowerCase()}` as any)}
                                    value={category.code}
                                    disablePadding={false}
                                />
                            ))}
                        </VStack>
                    </VStack>

                    <HStack>
                        <FormikButton color={'accent'} size={'large'} label={t('continue')} fullWidth />
                    </HStack>
                </VStack>
            </Screen>
        </Formik>
    );
}
