import { useRoute } from '@react-navigation/native';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Avatar } from '~/components/ui/Avatar';
import { Grid, VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { joinStr } from '~/lib/utils/string';
import { Service } from '../components/Services/Services';
import { StaffProfileActions } from '../components/StaffProfileActions/StaffProfileActions';

interface BusinessStaffScreenParams {
    staff: UserModel;
}

export function BusinessStaffScreen(): JSX.Element {
    const { t } = useTranslation();
    const route = useRoute();

    const { staff } = route.params as BusinessStaffScreenParams;

    const { data: business } = useQuery(
        ['business', staff?.business?.id],
        () => businessEndpoint.show(staff.business.id),
        {
            enabled: !!staff?.business?.id,
        },
    );

    if (!business) {
        return <></>;
    }

    const services = useMemo(
        () => staff.services.filter(({ hiddenFromClients }) => !hiddenFromClients).sort(ServiceModel.sort),
        [staff.services],
    );

    return (
        <Screen disablePadding disableScroll bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
            <ScreenHeader bgColor={'backgroundPrimary'} px={3} leftSlot={<ScreenHeaderBackButton />} />
            <VStack scrollable>
                <VStack alignItems={'center'} mt={3}>
                    <Avatar size={64} source={staff?.photoURL} name={staff?.displayName} />
                    <Typography variant={'title2'} fontWeight={700} textAlign={'center'} mt={1.25}>
                        {staff?.displayName}
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} textAlign="center" px={2}>
                        {joinStr([business?.name, business?.formattedAddress], ', ')}
                    </Typography>
                    <StaffProfileActions business={business} staff={staff} />
                </VStack>
                <Paper bgColor={'backgroundPrimary'} fullWidth flex borderRadius={0}>
                    <Grid p={3} fullWidth>
                        <Typography variant={'title2'} fontWeight={700} textAlign={'left'} mb={2}>
                            {t('services')}
                        </Typography>
                        {services.map((service) => (
                            <Grid key={service.id} mb={3}>
                                <Service business={business} service={service} staff={staff} />
                            </Grid>
                        ))}
                        {services.length === 0 && (
                            <Typography variant={'caption1'} color={'disabled'} fontWeight={400}>
                                {t('noServices')}
                            </Typography>
                        )}
                    </Grid>
                </Paper>
            </VStack>
        </Screen>
    );
}
