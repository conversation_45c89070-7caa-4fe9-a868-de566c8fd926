/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation } from '@react-navigation/native';
import * as Notifications from 'expo-notifications';
import { ReactElement, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components/native';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useLoading } from '~/hooks/useLoading';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { getPushNotificationToken } from '~/lib/utils/PushNotificationsUtils';
import { useApplicationStore } from '~/store/useApplicationStore';

export function NotificationPreferenceScreen(): ReactElement {
    const { t } = useTranslation();
    const loading = useLoading();
    const { navigate } = useNavigation();
    const notifications = useNotifications();
    const user = useUser();
    const logger = useLogger('NotificationPreferenceScreen');
    const setSkipPushNotifications = useApplicationStore((state) => state.setSkipPushNotifications);

    const handleSkipPushNotifications = useCallback(() => {
        notifications.warning(t('notificationPermissionsDenied'));
        setSkipPushNotifications(true);
        navigate('MainScreen');
    }, [navigate, notifications, setSkipPushNotifications, t]);

    const handleAllowNotifications = useCallback(async () => {
        loading.start();
        const notificationPermissions = await Notifications.getPermissionsAsync();
        if (!notificationPermissions.granted) {
            const permissions = await Notifications.requestPermissionsAsync();
            if (!permissions.granted) {
                notifications.warning(t('notificationPermissionsDenied'));
            }

            try {
                const { data } = await getPushNotificationToken();

                if (user?.uid) {
                    await usersEndpoint.update(user.uid, { pushNotificationToken: data });
                }
            } catch (e) {
                logger.error('error getting expo push token', { e });
            }

            setSkipPushNotifications(true);
        }

        navigate('MainScreen');
        loading.stop();
    }, [loading, logger, navigate, notifications, setSkipPushNotifications, t, user?.uid]);

    return (
        <Screen safeArea disableScroll bgColor={'backgroundPrimary'}>
            <VStack safeArea flex justifyContent={'space-between'}>
                <VStack>
                    <Typography variant={'title1'} fontWeight={700} mt={5}>
                        {t('notificationPreferenceHeadline')}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'} mt={2} mb={2}>
                        {t('notificationPreferenceDescription')}
                    </Typography>
                </VStack>

                <VStack alignItems={'center'}>
                    <StyledImage resizeMode={'contain'} source={require('~/assets/notificationsImage.png')} />
                </VStack>

                <VStack mb={2}>
                    <VStack opacity={loading.isLoading() ? 0 : 1}>
                        <Button
                            label={t('allowNotifications')}
                            size={'large'}
                            color={'accent'}
                            mb={1}
                            onPress={handleAllowNotifications}
                        />
                    </VStack>
                    <Button
                        label={t('notNow')}
                        size={'large'}
                        variant={'subtle'}
                        onPress={handleSkipPushNotifications}
                        loading={loading.isLoading()}
                    />
                </VStack>
            </VStack>
        </Screen>
    );
}

const StyledImage = styled.Image`
    height: 200px;
`;
