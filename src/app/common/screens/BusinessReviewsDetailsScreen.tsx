import { RouteProp, useRoute } from '@react-navigation/native';
import { uniqBy } from 'lodash';
import React, { Fragment, ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList } from 'react-native';
import { useQuery } from 'react-query';
import { reviewsEndpoint } from '@bookr-technologies/api/endpoints/reviewsEndpoint';
import { ReviewModel } from '@bookr-technologies/api/models/ReviewModel';
import { PageableResponse } from '@bookr-technologies/api/types/PageableResponse';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { RootStackParamList } from '~/RoutesParams';
import { UserReview } from '~/app/common/components/BusinessReviews/UserReview';
import { Avatar } from '~/components/ui/Avatar';
import { HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { RatingAndReviews } from '../components/BusinessReviews/RatingAndReviews';
import { RatingFilter } from '../components/BusinessReviews/RatingFilter';
import { Loader } from '../components/Loader';

export function BusinessReviewsDetailsScreen(): ReactElement {
    const route = useRoute<RouteProp<RootStackParamList, 'BusinessReviewsDetailsScreen'>>();
    const { t } = useTranslation();
    const notifications = useNotifications();
    const insets = useSafeAreaInsets();
    const { businessDisplayName, businessImageURL, reviewsInfo, businessId } = route.params;

    const [reviews, setReviews] = useState<ReviewModel[]>([]);
    const [selectedFilter, setSelectedFilter] = useState<number | undefined>(undefined);
    const [page, setPage] = useState(0);

    const avatar$ = useMemo(() => <Avatar source={businessImageURL} size={66} />, [businessImageURL]);

    const onError = (e: Error): void => notifications.error(getErrorMessage(e));

    const onSuccess = (data: PageableResponse<ReviewModel>): void =>
        setReviews((reviews) => uniqBy(page === 0 ? data.content : [...reviews, ...data.content], 'id'));

    const fetchReviews = (page = 0, filterRating?: number): Promise<PageableResponse<ReviewModel>> =>
        reviewsEndpoint.fetchData({ businessId, page, size: 10, filterRating });

    const {
        isPreviousData,
        isLoading,
        isFetching,
        data: reviewsResponse,
        refetch,
    } = useQuery(['reviews', page, selectedFilter], () => fetchReviews(page, selectedFilter), {
        keepPreviousData: true,
        onError,
        onSuccess,
    });

    const { data: ratingsChart } = useQuery('reviews', () => reviewsEndpoint.fetchReviewsCount(businessId));
    const totalNoOfReviews = ratingsChart?.reduce((acc, curr) => acc + curr.noOfRatings, 0);

    const refreshControl = useRefreshControl(() => refetch());

    const handleFilterPress = useEvent((ratingValue?: number) => {
        setSelectedFilter(ratingValue);
        setPage(0);
        if (!ratingValue) {
            fetchReviews(0, undefined);
        }
    });

    const handleOnEndReached = useEvent(() => {
        if (!isPreviousData && !reviewsResponse?.last) {
            setPage((old) => old + 1);
        }
    });

    const topElement = useMemo(
        () => (
            <Fragment key={'top-section'}>
                <VStack alignItems={'center'}>
                    {avatar$}
                    <Typography variant={'title2'} fontWeight={700} mt={1}>
                        {businessDisplayName}
                    </Typography>
                    <HStack alignItems={'center'} mt={1.25} mb={3.5}>
                        <RatingAndReviews reviewsInfo={reviewsInfo} showStar />
                    </HStack>
                </VStack>
                <VStack>
                    <Typography variant={'body'} fontWeight={700} mb={0.5}>
                        {t('filterByRating')}
                    </Typography>
                    {ratingsChart?.map((ratingCount, index) => (
                        <RatingFilter
                            key={index}
                            ratingCount={ratingCount}
                            totalNoOfReviews={totalNoOfReviews}
                            onFilterPress={handleFilterPress}
                            selectedFilter={selectedFilter}
                        />
                    ))}
                </VStack>
                <Paper bgColor={'backgroundPrimary'} p={2} mt={2} mb={4}>
                    <HStack alignItems={'flex-start'} flexWrap={'nowrap'}>
                        <Icon name="shield-check" variant="secondary" color="accent" />
                        <VStack px={2}>
                            <Typography variant={'subhead'} fontWeight={500} mb={0.5}>
                                {t('reviewsYouCanTrust')}
                            </Typography>
                            <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                                {t('reviewsFromOurUsers')}
                            </Typography>
                        </VStack>
                    </HStack>
                </Paper>
            </Fragment>
        ),
        [
            avatar$,
            businessDisplayName,
            handleFilterPress,
            ratingsChart,
            reviewsInfo,
            selectedFilter,
            t,
            totalNoOfReviews,
        ],
    );

    const data = useMemo(() => {
        const reviewsElements = reviews
            .filter((review) => !selectedFilter || review.rating === selectedFilter)
            .map((item) => <UserReview key={item.id} review={item as ReviewModel} />);

        const fetchingLoader = isFetching ? <Loader /> : <Fragment key={'loader'} />;

        return [topElement, ...reviewsElements, fetchingLoader];
    }, [isFetching, reviews, selectedFilter, topElement]);

    return (
        <Screen px={3} bgColor={'backgroundSecondary'} disableScroll>
            <ScreenHeader bgColor={'backgroundSecondary'} leftSlot={<ScreenHeaderBackButton />} />
            <VStack flex>
                {isLoading && <Loader p={2} />}
                <FlatList
                    refreshControl={refreshControl}
                    disableScrollViewPanResponder
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: insets.bottom }}
                    data={data}
                    initialNumToRender={10}
                    maxToRenderPerBatch={10}
                    renderItem={({ item }): ReactElement => item}
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={(item): string => String(item.key)}
                    onEndReachedThreshold={0.8}
                    onEndReached={handleOnEndReached}
                />
            </VStack>
        </Screen>
    );
}
