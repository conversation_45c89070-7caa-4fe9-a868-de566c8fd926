/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation } from '@react-navigation/native';
import * as Sentry from '@sentry/react-native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'react-native-expo-image-cache';
import { useMutation, useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';

export function JoinBusinessScreen(): ReactElement | null {
    const logger = useLogger('JoinBusinessScreen');
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const notifications = useNotifications();
    const { data, isLoading } = useQuery('amIAnEmployee', businessEndpoint.amIAnEmployee);
    const joinBusinessMutation = useMutation('joinBusiness', () => businessEndpoint.joinBusiness(), {
        onSuccess: async () => {
            navigate('MainScreen');
        },
        onError: (error) => {
            Sentry.captureException(error);
            notifications.error(t('somethingWentWrongError', { error }));
            logger.error('Error joining business', { error });
        },
    });

    if (isLoading || !data) {
        return null;
    }

    const handleJoinBusiness = (): void => joinBusinessMutation.mutate();

    return (
        <Screen disableScroll safeArea bgColor={'backgroundPrimary'}>
            <VStack safeArea>
                <Typography variant={'title1'} fontWeight={700}>
                    {t('joinBusinessHeadline', { businessName: data.businessName })}
                </Typography>
                <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'} mt={2} mb={6}>
                    {t('joinBusinessDescription')}
                </Typography>
                <Image
                    uri={data.businessProfilePicture}
                    style={{ width: 150, height: 150, borderRadius: 75, alignSelf: 'center' }}
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    resizeMode={'cover'}
                />
            </VStack>

            <Button
                variant={'contained'}
                color={'accent'}
                label={t('joinBusiness')}
                size={'large'}
                onPress={handleJoinBusiness}
            />
        </Screen>
    );
}
