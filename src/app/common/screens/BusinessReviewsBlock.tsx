import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ReviewModel } from '@bookr-technologies/api/models/ReviewModel';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { Rating } from '../components/BusinessReviews/Rating';
import { RatingAndReviews } from '../components/BusinessReviews/RatingAndReviews';
import { UserReview } from '../components/BusinessReviews/UserReview';

type BusinessReviewsBlockProps = {
    handleSeeReviewsPress: () => void;
    importedReviews?: boolean;
    reviews?: ReviewModel[];
    reviewsInfo: BusinessModel['reviewInfo'];
};

export function BusinessReviewsBlock({
    reviews,
    reviewsInfo,
    handleSeeReviewsPress,
    importedReviews,
}: BusinessReviewsBlockProps): JSX.Element {
    const { t } = useTranslation();

    if (importedReviews) {
        return (
            <VStack mx={3} mb={1.5} p={2} bgColor={'backgroundPrimary'} borderRadius={12}>
                <Typography variant={'body'} fontWeight={700} mb={1}>
                    {t('googleReviews')}
                </Typography>
                <HStack borderRadius={8} alignItems={'center'} mr={1}>
                    <IconButton disablePadding mr={1} disabled>
                        <MaterialIcons name={'star'} size={16} color={`primary`} />
                    </IconButton>
                    <Typography variant={'footnote'} fontWeight={500}>
                        {t('ratingNumber', { rating: reviewsInfo.averageRating.toFixed(2).slice(0, 3) })}
                    </Typography>
                    <Typography fontSize={4} color={'#afafaf'} variant={'footnote'} fontWeight={500} mx={0.5}>
                        ●
                    </Typography>
                    <Typography variant={'footnote'} fontWeight={500} color={'#afafaf'}>
                        {t('reviewsNumber', { reviews: reviewsInfo.noOfReviews })}
                    </Typography>
                </HStack>
            </VStack>
        );
    }

    return (
        <Paper bgColor={'backgroundPrimary'} pt={2} px={3} mb={1} pb={3} borderRadius={0}>
            <VStack>
                <Typography variant={'title3'} fontWeight={700} pb={2.5}>
                    {t('reviews')}
                </Typography>
                <Rating rating={Number(reviewsInfo.averageRating.toFixed(2))} size={22} starMargin={1} />
                <HStack mb={3}>
                    <RatingAndReviews reviewsInfo={reviewsInfo} />
                </HStack>
                {reviews?.map((review) => (
                    <UserReview key={`userReview${review.id}`} review={review} />
                ))}
                <TypographyLink
                    variant="footnote"
                    fontWeight={400}
                    onPress={handleSeeReviewsPress}
                    textDecorationLine="underline"
                    color="textPrimary"
                >
                    {t('seeAllReviews')}
                </TypographyLink>
            </VStack>
        </Paper>
    );
}
