import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import * as Localization from 'expo-localization';
import { Formik, FormikProps } from 'formik';
import { ReactElement, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import * as Yup from 'yup';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { RootStackParamList } from '~/RoutesParams';
import { FormikButton } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { Spacer } from '~/components/ui/Spacer';
import { FormikTextField } from '~/components/ui/TextField';
import { FormikLanguageField } from '~/components/ui/TextField/FormikLanguageField';
import { FormikPhoneField } from '~/components/ui/TextField/FormikPhoneField';
import { useAuth } from '~/hooks/useAuth';
import { useLogger } from '~/hooks/useLogger';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { AnalyticsEvent, logAnalyticsEvent, setAnalyticsUser } from '~/lib/analytics/analytics';
import { createUserWithEmailAndPassword, sendEmailVerification } from '~/lib/firebase/auth';

export function SignUpScreen(): ReactElement {
    const logger = useLogger('SignUpScreen');
    const { params } = useRoute<RouteProp<RootStackParamList, 'SignUpScreen'>>();
    const initialValues = useMemo(
        () => ({
            displayName: '',
            email: params?.email ?? '',
            language: Localization.locale,
            password: '',
            passwordConfirmation: '',
            phoneNumber: '',
        }),
        [params?.email],
    );

    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const { t } = useTranslation();
    const navigation = useNavigation();
    const { authenticated } = useAuth();

    const validation = useValidationSchema({
        displayName: Yup.string().required(t('requiredField')),
        email: Yup.string().required(t('requiredField')),
        language: Yup.string().required(t('requiredField')),
        password: Yup.string()
            .required(t('requiredField'))
            .min(8, t('minLength', { minLength: 8 })),
        passwordConfirmation: Yup.string()
            .required(t('requiredField'))
            .oneOf([Yup.ref('password'), null], t('passwordNotMatch')),
    });

    const handleSubmit = useCallback(
        async (values: typeof initialValues) => {
            try {
                const user = await createUserWithEmailAndPassword(values.email, values.password);
                await usersEndpoint.create({
                    accountType: AccountType.Invalid,
                    displayName: values.displayName,
                    email: values.email,
                    language: values.language,
                    phoneNumber: values.phoneNumber,
                    photoURL: '',
                    uid: user.uid,
                });

                setAnalyticsUser(user);
                logAnalyticsEvent(AnalyticsEvent.SignUp, { platform: Platform.OS });

                // TODO: add in app verification
                await sendEmailVerification();

                navigation.navigate('AccountVerificationScreen', { email: values.email });
            } catch (error) {
                const message = getErrorMessage(error, 'errorCreatingAccount');

                logger.error('error creating account', {
                    error,
                    message,
                    displayName: values.displayName,
                    email: values.email,
                    language: values.language,
                    phoneNumber: values.phoneNumber,
                });
            }
        },
        [logger, navigation],
    );

    return (
        <Screen safeArea>
            <ScreenHeader
                disableSafeViewArea
                headline={t('signUpScreenHeadline')}
                caption={t('signUpScreenDescription')}
                mb={1.5}
                leftSlot={<ScreenHeaderBackButton />}
            />
            <Formik initialValues={initialValues} onSubmit={handleSubmit} innerRef={formikRef} {...validation}>
                <VStack justifyContent={'flex-start'} flex>
                    <FormikTextField
                        type={'email'}
                        name={'email'}
                        label={t('enterYourEmail')}
                        disabled={!!params?.email}
                        mb={2}
                    />
                    <FormikTextField name={'displayName'} label={t('firstNameAndLastName')} mb={2} />

                    <FormikPhoneField name={'phoneNumber'} />
                    <FormikLanguageField name={'language'} />

                    <FormikTextField type={'password'} name={'password'} label={t('createPassword')} mb={2} />
                    <FormikTextField
                        type={'password'}
                        name={'passwordConfirmation'}
                        label={t('confirmYourPassword')}
                        mb={3}
                    />

                    <Spacer />

                    <FormikButton
                        size={'large'}
                        label={t('continue')}
                        loading={authenticated === null}
                        fullWidth
                        mb={2}
                    />
                </VStack>
            </Formik>
        </Screen>
    );
}
