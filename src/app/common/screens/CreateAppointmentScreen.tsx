/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import * as Sentry from '@sentry/react-native';
import { AxiosError } from 'axios';
import { Formik, FormikProps } from 'formik';
import { debounce } from 'lodash';
import moment from 'moment';
import React, { ReactElement, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import * as Yup from 'yup';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { DEFAULT_USER_PHOTO_URL } from '@bookr-technologies/api/constants/UserDefaults';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { BookNowRequest } from '@bookr-technologies/api/dto/BookNowDTO';
import { IsTimeslotFreeDTO } from '@bookr-technologies/api/dto/IsTimeslotFreeDTO';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { PaymentMethod } from '@bookr-technologies/api/models/PaymentMethod';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { formatDate } from '@bookr-technologies/core';
import { Nullable } from '@bookr-technologies/core/types';
import { RootStackParamList } from '~/RoutesParams';
import { FreePlanCountAppointmentsPieChart } from '~/app/business/components/FreePlanCountAppointmentsPieChart';
import { FormikSelect } from '~/app/common/components/FormikSelect';
import { FormikSelectStaffMemberService } from '~/app/common/components/FormikSelectStaffMemberService';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { BottomSheetDatePicker } from '~/components/BottomSheetDatePicker';
import { BottomSheetTimePicker } from '~/components/BottomSheetTimePicker';
import { useUserHasAccess } from '~/components/PrivilegeBasedAccess/useUserHasAccess';
import { AutoComplete } from '~/components/ui/AutoComplete';
import { AutoCompleteOptionType } from '~/components/ui/AutoComplete/AutoCompleteOptionType';
import { Avatar } from '~/components/ui/Avatar';
import { Box } from '~/components/ui/Box';
import { FormikButton } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Switch } from '~/components/ui/Switch';
import { FormikTextField, TextFieldAdornment } from '~/components/ui/TextField';
import { FormikPhoneField } from '~/components/ui/TextField/FormikPhoneField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { str } from '~/lib/utils/string';
import { useAuthStore } from '~/store/useAuthStore';
import { useBusinessStore } from '~/store/useBusinessStore';
import { FormikSelectStaffMember } from '../components/FormikSelectStaffMember';

interface SearchData {
    page: number;
    textSearch: string;
}

const debouncedCall = debounce((func) => func(), 500);

export function CreateAppointmentScreen(): ReactElement {
    const theme = useTheme();
    const log = useLogger('CreateAppointmentScreen');
    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const { t } = useTranslation();
    const notifications = useNotifications();
    const user = useUser();
    const { navigate } = useNavigation<any>();
    const { params } = useRoute<RouteProp<RootStackParamList, 'CreateAppointmentScreen'>>();
    const [sendPaymentLink, setSendPaymentLink] = useState(false);
    const [searchData, setSearchData] = useState<SearchData>({ page: 0, textSearch: '' });
    const freePlanCountModal = useModal();
    const subscription = useAuthStore((state) => state.subscription);
    const isFreePlan = !!user && subscription?.subscriptionPlan === 'FREE';
    const { data: appointmentCount } = useQuery({
        queryKey: ['myAppointmentsAsStaff'],
        enabled: isFreePlan,
        queryFn: usersEndpoint.countMyAppointmentsAsStaff,
        cacheTime: 0,
        onSuccess: (data) => {
            if (data >= 100) {
                freePlanCountModal.open();
            }
        },
    });

    const currentBusiness = useBusinessStore((state) => state.currentBusiness);
    const [clients, setClients] = useState<UserModel[]>([]);
    const loadClientsEnabled = useUserHasAccess([UserPrivilegeType.Standard, UserPrivilegeType.Professional]);

    const { data, isFetching, refetch } = useQuery({
        queryKey: `business:clients:${searchData.textSearch.toLowerCase()}`,
        enabled: !!currentBusiness?.id && loadClientsEnabled,
        queryFn: () =>
            businessClientsEndpoint.getClients({
                businessId: currentBusiness?.id,
                ...searchData,
                textSearch: searchData.textSearch.toLowerCase(),
                size: 20,
            }),
        onSuccess: (data) => {
            if (searchData.page > 0) {
                setClients((prevState) => [...prevState, ...(data?.content ?? [])]);
            } else {
                setClients(data?.content ?? []);
            }
            setSearchData((prev) => ({
                ...prev,
                page: prev.page + 1,
            }));
        },
        onError: (error) => {
            log.error('error getting clients', { error });
            Sentry.captureException(error, {
                extra: {
                    uid: user?.uid,
                    businessId: currentBusiness?.id,
                    searchData,
                },
            });
            notifications.error(t('cannotLoadClients'));
        },
        cacheTime: 0,
        retry: (failureCount, { response }: AxiosError) =>
            ![401, 403].includes(response?.status || 0) && failureCount <= 3,
    });

    const showAlert = useModal();

    const appointment = params?.appointment;
    const isEdit = !!appointment?.id;

    const initialValues = useMemo<{
        client: UserModel | null;
        day: string;
        newClient: boolean;
        recurrenceStrategy: string;
        recurrent: boolean;
        serviceId: string | number | null;
        staffMemberId: string | null;
        time: string;
        timestampEnd: string;
    }>(
        () => ({
            client: appointment?.client ?? null,
            day: formatDate(appointment?.dateTime || params?.date || new Date(), BottomSheetDatePicker.DateFormat),
            recurrent: appointment?.recurrent ?? false,
            newClient: false,
            serviceId: appointment?.service?.id ?? null,
            staffMemberId: appointment?.staff?.uid || params?.staffMemberId || user?.uid || null,
            time: formatDate(appointment?.dateTime || params?.date || new Date(), BottomSheetTimePicker.DateFormat),
            timestampEnd: '',
            recurrenceStrategy: '',
        }),
        [appointment, params?.date, params?.staffMemberId, user?.uid],
    );

    const validation = useValidationSchema({
        client: Yup.object().required(t('requiredField')),
        day: Yup.string().required(t('requiredField')),
        serviceId: Yup.string().required(t('requiredField')),
        staffMemberId: Yup.string().required(t('requiredField')),
        time: Yup.string().required(t('requiredField')),
        // timestampEnd required if recurrenceStrategy is not empty
        timestampEnd: Yup.string().when('recurrenceStrategy', {
            is: (val: string) => !!val,
            then: Yup.string().required(t('requiredField')),
        }),
    });

    const handleFetch = useEvent(async (search: string) =>
        debouncedCall(() =>
            setSearchData({
                page: 0,
                textSearch: String(search).trim(),
            }),
        ),
    );

    const mapToAutocompleteSuggestions = (clients: UserModel[], search: string) => {
        const showAvatars = clients?.length < 5; // show avatars only if there are less than 5 clients, performance reasons
        const options = (clients ?? []).map((item) => ({
            avatar: showAvatars ? (
                <Avatar
                    size={32}
                    source={item.photoURL !== DEFAULT_USER_PHOTO_URL ? item.photoURL : null}
                    name={item.displayName}
                />
            ) : (
                <Avatar size={32} name={item.displayName} />
            ),
            id: item.uid,
            label: item.displayName,
            value: item,
        }));

        return [
            {
                avatar: <Avatar size={32} icon={<MaterialIcons name={'person'} size={24} />} bgColor={'accent'} />,
                fieldLabel: search,
                id: 'new',
                label: search ? t('addNewClientByName', { name: search }) : t('addNewClient'),
                value: {
                    displayName: search,
                    id: 0,
                },
            },

            ...options.filter((option) => !search || option.label.toLowerCase().trim().includes(search.toLowerCase())),
        ] as Array<AutoCompleteOptionType<any>>;
    };

    const handleEndReached = useEvent(async () => {
        if (!data || data?.last) {
            return;
        }

        await refetch();
    });

    const handleNewAppointment = useEvent(async (createAppointmentInput: BookNowRequest) => {
        try {
            let result;
            if (params?.appointment?.id) {
                const updateData: { serviceId?: string; timestamp: number } = {
                    timestamp: createAppointmentInput.timestamp,
                };

                // Only include serviceId if it has changed from the original appointment
                if (createAppointmentInput.serviceId !== String(params.appointment.service?.id)) {
                    updateData.serviceId = createAppointmentInput.serviceId;
                }

                result = await appointmentsEndpoint.update<AppointmentModel, { serviceId?: string; timestamp: number }>(
                    params.appointment.id,
                    updateData,
                );

                logAnalyticsEvent(AnalyticsEvent.BookingEdited);
            } else {
                const { appointment } = await appointmentsEndpoint.create<
                    { appointment: AppointmentModel },
                    BookNowRequest
                >(createAppointmentInput);

                result = appointment;

                logAnalyticsEvent(AnalyticsEvent.BookingMade);
            }

            refetch();

            navigate('AppointmentConfirmationScreen', {
                appointments: [result],
                onClose: () => navigate('AppointmentsScreen'),
                onGoToAppointment: (appointment: AppointmentModel) =>
                    navigate('AppointmentsScreen', {
                        actionAfterLoad: () =>
                            navigate('AppointmentDetailsScreen', {
                                appointment,
                                client: createAppointmentInput.client,
                            }),
                    }),
                isEdit,
            });
        } catch (e) {
            log.error('error creating appointment', { e });
            notifications.error(t('somethingWentWrong'));
        }
    });

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        if (!!appointmentCount && appointmentCount >= 100) {
            freePlanCountModal.open();
            return;
        }
        if (!values.staffMemberId) {
            notifications.error(t('staffMemberIsNotSelected'));
            return;
        }

        if (!values.client) {
            notifications.error(t('clientIsNotSelected'));
            return;
        }

        if (!values.serviceId) {
            notifications.error(t('serviceIsNotSelected'));
            return;
        }

        const createAppointmentInput: BookNowRequest = {
            client: {
                ...(values.client || ({} as any)),
                accountType: AccountType.Client,
                email: values.client.email,
                phoneNumber: values.client.phoneNumber,
                uid: values.client?.uid,
            },
            recurrenceStrategy: values.recurrenceStrategy,
            inviteClient: false,
            serviceId: str(values.serviceId),
            staffId: values.staffMemberId,
            timestamp: moment(
                `${values.day} ${values.time}`,
                `${BottomSheetDatePicker.DateFormat} ${BottomSheetTimePicker.DateFormat}`,
            ).unix(),
            timestampEnd: !!values.recurrenceStrategy
                ? moment(values.timestampEnd, BottomSheetDatePicker.DateFormat).endOf('day').unix()
                : 0,
            paymentMethod: sendPaymentLink ? PaymentMethod.CARD : PaymentMethod.CASH,
        };

        // Check if we're editing an appointment where only the service has changed
        const isEditingWithOnlyServiceChange =
            isEdit &&
            params?.appointment &&
            createAppointmentInput.timestamp === moment(params.appointment.dateTime).unix() &&
            createAppointmentInput.staffId === params.appointment.staff?.uid &&
            createAppointmentInput.serviceId !== String(params.appointment.service?.id);

        // Skip timeslot check if only service changed in edit mode
        if (isEditingWithOnlyServiceChange) {
            await handleNewAppointment(createAppointmentInput);
            return;
        }

        if (!!createAppointmentInput.recurrenceStrategy) {
            const data = await appointmentsEndpoint.isRecurrentTimeslotFree(
                createAppointmentInput.serviceId,
                createAppointmentInput.timestamp,
                createAppointmentInput.timestampEnd,
                createAppointmentInput.recurrenceStrategy,
            );

            const overlappingAppointments = data.filter((timeslot) => !timeslot.free);
            if (overlappingAppointments.length > 0) {
                showAlert.openWithContext({
                    multiOverlap: true,
                    overlappingAppointments,
                    newAppointment: createAppointmentInput,
                });
            } else {
                await handleNewAppointment(createAppointmentInput);
            }
        } else {
            const data = await appointmentsEndpoint.isTimeslotFree(
                createAppointmentInput.staffId,
                createAppointmentInput.timestamp,
                createAppointmentInput.serviceId,
            );

            if (!data.free) {
                showAlert.openWithContext({
                    multiOverlap: false,
                    overlappingAppointment: data.appointment,
                    newAppointment: createAppointmentInput,
                });
            } else {
                await handleNewAppointment(createAppointmentInput);
            }
        }
    });

    const handleClientSelect = useEvent((option: Nullable<AutoCompleteOptionType<UserModel>>): void => {
        formikRef.current?.setFieldValue('newClient', option?.id === 'new');

        formikRef.current?.setFieldValue(
            'client',
            {
                uid: option?.value?.uid ?? null,
                email: option?.value?.email,
                displayName: option?.value?.displayName,
                phoneNumber: option?.value?.phoneNumber,
            },
            true,
        );
    });

    const handleClearClient = useEvent((): void => {
        formikRef.current?.setFieldValue('client', {});
        formikRef.current?.setFieldValue('newClient', false);
    });

    return (
        <Screen safeArea keyboardShouldPersistTaps={'never'}>
            <ScreenHeader
                disableSafeViewArea
                headline={isEdit ? t('editAppointment') : t('createAppointmentScreen.headline')}
                leftSlot={<ScreenHeaderBackButton />}
            />
            <Formik
                enableReinitialize
                initialValues={initialValues}
                onSubmit={handleSubmit}
                innerRef={formikRef}
                {...validation}
            >
                {({ values, handleChange }): any => (
                    <>
                        <VStack mb={2}>
                            <FormikSelectStaffMember
                                name={'staffMemberId'}
                                label={t('chooseStaffMember')}
                                disabled={isEdit || !!params?.appointment?.staff?.uid}
                            />
                        </VStack>

                        {values.newClient ? (
                            <VStack>
                                <FormikTextField
                                    name="client.displayName"
                                    mb={2}
                                    label={t('clientName')}
                                    endAdornment={
                                        <TextFieldAdornment variant="end">
                                            <IconButton
                                                disablePadding
                                                onPress={handleClearClient}
                                                size={'small'}
                                                color={'typography.textSecondary'}
                                            >
                                                <MaterialIcons name={'cancel'} />
                                            </IconButton>
                                        </TextFieldAdornment>
                                    }
                                />
                                <FormikTextField type="email" mb={2} name="client.email" label={t('clientEmail')} />
                                <FormikPhoneField name="client.phoneNumber" />
                            </VStack>
                        ) : (
                            <AutoComplete
                                value={values.client?.uid}
                                onFetch={handleFetch}
                                data={mapToAutocompleteSuggestions(clients, searchData.textSearch)}
                                label={t('clientName')}
                                loadingText={t('fetchingClients')}
                                noResultsText={t('fetchingClientsNoData')}
                                loading={isFetching}
                                onChange={handleClientSelect}
                                disabled={isEdit || !!params?.appointment?.client?.uid}
                                listProps={{
                                    onEndReachedThreshold: 0.85,
                                    onEndReached: handleEndReached,
                                }}
                            />
                        )}

                        {values.client?.uid || values.newClient ? (
                            <VStack mt={2}>
                                <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                                    {t('appointmentDetails')}
                                </Typography>

                                <HStack flexWrap={'nowrap'} mt={1} mb={2}>
                                    <VStack xs mr={1}>
                                        <BottomSheetDatePicker
                                            value={values.day}
                                            onChange={handleChange('day')}
                                            label={t('chooseDay')}
                                        />
                                    </VStack>
                                    <VStack xs ml={1}>
                                        <BottomSheetTimePicker
                                            value={values.time}
                                            onChange={handleChange('time')}
                                            label={t('chooseHour')}
                                        />
                                    </VStack>
                                </HStack>

                                <FormikSelectStaffMemberService
                                    name={'serviceId'}
                                    staffMemberKey={'staffMemberId'}
                                    label={t('chosenService')}
                                />
                                <Box mt={2} />
                                <FormikSelect
                                    label={t('recurrence')}
                                    items={[
                                        { label: t('appointmentNotRepeating'), value: '' },
                                        { label: t('appointmentRepeatingDaily'), value: '1d' },
                                        { label: t('appointmentRepeatingWeekly'), value: '1w' },
                                        { label: t('appointmentRepeatingBiWeekly'), value: '2w' },
                                        { label: t('appointmentRepeatingThreeWeekly'), value: '3w' },
                                        { label: t('appointmentRepeatingMonthly'), value: '1m' },
                                    ]}
                                    name={'recurrenceStrategy'}
                                    disabled={isEdit || !!params?.appointment}
                                />
                                {!isEdit && !!values.recurrenceStrategy && (
                                    <>
                                        <Box mt={2} />
                                        <BottomSheetDatePicker
                                            value={values.timestampEnd}
                                            onChange={handleChange('timestampEnd')}
                                            label={t('chooseRecurringEndDay')}
                                        />
                                    </>
                                )}
                                {!isEdit &&
                                    !values.recurrenceStrategy && // we are not supporting online payments for recurring appointments yet
                                    currentBusiness?.staffMembers
                                        ?.find(({ uid }) => uid === values.staffMemberId)
                                        ?.services?.find(({ id }) => id === values.serviceId)
                                        ?.acceptsOnlinePayments && (
                                        <HStack mt={2} alignItems={'center'}>
                                            <Box>
                                                <Switch
                                                    value={sendPaymentLink}
                                                    onValueChange={() => setSendPaymentLink(!sendPaymentLink)}
                                                />
                                            </Box>
                                            <Box flex={1} ml={1}>
                                                <Typography
                                                    variant={'subhead'}
                                                    color={'primary'}
                                                    lineHeight={22.5}
                                                    fontWeight={500}
                                                    width={'100%'}
                                                >
                                                    {t('sendPaymentLinkToCustomer')}
                                                </Typography>
                                            </Box>
                                        </HStack>
                                    )}
                            </VStack>
                        ) : null}

                        <Spacer />
                        {!!appointmentCount && appointmentCount >= 100 && (
                            <FreePlanCountAppointmentsPieChart
                                modal={freePlanCountModal}
                                // we don't want to show the chart, just the modal if the appointments are over the 100 limit
                                style={{ display: 'none' }}
                                minimal
                                count={appointmentCount}
                                width={0}
                                height={0}
                                borderRadius={99}
                                bgColor={theme.palette.backgroundPrimary.main}
                            />
                        )}
                        <FormikButton mt={2} label={isEdit ? t('saveChanges') : t('bookNow')} size={'large'} />
                    </>
                )}
            </Formik>
            {showAlert.context && (
                <BottomSheetAlert
                    variant={BottomSheetAlertVariant.Error}
                    headline={t('overlapAppointmentTitle')}
                    subHeadline={
                        showAlert.context.multiOverlap
                            ? t('overlapAppointmentsMessage', {
                                  count: showAlert.context.overlappingAppointments.length,
                                  appointments: showAlert.context.overlappingAppointments.map(
                                      ({ appointment: a }: IsTimeslotFreeDTO, index: number) =>
                                          `${index + 1}) ${a.client.displayName}, ${moment(a.dateTime).format(
                                              'DD MMM YYYY, HH:mm',
                                          )} - ${moment(a.dateTime)
                                              .add(a.service.duration, 'minutes')
                                              .format('HH:mm')}\n`,
                                  ),
                              })
                            : t('overlapAppointmentMessage', {
                                  displayName: showAlert.context.overlappingAppointment.client.displayName,
                                  start: moment(showAlert.context.overlappingAppointment.dateTime).format('HH:mm'),
                                  end: moment(showAlert.context.overlappingAppointment.dateTime)
                                      .add(showAlert.context.overlappingAppointment.service.duration, 'minutes')
                                      .format('HH:mm'),
                              })
                    }
                    primaryText={t('continue')}
                    secondaryText={t('iHaveChangedMyMind')}
                    onCancel={showAlert.close}
                    onAction={(): Promise<void> => handleNewAppointment(showAlert.context.newAppointment)}
                    icon={<MaterialCommunityIcons name="vector-intersection" />}
                    snapPoints={[showAlert.context.multiOverlap ? '100%' : '50%']}
                    {...showAlert.props}
                />
            )}
        </Screen>
    );
}
