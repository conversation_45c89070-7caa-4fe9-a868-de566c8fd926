import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import momentTimezone from 'moment-timezone';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components/native';
import { RootStackParamList } from '~/RoutesParams';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useBusiness } from '~/hooks/useBusiness';

export function AppointmentConfirmationScreen() {
    const theme = useTheme();
    const { t } = useTranslation();
    const { params } = useRoute<RouteProp<RootStackParamList, 'AppointmentConfirmationScreen'>>();
    const [appointmentsExpanded, setAppointmentsExpanded] = useState(false);

    const { goBack } = useNavigation();
    const { appointments } = params;

    const firstAppointment = appointments[0];
    const { dateTime, id } = firstAppointment;
    const staffName = firstAppointment.staff.displayName;
    const serviceName = firstAppointment.service.name;
    const business = useBusiness(firstAppointment.staff.business.id);

    const goToAppointment = () => {
        goBack();
        params.onGoToAppointment?.(firstAppointment);
    };

    const handleClose = () => {
        goBack();
        params.onClose?.();
    };

    const handleAddToCalendar = () => {
        params.addToCalendar?.(firstAppointment);
    };

    const getTime = (dateToFormat: string) => {
        if (!!business?.data?.zoneId && !!dateToFormat) {
            return momentTimezone.tz(dateToFormat, 'UTC').tz(business.data.zoneId).format('lll');
        }
        return '';
    };

    return (
        <Screen>
            <ScreenHeader
                disableSafeViewArea
                rightSlotGridProps={{ pt: 2 }}
                rightSlot={
                    <IconButton disablePadding onPress={handleClose}>
                        <MaterialIcons name={'close'} />
                    </IconButton>
                }
            />

            <VStack flex justifyContent={'flex-end'} alignItems={'center'} alignContent={'center'} safeBottom={2}>
                <MaterialIcons name={'check-circle'} size={64} color={theme.palette.accent.main} />
                <Typography variant={'title1'} fontWeight={700} mt={5} textAlign={'center'}>
                    {params.isEdit ? t('appointmentSuccessfullyModified') : t('appointmentSuccessfullyCreated')}
                </Typography>

                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mt={7} fullWidth>
                    {t('appointmentDetails')}
                </Typography>
                <Paper bgColor={'backgroundSecondary'} mt={1} fullWidth px={2} pt={2} pb={1}>
                    <HStack mb={1}>
                        <Typography variant={'subhead'} fontWeight={700} mr={1}>
                            {t('id') + ':'}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                            {t('idValue', { id })}
                        </Typography>
                    </HStack>
                    <HStack mb={1}>
                        <Typography variant={'subhead'} fontWeight={700} mr={1}>
                            {t('staffName') + ':'}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                            {staffName}
                        </Typography>
                    </HStack>
                    <HStack mb={1}>
                        <Typography variant={'subhead'} fontWeight={700} mr={1}>
                            {t('serviceName') + ':'}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                            {serviceName}
                        </Typography>
                    </HStack>
                    {appointments.length === 1 && (
                        <HStack mb={1}>
                            <Typography variant={'subhead'} fontWeight={700} mr={1}>
                                {t('dateAndTime') + ':'}
                            </Typography>
                            <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                                {getTime(dateTime)}
                            </Typography>
                        </HStack>
                    )}
                    <HStack mb={1}>
                        <Typography variant={'subhead'} fontWeight={700} mr={1}>
                            {t('isRecurrence') + ':'}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                            {firstAppointment.recurrent ? t('yes') : t('no')}
                        </Typography>
                    </HStack>
                </Paper>

                {appointments.length > 1 && (
                    <Paper bgColor={'backgroundSecondary'} mt={1} fullWidth pl={2} pr={1} py={1}>
                        <HStack justifyContent={'space-between'} alignItems={'center'}>
                            <Typography variant={'subhead'} fontWeight={700}>
                                {t('dateAndTime')}
                            </Typography>
                            <IconButton onPress={() => setAppointmentsExpanded(!appointmentsExpanded)}>
                                <MaterialIcons name={`keyboard-arrow-${appointmentsExpanded ? 'up' : 'down'}`} />
                            </IconButton>
                        </HStack>
                        {appointmentsExpanded && (
                            <VStack>
                                {appointments.map((appointment, index) => (
                                    <HStack my={1} key={appointment.id}>
                                        <Typography variant={'subhead'} fontWeight={700} mr={1}>
                                            {t('appointment') + ' ' + (index + 1)}:
                                        </Typography>
                                        <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                                            {getTime(appointment.dateTime)}
                                        </Typography>
                                    </HStack>
                                ))}
                            </VStack>
                        )}
                    </Paper>
                )}

                {params.showAddToCalendar && (
                    <Button
                        mt={8}
                        label={t('addToCalendar')}
                        size={'large'}
                        onPress={handleAddToCalendar}
                        startIcon={<MaterialIcons name={'event'} />}
                        align={'space-between'}
                        width={'100%'}
                        keepIcons
                    />
                )}
                <Button
                    mt={params.showAddToCalendar ? 1 : 8}
                    mb={2}
                    label={t('viewAppointment')}
                    size={'large'}
                    fullWidth
                    keepIcons
                    color={params.showAddToCalendar ? 'backgroundTertiary' : 'primary'}
                    onPress={goToAppointment}
                />
            </VStack>
        </Screen>
    );
}
