import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { ImageBackground, ImageStyle } from 'react-native';
import Background from '~/assets/chooseSubscriptionPlan.jpg';
import { Button } from '~/components/ui/Button';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';
import { useNavigateToMainScreen } from '~/hooks/useNavigateToMainScreen';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { useApplicationStore } from '~/store/useApplicationStore';
import { useAuthStore } from '~/store/useAuthStore';

const useStyles = makeStyles(() => ({
    background: {
        flex: 1,
        height: '100%',
        width: '100%',
    },
}));

export function ChooseSubscriptionPlanScreen(): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const navigateToMainScreen = useNavigateToMainScreen();
    const loader = useLoading();
    const setSkipChooseSubscriptionPlan = useApplicationStore((state) => state.setSkipChooseSubscriptionPlan);
    const subscription = useAuthStore((state) => state.subscription);

    const handlePressSkip = useEvent(async () => {
        setSkipChooseSubscriptionPlan();
        logAnalyticsEvent(AnalyticsEvent.ChoosePlanSkipped);
        await loader.from(navigateToMainScreen());
    });

    return (
        <Screen disablePadding bgColor={'primary'}>
            <ImageBackground source={Background} style={styles.background as ImageStyle} resizeMode={'cover'}>
                <VStack
                    safeBottom={3}
                    flex
                    px={3}
                    justifyContent={'flex-end'}
                    alignContent={'center'}
                    alignItems={'center'}
                >
                    {!subscription?.shouldSelectSubscriptionPlan && (
                        <HStack safeTop={3} justifyContent={'flex-end'} fullWidth>
                            {loader.any() ? (
                                <CircularProgress size={24} color={'accent'} />
                            ) : (
                                <TypographyLink to={'/MainScreen'} onPress={handlePressSkip}>
                                    {t('skip')}
                                </TypographyLink>
                            )}
                        </HStack>
                    )}
                    <Spacer />
                    <Typography
                        variant={'title1'}
                        color={'primary.contrast'}
                        textAlign={'center'}
                        fontWeight={700}
                        mb={1}
                    >
                        {t('chooseSubscriptionPlanHeadline')}
                    </Typography>
                    <Typography variant={'subhead'} color={'disabled'} textAlign={'center'} fontWeight={500} mb={4}>
                        {t('chooseSubscriptionPlanDescription')}
                    </Typography>

                    <Button
                        onPress={() => logAnalyticsEvent(AnalyticsEvent.ChoosePlan)}
                        to={'/SubscriptionPlansScreen'}
                        color={'accent'}
                        label={t('chooseSubscriptionPlan')}
                        fullWidth
                        size={'large'}
                    />
                </VStack>
            </ImageBackground>
        </Screen>
    );
}
