import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { useTheme } from 'styled-components/native';
import { CreateFeedbackDTO } from '@bookr-technologies/api/dto/CreateFeedbackDTO';
import { feedbackEndpoint } from '@bookr-technologies/api/endpoints/feedbackEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { Button } from '~/components/ui/Button';
import { Grid, VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { Spacer } from '~/components/ui/Spacer';
import { TextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';

type FeedbackButton = {
    icon?: JSX.Element;
    label?: string;
    value: string;
};

type FeedbackButtonProps = {
    button: FeedbackButton;
    isSelected: boolean;
    onPress: (button: FeedbackButton) => void;
};

type FeedBackSelectionProps = {
    onCategorySelected: (category: string) => void;
    value: string;
};

const feedbackButtons: FeedbackButton[] = [
    { icon: <MaterialIcons name={'bug-report'} />, label: 'bugReport', value: 'Bug Report' },
    { icon: <MaterialIcons name={'thumb-up'} />, label: 'compliment', value: 'Suggestions' },
    { icon: <MaterialIcons name={'text-fields'} />, label: 'content', value: 'Content' },
    { icon: <MaterialIcons name={'sms'} />, label: 'suggestions', value: 'Compliment' },
    { icon: <MaterialIcons name={'more-horiz'} />, label: 'others', value: 'Other' },
];

function FeedBackButton({ onPress, button, isSelected }: FeedbackButtonProps): ReactElement {
    const { t } = useTranslation();
    const handlePress = useEvent(() => {
        onPress(button);
    });

    return (
        <Button
            mr={1}
            mb={0.75}
            variant={isSelected ? 'contained' : 'subtle'}
            color={'accent'}
            size={'small'}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            label={t(button.label as any)}
            onPress={handlePress}
            startIcon={button.icon}
        />
    );
}

function FeedBackSelection({ onCategorySelected, value }: FeedBackSelectionProps): ReactElement {
    const [selectedButton, setSelectedButton] = useState<FeedbackButton | undefined>(undefined);

    const handleButtonPress = useEvent((button: FeedbackButton) => {
        setSelectedButton(button);
        onCategorySelected(button.value);
    });

    useEffect(() => {
        if (!value) {
            handleButtonPress({ icon: undefined, value, label: undefined });
        }
    }, [handleButtonPress, onCategorySelected, value]);

    return (
        <Grid flexWrap={'wrap'} flexDirection={'row'}>
            {feedbackButtons.map((button, index) => {
                const isSelected = selectedButton?.label === button.label;
                return (
                    <FeedBackButton
                        key={`feedbackButton${index}`}
                        onPress={handleButtonPress}
                        isSelected={isSelected}
                        button={button}
                    />
                );
            })}
        </Grid>
    );
}

export function FeedbackScreen(): ReactElement {
    const theme = useTheme();
    const { t } = useTranslation();
    const notifications = useNotifications();
    const [message, setMessage] = useState('');
    const [thoughts, setThoughts] = useState('');
    const [category, setCategory] = useState('');

    const createFeedback = useMutation(
        'createFeedback',
        (data: CreateFeedbackDTO) => feedbackEndpoint.createFeedback(data),
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
            onSuccess: () => {
                notifications.success(t('thanksForFeedback'));
                setCategory('');
                setMessage('');
                setThoughts('');
            },
        },
    );

    const handleSendFeedbackPress = useEvent(() => {
        createFeedback.mutate({ category, message, thoughts });
    });

    return (
        <Screen px={3} safeArea>
            <VStack alignItems={'flex-start'}>
                <ScreenHeaderBackButton />
                <Typography variant={'title2'} fontWeight={700} mt={1}>
                    {t('feedback')}
                </Typography>
            </VStack>
            <Typography
                variant={'footnote'}
                color={theme.palette.contentTertiary.main}
                fontWeight={500}
                mb={1.5}
                mt={4}
            >
                {t('bookrFeedback')}
            </Typography>
            <TextField
                label={t('feedbackUs')}
                value={thoughts}
                multiline
                numberOfLines={4}
                maxLength={255}
                onChangeText={setThoughts}
            />
            <Typography
                variant={'footnote'}
                color={theme.palette.contentTertiary.main}
                fontWeight={500}
                mb={1.5}
                mt={4}
            >
                {t('feedbackCategory')}
            </Typography>
            <FeedBackSelection onCategorySelected={setCategory} value={category} />
            <Typography
                variant={'footnote'}
                color={theme.palette.contentTertiary.main}
                fontWeight={500}
                mb={1.5}
                mt={4}
            >
                {t('howCanWeImprove')}
            </Typography>
            <TextField
                label={t('howToImprove')}
                multiline
                numberOfLines={4}
                maxLength={255}
                value={message}
                onChangeText={setMessage}
            />

            <Spacer />

            <Button
                loading={createFeedback.isLoading}
                variant={'contained'}
                size={'large'}
                label={t('sendFeedback')}
                mt={2}
                mb={3}
                onPress={handleSendFeedbackPress}
                disabled={(message.length === 0 && thoughts.length === 0) || category.length === 0}
            />
        </Screen>
    );
}
