import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import * as Yup from 'yup';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { FormikButton } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { FormikTextField } from '~/components/ui/TextField';
import { FormikPhoneField } from '~/components/ui/TextField/FormikPhoneField';
import { useNotifications } from '~/hooks/useNotifications';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { useAuthStore } from '~/store/useAuthStore';
import { BusinessSignUpForm, useBusinessStore } from '~/store/useBusinessStore';

export function SignUpBusinessDetailsScreen() {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const signUpForm = useBusinessStore((state) => state.signUpForm);
    const patchSignUpForm = useBusinessStore((state) => state.patchSignUpForm);
    const updateUser = useAuthStore((state) => state.updateUser);
    const notifications = useNotifications();

    const validation = useValidationSchema({
        email: Yup.string().email(t('invalidEmail')).required(t('requiredField')),
        name: Yup.string().required(t('requiredField')),
        phoneNumber: Yup.string().required(t('requiredField')),
    });

    const handleSubmit = useCallback(
        async (values: Partial<BusinessSignUpForm>) => {
            patchSignUpForm(values);
            navigate('SignUpBusinessCategoriesScreen');
        },
        [navigate, patchSignUpForm],
    );

    const updateAccountTypeMutation = useMutation(
        'updateUser',
        (accountType: AccountType) => updateUser({ accountType }),
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
        },
    );

    const handlePress = useCallback(async () => {
        await updateAccountTypeMutation.mutateAsync(AccountType.Client);
        navigate('MainScreen');
    }, [navigate, updateAccountTypeMutation]);

    return (
        <Formik validateOnMount enableReinitialize initialValues={signUpForm} onSubmit={handleSubmit} {...validation}>
            <Screen>
                <ScreenHeader
                    headline={t('signUpBusinessDetailsHeadline')}
                    caption={t('signUpBusinessDetailsDescription')}
                    leftSlot={
                        <IconButton size={'large'} disablePadding onPress={handlePress}>
                            <Icon name={'arrow-back'} color={'typography.textPrimary'} />
                        </IconButton>
                    }
                />

                <VStack flex safeBottom={2}>
                    <FormikTextField name={'name'} label={t('businessName')} mb={2} />
                    <FormikTextField type={'email'} name={'email'} label={t('businessEmail')} mb={2} />
                    <FormikPhoneField name={'phoneNumber'} />

                    <Spacer />

                    <FormikButton color={'accent'} size={'large'} label={t('continue')} fullWidth />
                </VStack>
            </Screen>
        </Formik>
    );
}
