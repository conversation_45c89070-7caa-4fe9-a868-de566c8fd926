import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import Bookr from '~/components/icons/Bookr';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';

export function MaintenanceScreen(): ReactElement {
    const { t } = useTranslation();

    return (
        <Screen safeArea>
            <VStack flex py={2}>
                <Typography variant="title2" fontWeight={600} mb={1}>
                    {t('maintenanceTitle')}
                </Typography>

                <Typography variant="body" fontWeight={500} color="textSecondary">
                    {t('maintenanceDescription')}
                </Typography>

                <Spacer />
                <HStack justifyContent={'center'}>
                    <Bookr size={128} color="backgroundTertiary" />
                </HStack>
                <Spacer />

                <Typography variant="caption1" fontWeight={500} color="textSecondary" textAlign="center">
                    {t('maintenanceCopy')}
                </Typography>
            </VStack>
        </Screen>
    );
}
