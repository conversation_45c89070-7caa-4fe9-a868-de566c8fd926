import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { SmsOption } from '~/app/business/components/SmsOption';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { StripeCheckoutModal } from '~/components/StripeCheckoutModal';
import { Button } from '~/components/ui/Button';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { PaymentsSMSCancel, PaymentsSMSSuccess } from '~/data/applicationData';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';

export function SettingsPaymentsBuyMessagesScreen(): ReactElement {
    const { t } = useTranslation();
    const insets = useSafeAreaInsets(true);
    const notifications = useNotifications();
    const [showSMSBuySuccess, setShowSMSBuySuccess] = useState(false);
    const [sessionId, setSessionId] = useState<string | null>(null);

    const [selectedIndex, setSelectedIndex] = useState<number>(-1);
    const options = useQuery(
        'global/sms/options',
        async () => {
            const data = await paymentEndpoint.getSmsOptions();
            return data.sort((a, b) => Number(a.smsAmount) - Number(b.smsAmount));
        },
        {
            onSuccess: () => LayoutAnimation.easeInEaseOut(),
        },
    );

    const selectedOption = options.data?.[selectedIndex];
    const hasOptionSelected = selectedIndex > -1 && selectedOption;

    const paySMSMutation = useMutation(
        'paySMS',
        async () => {
            if (!hasOptionSelected) {
                return;
            }
            return paymentEndpoint.paySMS({
                amount: selectedOption.smsAmount,
                cancelUrl: PaymentsSMSCancel,
                successUrl: PaymentsSMSSuccess,
            });
        },
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
            onSuccess: (data) => {
                setSessionId(data?.sessionId || '');
            },
        },
    );

    const handleBuyMessages = useEvent(async () => {
        await paySMSMutation.mutateAsync();
    });

    const handleBuySMSSheetClose = useEvent(() => {
        setShowSMSBuySuccess(false);
    });

    const handleSuccessPayment = useEvent(() => {
        setShowSMSBuySuccess(true);
    });

    return (
        <>
            <Screen bgColor={'backgroundSecondary'}>
                <ScreenHeader
                    bgColor={'backgroundSecondary'}
                    leftSlot={<ScreenHeaderBackButton />}
                    headline={t('buySmsMessages')}
                    headlineTypographyProps={{ variant: 'title2' }}
                />

                <VStack flex>
                    {options.isLoading ? (
                        <VStack alignItems={'center'} justifyContent={'center'} mb={2}>
                            <CircularProgress size={24} color={'accent'} />
                        </VStack>
                    ) : options.data && !options.error ? (
                        options.data.map((option, index) => (
                            <SmsOption
                                key={option.smsPrice + ':' + option.smsAmount}
                                option={option}
                                selected={selectedIndex === index}
                                index={index}
                                onPress={setSelectedIndex}
                            />
                        ))
                    ) : (
                        <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                            {t('noSmsOptionsFound')}
                        </Typography>
                    )}
                </VStack>

                <Button
                    size={'large'}
                    loading={paySMSMutation.isLoading}
                    label={!hasOptionSelected ? t('buy') : t('buyFor', { value: selectedOption.smsPrice })}
                    disabled={!hasOptionSelected}
                    mb={insets.bottom}
                    onPress={handleBuyMessages}
                />
                <StripeCheckoutModal
                    visible={!!sessionId}
                    sessionId={sessionId}
                    onClose={(): void => setSessionId(null)}
                    onSuccess={handleSuccessPayment}
                />
                <BottomSheetAlert
                    variant={BottomSheetAlertVariant.Success}
                    open={showSMSBuySuccess}
                    onClose={handleBuySMSSheetClose}
                    headline={t('smsBought', { count: Number(selectedOption?.smsAmount) })}
                    subHeadline={t('thanksBookr')}
                />
            </Screen>
        </>
    );
}
