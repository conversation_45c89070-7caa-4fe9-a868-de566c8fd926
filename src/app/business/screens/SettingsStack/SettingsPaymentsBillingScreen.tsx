import { Formik } from 'formik';
import React, { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { CountryPicker } from '~/components/PhoneInput/CountryPicker';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';

export function SettingsPaymentsBillingScreen(): ReactElement {
    const { t } = useTranslation();
    const notifications = useNotifications();
    const log = useLogger('SettingsPaymentsBillingScreen');
    const billingInfo = useQuery('session/business/billing', () => businessEndpoint.getBillingInfo());
    const initialValues = useMemo(
        () => ({
            line1: billingInfo.data?.shipping?.address?.line1 || '',
            line2: billingInfo.data?.shipping?.address?.line2 || '',
            country: billingInfo.data?.shipping?.address?.country || '',
            city: billingInfo.data?.shipping?.address?.city || '',
            name: billingInfo.data?.name || '',
            phone: billingInfo.data?.shipping?.phone || '',
            state: billingInfo.data?.shipping?.address?.state || '',
            postalCode: billingInfo.data?.shipping?.address?.postalCode || '',
            taxId: billingInfo.data?.taxId || '',
        }),
        [billingInfo.data],
    );
    const [countryPickerVisible, setCountryPickerVisible] = useState(false);

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        try {
            await businessEndpoint.updateBillingInfo({
                name: values.name,
                taxId: values.taxId,
                shipping: {
                    name: values.name,
                    phone: values.phone,
                    address: {
                        city: values.city,
                        country: values.country,
                        line1: values.line1,
                        line2: values.line2,
                        postalCode: values.postalCode,
                        state: values.state,
                    },
                },
            });
        } catch (e) {
            log.error("billing info couldn't be updated", { e });
            notifications.error(t('somethingWentWrongError'));
        }
    });

    return (
        <Formik initialValues={initialValues} onSubmit={handleSubmit} enableReinitialize>
            {({ setFieldValue }) => (
                <>
                    <Screen bgColor={'backgroundPrimary'} stickyHeaderIndices={[0]}>
                        <ScreenHeader
                            leftSlot={<ScreenHeaderBackButton />}
                            headline={t('billingInfo')}
                            headlineTypographyProps={{ variant: 'title2' }}
                        />

                        {billingInfo.isLoading ? (
                            <VStack alignItems={'center'} justifyContent={'center'}>
                                <CircularProgress />
                            </VStack>
                        ) : (
                            <>
                                <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} mb={1.5}>
                                    {t('primaryInformation')}
                                </Typography>
                                <FormikTextField name={'name'} label={t('businessName')} mb={2} />
                                <FormikTextField name={'line1'} label={t('address1')} mb={2} />
                                <FormikTextField name={'line2'} label={t('address2')} mb={2} />
                                <FormikTextField name={'postalCode'} label={t('postalCode')} mb={2} />
                                <FormikTextField
                                    name={'country'}
                                    label={t('country')}
                                    mb={2}
                                    editable={false}
                                    onPressIn={() => setCountryPickerVisible(true)}
                                />
                                <CountryPicker
                                    visible={countryPickerVisible}
                                    onSelect={(dialCode) => {
                                        setFieldValue('country', dialCode.countryCode);
                                        setCountryPickerVisible(false);
                                    }}
                                    onRequestClose={() => setCountryPickerVisible(false)}
                                />
                                <FormikTextField name={'city'} label={t('city')} mb={2} />
                                <FormikTextField name={'taxId'} label={t('taxId')} />
                            </>
                        )}
                    </Screen>
                    <SettingsSaveFooter />
                </>
            )}
        </Formik>
    );
}
