import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Formik } from 'formik';
import { ReactElement, useMemo } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from 'styled-components/native';
import { businessEndpoint } from '@bookr-technologies/api';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { formatDate } from '@bookr-technologies/core';
import { ProfileAvatar } from '~/app/business/components/ProfileAvatar';
import { BottomSheetDatePicker } from '~/components/BottomSheetDatePicker';
import { FormikButton } from '~/components/ui/Button';
import { Container, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { FormikTextField, TextFieldAdornment } from '~/components/ui/TextField';
import { FormikLanguageField } from '~/components/ui/TextField/FormikLanguageField';
import { Typography } from '~/components/ui/Typography';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshOnFocus } from '~/hooks/useRefreshOnFocus';
import { useI18n } from '~/hooks/useTranslation';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { useAuthStore } from '~/store/useAuthStore';

export function SettingsProfileInfoScreen(): ReactElement {
    const t = useI18n();
    const theme = useTheme();
    const insets = useSafeAreaInsets();
    const containerPadding = useContainerPadding();
    const userBusiness = useUserBusiness();
    const user = useUser();
    const notifications = useNotifications();
    const log = useLogger('SettingsProfileInfoScreen');

    const resolveUser = useAuthStore((state) => state.resolveUser);

    const initialValues = useMemo(
        () => ({
            name: userBusiness.data?.name ?? '',
            phoneNumber: userBusiness.data?.phoneNumber ?? '',
            description: userBusiness.data?.description ?? '',
            facebook: userBusiness.data?.facebookURL ?? '',
            instagram: userBusiness.data?.instagramURL ?? '',
            website: userBusiness.data?.websiteURL ?? '',
            startingDate: userBusiness.data?.startingDate ?? '',
            language: user?.language ?? '',
        }),
        [
            user?.language,
            userBusiness.data?.description,
            userBusiness.data?.facebookURL,
            userBusiness.data?.instagramURL,
            userBusiness.data?.name,
            userBusiness.data?.phoneNumber,
            userBusiness.data?.startingDate,
            userBusiness.data?.websiteURL,
        ],
    );

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        if (!user?.uid) {
            notifications.error(t('somethingWentWrong'));
            return;
        }

        if (!userBusiness.data?.id) {
            notifications.error(t('somethingWentWrongError', { error: 'businessNotFound' }));
            return;
        }

        try {
            await Promise.all([
                businessEndpoint.update(userBusiness.data.id, {
                    name: values.name,
                    phoneNumber: values.phoneNumber,
                    description: values.description,
                    facebookURL: values.facebook,
                    instagramURL: values.instagram,
                    websiteURL: values.website,
                    startingDate: formatDate(values.startingDate, BottomSheetDatePicker.DateFormat),
                }),
                usersEndpoint.update(user.uid, {
                    language: values.language,
                }),
            ]);

            await Promise.all([userBusiness.refetch(), resolveUser()]);
            notifications.success(t('settingsUpdated'));
        } catch (e) {
            const message = getErrorMessage(e, 'somethingWentWrong');
            log.error('error updating business info', { e, message });

            notifications.error(t(message));
        }
    });

    useRefreshOnFocus(() => userBusiness.refetch());

    return (
        <Formik initialValues={initialValues} onSubmit={handleSubmit}>
            {({ values, handleChange }): ReactElement => (
                <>
                    <Screen stickyHeaderIndices={[0]} disablePadding>
                        <ScreenHeader
                            blur
                            px={containerPadding}
                            headlineTypographyProps={{ variant: 'title2' }}
                            leftSlotGridProps={{ alignItems: 'flex-start', direction: 'column' }}
                            leftSlot={
                                <>
                                    <ScreenHeaderBackButton />
                                    <Typography variant={'title2'} fontWeight={700} mt={1}>
                                        {t('businessInfo')}
                                    </Typography>
                                </>
                            }
                            rightSlot={<ProfileAvatar business />}
                        />

                        <Container pt={1}>
                            <VStack mb={4}>
                                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mb={1}>
                                    {t('primaryInformation')}
                                </Typography>
                                <FormikTextField name={'name'} mb={2} label={t('businessName')} />
                                <FormikTextField name={'phoneNumber'} mb={2} label={t('phoneNumber')} />
                                <FormikTextField
                                    name={'description'}
                                    minHeight={120}
                                    label={t('aboutBusiness')}
                                    multiline
                                />
                            </VStack>

                            <VStack mb={4}>
                                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mb={1}>
                                    {t('socialMedia')}
                                </Typography>
                                <FormikTextField
                                    name={'facebook'}
                                    label={t('facebookUrl')}
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <MaterialCommunityIcons name={'facebook'} size={24} />
                                        </TextFieldAdornment>
                                    }
                                    mb={2}
                                />
                                <FormikTextField
                                    name={'instagram'}
                                    label={t('instagramUrl')}
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <MaterialCommunityIcons name={'instagram'} size={24} />
                                        </TextFieldAdornment>
                                    }
                                    mb={2}
                                />
                                <FormikTextField
                                    name={'website'}
                                    label={t('websiteUrl')}
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <MaterialCommunityIcons name={'web'} size={24} />
                                        </TextFieldAdornment>
                                    }
                                />
                            </VStack>

                            <VStack mb={4}>
                                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mb={1}>
                                    {t('language')}
                                </Typography>
                                <FormikLanguageField name={'language'} />
                            </VStack>

                            <VStack mb={4}>
                                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mb={1}>
                                    {t('businessStartingDate')}
                                </Typography>
                                <BottomSheetDatePicker
                                    value={values.startingDate}
                                    label={t('chooseDay')}
                                    onChange={handleChange('startingDate')}
                                />
                                <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} mt={1}>
                                    {t('businessStartingDateDescription')}
                                </Typography>
                            </VStack>
                        </Container>
                    </Screen>
                    <Container bgColor={'backgroundPrimary'} pt={2} pb={2 + insets.bottom / theme.spacingOptions.size}>
                        <FormikButton label={t('saveChanges')} size={'large'} />
                    </Container>
                </>
            )}
        </Formik>
    );
}
