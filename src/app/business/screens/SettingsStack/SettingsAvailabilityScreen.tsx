import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useFormik } from 'formik';
import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { AvailabilitySettingsCard } from '~/app/business/components/AvailabilitySettingsCard';
import AcceptsNewClientsIcon from '~/assets/icons/availability/acceptsNewClients.png';
import AvailabilityIcon from '~/assets/icons/availability/availability.png';
import MinAppointmentTimeIcon from '~/assets/icons/availability/minAppointmentTime.png';
import MinCancelTimeIcon from '~/assets/icons/availability/minCancelTime.png';
import { BottomSheet } from '~/components/BottomSheet';
import { Button } from '~/components/ui/Button';
import { Container, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Switch } from '~/components/ui/Switch';
import { TextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useLogger } from '~/hooks/useLogger';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useAuthStore } from '~/store/useAuthStore';

enum Cards {
    AcceptsNewClients = 'acceptsNewClients',
    Availability = 'maxFutureDaysAppointment',
    TimeBeforeAppointment = 'minimumIntervalToCreateInMinutes',
    TimeBeforeCancel = 'minimumIntervalToCancelInMinutes',
}

export function SettingsAvailabilityScreen(): ReactElement {
    const { t } = useTranslation();
    const user = useUser();
    const updateUser = useAuthStore((state) => state.updateUser);
    const applyUserPartialUpdate = useAuthStore((state) => state.applyUserPartialUpdate);
    const notifications = useNotifications();
    const log = useLogger('SettingsAvailabilityScreen');

    const formik = useFormik({
        initialValues: {
            maxFutureDaysAppointment: user?.maxFutureDaysAppointment ?? 0,
            minimumIntervalToCreateInMinutes: user?.userSettings?.minimumIntervalToCreateInMinutes || 0,
            minimumIntervalToCancelInMinutes: user?.userSettings?.minimumIntervalToCancelInMinutes || 0,
            acceptsNewClients: user?.userSettings?.acceptsNewClients ?? true,
        },
        onSubmit: async (values) => {
            if (!user?.uid) {
                notifications.error(t('userNotFoundError'));
                return;
            }

            try {
                if (values.maxFutureDaysAppointment !== user?.maxFutureDaysAppointment) {
                    await updateUser({ maxFutureDaysAppointment: Number(values.maxFutureDaysAppointment) });
                }

                if (
                    values.minimumIntervalToCreateInMinutes !== user?.userSettings?.minimumIntervalToCreateInMinutes ||
                    values.minimumIntervalToCancelInMinutes !== user?.userSettings?.minimumIntervalToCancelInMinutes ||
                    values.acceptsNewClients !== user?.userSettings?.acceptsNewClients
                ) {
                    const userSettings = await usersEndpoint.updateSettings(user.uid, {
                        id: user.userSettings?.id,
                        minimumIntervalToCancelInMinutes: Number(values.minimumIntervalToCancelInMinutes),
                        minimumIntervalToCreateInMinutes: Number(values.minimumIntervalToCreateInMinutes),
                        acceptsNewClients: values.acceptsNewClients,
                    });
                    applyUserPartialUpdate({ userSettings });
                }

                notifications.success(t('changesWereSuccessfullySaved'));
                modal.close();
            } catch (e) {
                notifications.error(t('somethingWentWrong'));
                log.error('Failed to update user', { e });
            }
        },
    });

    const modal = useModal<Cards>();

    const bottomSheetHeadline = useMemo(() => {
        if (modal.context === Cards.TimeBeforeAppointment) {
            return t('minTimeBeforeAppointment');
        }

        if (modal.context === Cards.TimeBeforeCancel) {
            return t('minTimeBeforeCancel');
        }

        if (modal.context === Cards.AcceptsNewClients) {
            return t('acceptsNewClients');
        }

        return t('availabilityNrOfDays');
    }, [modal.context, t]);

    const bottomSheetFieldLabel = useMemo(() => {
        if (modal.context === Cards.TimeBeforeAppointment) {
            return t('minutes');
        }

        if (modal.context === Cards.TimeBeforeCancel) {
            return t('minutes');
        }

        if (modal.context === Cards.AcceptsNewClients) {
            return formik.values.acceptsNewClients ? t('enabled') : t('disabled');
        }

        return t('daysLabel');
    }, [formik.values.acceptsNewClients, modal.context, t]);

    const bottomSheetCaption = useMemo(() => {
        if (modal.context === Cards.TimeBeforeAppointment) {
            return t('minTimeBeforeAppointmentCardCaption');
        }

        if (modal.context === Cards.TimeBeforeCancel) {
            return t('minTimeBeforeCancelCardCaption');
        }

        if (modal.context === Cards.AcceptsNewClients) {
            return t('acceptsNewClientsCardCaption');
        }

        return t('availabilityCardCaption');
    }, [modal.context, t]);

    return (
        <Screen stickyHeaderIndices={[0]} safeArea bgColor={'backgroundSecondary'}>
            <ScreenHeader
                disableSafeViewArea
                bgColor={'backgroundSecondary'}
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('availability')}
                caption={t('availabilityScreenCaption')}
                headlineTypographyProps={{ variant: 'title2' }}
            />

            <AvailabilitySettingsCard
                caption={t('availability')}
                headline={t('numberOfDays', { value: formik.values.maxFutureDaysAppointment })}
                value={Cards.Availability}
                image={AvailabilityIcon}
                onPress={modal.openWithContext}
            />

            <AvailabilitySettingsCard
                caption={t('minTimeBeforeAppointment')}
                headline={t('numberOfMinutes', { value: formik.values.minimumIntervalToCreateInMinutes })}
                value={Cards.TimeBeforeAppointment}
                image={MinAppointmentTimeIcon}
                onPress={modal.openWithContext}
            />

            <AvailabilitySettingsCard
                caption={t('minTimeBeforeCancel')}
                headline={t('numberOfMinutes', { value: formik.values.minimumIntervalToCancelInMinutes })}
                value={Cards.TimeBeforeCancel}
                image={MinCancelTimeIcon}
                onPress={modal.openWithContext}
            />

            <AvailabilitySettingsCard
                caption={t('acceptsNewClients')}
                headline={user?.userSettings?.acceptsNewClients ? t('enabled') : t('disabled')}
                value={Cards.AcceptsNewClients}
                image={AcceptsNewClientsIcon}
                onPress={modal.openWithContext}
            />

            <BottomSheet {...modal.props}>
                <Container flex safeBottom>
                    <HStack justifyContent={'flex-end'} mb={1}>
                        <IconButton onPress={modal.close}>
                            <MaterialIcons name={'close'} />
                        </IconButton>
                    </HStack>

                    <Typography variant={'title2'} fontWeight={700} mb={2}>
                        {bottomSheetHeadline}
                    </Typography>

                    {modal.context === Cards.AcceptsNewClients ? (
                        <VStack mb={2}>
                            <HStack justifyContent={'space-between'} alignItems={'center'} mb={2}>
                                <Typography variant={'subhead'} fontWeight={500}>
                                    {bottomSheetFieldLabel}
                                </Typography>
                                <Switch
                                    value={formik.values.acceptsNewClients}
                                    onValueChange={(value) => {
                                        formik.setFieldValue('acceptsNewClients', value);
                                    }}
                                />
                            </HStack>
                        </VStack>
                    ) : (
                        <TextField
                            type={'number'}
                            label={bottomSheetFieldLabel}
                            bottomSheetField
                            mb={2}
                            value={String((modal.context ? formik.values[modal.context] : null) ?? '')}
                            onChangeText={formik.handleChange(modal.context ?? '_')}
                        />
                    )}

                    <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {bottomSheetCaption}
                    </Typography>
                    <Spacer />
                    <Button
                        variant={'contained'}
                        color={'accent'}
                        label={t('saveChanges')}
                        size={'large'}
                        onPress={formik.submitForm}
                        loading={formik.isSubmitting}
                    />
                </Container>
            </BottomSheet>
        </Screen>
    );
}
