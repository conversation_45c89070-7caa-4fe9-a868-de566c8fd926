import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useImagePicker } from '~/hooks/useImagePicker';
import { useNotifications } from '~/hooks/useNotifications';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { Gallery } from '../../components/Gallery';

export function SettingsProfileGalleryScreen(): ReactElement {
    const { t } = useTranslation();
    const notifications = useNotifications();
    const userBusiness = useUserBusiness();
    const containerPadding = useContainerPadding();
    const imagePicker = useImagePicker({
        aspect: [16, 9],
    });

    const handleDeleteImage = useEvent(async (image) => {
        await businessEndpoint.removeWorkplacePicture(image);
        notifications.success(t('imageDeleted'));
        userBusiness.refetch();
    });

    const handlePickImage = useEvent(async () => {
        await imagePicker.pick();
        const image = imagePicker.asUploadFile();

        if (image) {
            const data = new FormData();
            data.append('file', image as any, image.name);

            await businessEndpoint.attachWorkplacePicture(image, {
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'multipart/form-data',
                },
                transformRequest: () => data,
            });
            notifications.success(t('imageAdded'));

            userBusiness.refetch();
        }
    });

    const screenPadding = containerPadding - 0.75;

    return (
        <Screen stickyHeaderIndices={[0]} disablePadding>
            <ScreenHeader
                px={screenPadding}
                blur={{ intensity: 50 }}
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('gallery')}
                headlineTypographyProps={{
                    ml: 0.75,
                    variant: 'title2',
                }}
                headlineActions={
                    <Button
                        label={t('add')}
                        size={'xsmall'}
                        color={'accent'}
                        pr={1}
                        mr={0.75}
                        startIcon={<MaterialIcons name={'add'} />}
                        onPress={handlePickImage}
                        loading={imagePicker.loading}
                    />
                }
            />

            <VStack safeBottom px={screenPadding}>
                <Gallery images={userBusiness.data?.photos ?? []} onDelete={handleDeleteImage} />
            </VStack>
        </Screen>
    );
}
