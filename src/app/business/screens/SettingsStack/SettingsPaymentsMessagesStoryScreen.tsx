import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Story } from '~/app/common/components/Story';
import { StoryConfig } from '~/app/common/components/Story/StoryConfig';
import SmsStoryBackground from '~/assets/screenBackgrounds/SmsStoryBackground.jpg';
import { NavigateBack } from '~/components/NavigateBack';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';

export function SettingsPaymentsMessagesStoryScreen(): ReactElement {
    const stories = useMemo<StoryConfig[]>(() => [new StoryConfig(null, StoryContent, SmsStoryBackground)], []);

    return (
        <Screen disablePadding bgColor={'primary'} statusBarStyle={'light-content'}>
            <Story stories={stories} />
        </Screen>
    );
}

export function StoryContent(): ReactElement {
    const { t } = useTranslation();

    return (
        <>
            <VStack>
                <HStack justifyContent={'flex-end'} mb={1}>
                    <NavigateBack>
                        <IconButton color={'secondary'}>
                            <MaterialIcons name={'close'} />
                        </IconButton>
                    </NavigateBack>
                </HStack>
                <Typography variant={'title1'} color={'#fff'} fontSize={34} fontWeight={700} mb={1.5} width={240}>
                    {t('sendSmsMessages')}
                </Typography>

                <Typography color={'#fff'} fontWeight={500} width={320}>
                    {t('sendSmsMessagesCaption')}
                </Typography>
            </VStack>
            <Button
                label={t('buySmsMessages')}
                size={'large'}
                variant={'contained'}
                color={'secondary'}
                to={'/BusinessSettingsStack/SettingsPaymentsBuyMessagesScreen'}
            />
        </>
    );
}
