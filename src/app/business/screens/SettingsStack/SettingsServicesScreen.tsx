import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement, useState } from 'react';
import { useMutation } from 'react-query';
import { servicesEndpoint } from '@bookr-technologies/api/endpoints/servicesEndpoint';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import { BusinessServices } from '~/app/business/components/BusinessServices';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { Button } from '~/components/ui/Button';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useUser } from '~/hooks/useUser';
import { useAuthStore } from '~/store/useAuthStore';

export function SettingsServicesScreen(): ReactElement {
    const t = useI18n();
    const containerPadding = useContainerPadding();
    const user = useUser();
    const notifications = useNotifications();
    const { navigate } = useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsServicesScreen'>>();
    const log = useLogger('SettingsServicesScreen');
    const resolveUser = useAuthStore((state) => state.resolveUser);

    const [hasChanges, setHasChanges] = useState(false);
    const [rankedServices, setRankedServices] = useState<ServiceModel[]>([]);

    const handleOrder = useEvent((order: ServiceModel[]) => {
        setHasChanges(true);
        setRankedServices(order);
    });

    const handleNewService = useEvent(async () => navigate('SettingsServiceScreen'));

    const handleReload = useEvent(async () => {
        await resolveUser();
    });

    const changeOrderMutation = useMutation(
        'updateRank',
        async () => {
            await servicesEndpoint.updateRank(rankedServices);
            await resolveUser();
            setRankedServices([]);
            setHasChanges(false);
        },
        {
            onError: (e) => {
                const message = getErrorMessage(e, 'servicesOrderCouldNotBeSaved');
                log.error("services couldn't be ordered", { e, message });
                notifications.error(t(message));
            },
        },
    );

    const handleSavePress = useEvent(() => {
        changeOrderMutation.mutate();
    });

    return (
        <>
            <Screen disablePadding disableScroll>
                <ScreenHeader
                    px={containerPadding}
                    headline={t('services')}
                    leftSlot={<ScreenHeaderBackButton />}
                    headlineTypographyProps={{
                        variant: 'title2',
                    }}
                    headlineActions={
                        <Button
                            variant={'contained'}
                            color={'accent'}
                            startIcon={<MaterialIcons name="add" />}
                            label={t('add')}
                            size={'xsmall'}
                            pr={1.5}
                            onPress={handleNewService}
                        />
                    }
                />

                <BusinessServices items={user?.services ?? []} onOrder={handleOrder} onReload={handleReload} />
            </Screen>
            <SettingsSaveFooter
                disableFormik
                onPress={handleSavePress}
                loading={changeOrderMutation.isLoading}
                disabled={!hasChanges || !rankedServices?.length}
            />
        </>
    );
}
