import { useNavigation } from '@react-navigation/native';
import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Pressable } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SmsConfiguration } from '@bookr-technologies/api/models/SmsConfiguration';
import { Loader } from '~/app/common/components/Loader';
import { Button } from '~/components/ui/Button';
import { Checkbox } from '~/components/ui/Checkbox';
import { HStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';
import { useNotifications } from '~/hooks/useNotifications';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useUserBusiness } from '~/hooks/useUserBusiness';

const notificationTypes = [
    'NEW_APPOINTMENT',
    'APPOINTMENT_CANCELLED',
    'APPOINTMENT_RESCHEDULED',
    'APPOINTMENT_REMINDER',
];

export function SettingsPaymentsMessagesConfigureNotificationTypesScreen(): ReactElement {
    const { t } = useTranslation();
    const insets = useSafeAreaInsets(true);
    const business = useUserBusiness();
    const { goBack } = useNavigation();
    const notifications = useNotifications();

    const [selectedNotificationTypes, setSelectedNotificationTypes] = useState<string[]>(notificationTypes);

    const { data: smsConfiguration, isLoading } = useQuery({
        queryKey: [business?.data?.id, 'sms-configuration'],
        enabled: !!business?.data?.id,
        queryFn: () => businessEndpoint.getSmsConfiguration(business?.data?.id || ''),
        cacheTime: 0,
        onSuccess: (data) => setSelectedNotificationTypes(data.notificationTypes),
    });

    const updateSmsConfigurationMutation = useMutation({
        mutationFn: (data: SmsConfiguration) => businessEndpoint.updateSmsConfiguration(business?.data?.id || '', data),
        onSuccess: () => {
            notifications.success(t('changesWereSuccessfullySaved'));
            goBack();
        },
        onError: (error) => {
            console.log('sms error = ', error);
            notifications.error(t('somethingWentWrong'));
        },
    });

    if (!smsConfiguration || isLoading) {
        return <Loader />;
    }

    const handleNotificationTypePress = (notificationType: string) => {
        if (selectedNotificationTypes.includes(notificationType)) {
            setSelectedNotificationTypes(selectedNotificationTypes.filter((s) => s !== notificationType));
        } else {
            setSelectedNotificationTypes([...selectedNotificationTypes, notificationType]);
        }
    };

    const updateSmsConfiguration = () => {
        updateSmsConfigurationMutation.mutate({
            ...smsConfiguration,
            notificationTypes: selectedNotificationTypes,
        });
    };

    return (
        <Screen bgColor={'backgroundPrimary'}>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('chooseNotificationTypes')}
                caption={t('chooseNotificationTypesDescription')}
            />

            <FlatList
                contentContainerStyle={{}}
                data={notificationTypes}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                renderItem={({ item }): ReactElement => {
                    const isSelected = selectedNotificationTypes.includes(item);
                    const handlePress = () => handleNotificationTypePress(item);
                    return (
                        <Pressable onPress={handlePress}>
                            <HStack fullWidth alignItems={'center'} justifyContent={'space-between'} borderRadius={12}>
                                <Typography variant={'caption1'} fontWeight={500}>
                                    {t(`notificationTypes.${item}` as any)}
                                </Typography>
                                <Checkbox checked={isSelected} onPress={handlePress} noFeedback />
                            </HStack>
                        </Pressable>
                    );
                }}
                keyExtractor={(item): string => item}
            />

            <Spacer />
            <Button
                label={t('save')}
                size={'large'}
                variant={'contained'}
                color={'accent'}
                mb={insets.bottom}
                onPress={updateSmsConfiguration}
                loading={updateSmsConfigurationMutation.isLoading}
            />
        </Screen>
    );
}
