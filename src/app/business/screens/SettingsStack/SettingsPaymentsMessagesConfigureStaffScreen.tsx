import { useNavigation } from '@react-navigation/native';
import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Pressable } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SmsConfiguration } from '@bookr-technologies/api/models/SmsConfiguration';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Loader } from '~/app/common/components/Loader';
import { Button } from '~/components/ui/Button';
import { Checkbox } from '~/components/ui/Checkbox';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';
import { useNotifications } from '~/hooks/useNotifications';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useUserBusiness } from '~/hooks/useUserBusiness';

export function SettingsPaymentsMessagesConfigureStaffScreen(): ReactElement {
    const { t } = useTranslation();
    const insets = useSafeAreaInsets(true);
    const business = useUserBusiness();
    const { goBack } = useNavigation();
    const notifications = useNotifications();

    const [selectedStaffMembers, setSelectedStaffMembers] = useState<UserModel[]>([]);

    const { data: smsConfiguration, isLoading } = useQuery({
        queryKey: [business?.data?.id, 'sms-configuration'],
        enabled: !!business?.data?.id,
        queryFn: () => businessEndpoint.getSmsConfiguration(business?.data?.id || ''),
        cacheTime: 0,
        onSuccess: (data) => setSelectedStaffMembers(data.staffMembers),
    });

    const updateSmsConfigurationMutation = useMutation({
        mutationFn: (data: SmsConfiguration) => businessEndpoint.updateSmsConfiguration(business?.data?.id || '', data),
        onSuccess: () => {
            notifications.success(t('changesWereSuccessfullySaved'));
            goBack();
        },
        onError: (error) => {
            console.log('sms error = ', error);
            notifications.error(t('somethingWentWrong'));
        },
    });

    if (!smsConfiguration || isLoading) {
        return <Loader />;
    }

    const handleStaffMemberPress = (staffMember: UserModel) => {
        if (selectedStaffMembers.map((s) => s.uid).includes(staffMember.uid)) {
            setSelectedStaffMembers(selectedStaffMembers.filter((s) => s.uid !== staffMember.uid));
        } else {
            setSelectedStaffMembers([...selectedStaffMembers, staffMember]);
        }
    };

    const updateSmsConfiguration = () => {
        updateSmsConfigurationMutation.mutate({
            ...smsConfiguration,
            staffMembers: selectedStaffMembers,
        });
    };

    return (
        <Screen bgColor={'backgroundPrimary'}>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('chooseStaffMembers')}
                caption={t('chooseStaffMembersDescription')}
            />

            <FlatList
                contentContainerStyle={{}}
                data={business?.data?.staffMembers || []}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                renderItem={({ item }): ReactElement => {
                    const isSelected = selectedStaffMembers.map((s) => s.uid).includes(item.uid);
                    const handlePress = () => handleStaffMemberPress(item);
                    return (
                        <Pressable onPress={handlePress}>
                            <HStack
                                py={1}
                                fullWidth
                                alignItems={'center'}
                                justifyContent={'space-between'}
                                borderRadius={12}
                            >
                                <VStack>
                                    <Typography variant={'callout'} fontWeight={500}>
                                        {item.displayName}
                                    </Typography>
                                    <Typography variant={'caption1'} color={'textSecondary'} fontWeight={500}>
                                        {t(`accountTypes.${item.accountType}` as any)}
                                    </Typography>
                                </VStack>
                                <Checkbox checked={isSelected} onPress={handlePress} noFeedback />
                            </HStack>
                        </Pressable>
                    );
                }}
                keyExtractor={(item): string => item.uid}
            />

            <Spacer />
            <Button
                label={t('save')}
                size={'large'}
                variant={'contained'}
                color={'accent'}
                mb={insets.bottom}
                onPress={updateSmsConfiguration}
                loading={updateSmsConfigurationMutation.isLoading}
            />
        </Screen>
    );
}
