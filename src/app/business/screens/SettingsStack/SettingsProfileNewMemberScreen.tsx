import { useFormik } from 'formik';
import { ReactElement } from 'react';
import { string } from 'yup';
import { businessEndpoint } from '@bookr-technologies/api';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { PrivilegeBasedAccess } from '~/components/PrivilegeBasedAccess/PrivilegeBasedAccess';
import { FormikButton } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { TextField } from '~/components/ui/TextField';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useValidationSchema } from '~/hooks/useValidationSchema';

export function SettingsProfileNewMemberScreen(): ReactElement {
    const t = useI18n();
    const notifications = useNotifications();
    const log = useLogger('SettingsProfileNewMemberScreen');

    const validation = useValidationSchema({
        email: string().email().required(t('requiredField')),
    });

    const formik = useFormik({
        ...validation,
        initialValues: {
            email: '',
        },
        async onSubmit(values) {
            try {
                await businessEndpoint.inviteUser({
                    email: values.email,
                });
                notifications.success(t('staffMemberInvitationSent'));
                await formik.setFieldValue('email', '', true);
            } catch (e) {
                const message = getErrorMessage(e, 'errorSendingInvitation');
                log.error('error sending invitation', { e, message });
                notifications.error(t(message));
            }
        },
    });

    return (
        <Screen keyboardShouldPersistTaps={'never'} bounces={false}>
            <ScreenHeader
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('inviteStaffMember')}
                caption={t('inviteStaffMemberDescription')}
                headlineTypographyProps={{ variant: 'title2' }}
            />
            <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]}>
                <VStack flex safeBottom>
                    <TextField
                        type={'email'}
                        label={t('emailAddress')}
                        value={formik.values.email}
                        onChangeText={formik.handleChange('email')}
                    />
                    <Spacer />
                    <FormikButton label={t('sendInvitation')} size={'large'} color={'accent'} formik={formik} />
                </VStack>
            </PrivilegeBasedAccess>
        </Screen>
    );
}
