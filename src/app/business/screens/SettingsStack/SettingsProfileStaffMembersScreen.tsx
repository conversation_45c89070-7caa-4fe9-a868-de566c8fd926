import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement, useState } from 'react';
import { businessEndpoint } from '@bookr-technologies/api';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { StaffMembers } from '~/app/business/components/StaffMembers';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useUserBusiness } from '~/hooks/useUserBusiness';

export function SettingsProfileStaffMembersScreen(): ReactElement {
    const t = useI18n();
    const log = useLogger('SettingsProfileStaffMembersScreen');
    const notifications = useNotifications();
    const { navigate } =
        useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsProfileStaffMembersScreen'>>();
    const userBusiness = useUserBusiness();
    const staffMembers = userBusiness.data?.staffMembers ?? [];

    const loader = useLoading();
    const [hasChanges, setHasChanges] = useState(false);
    const [rankedMembers, setRankedMembers] = useState<Array<Pick<UserModel, 'uid'>>>([]);

    const handleOrder = useEvent((order: Array<Pick<UserModel, 'uid'>>) => {
        setHasChanges(true);
        setRankedMembers(order);
    });

    const handleReload = useEvent(async () => {
        await userBusiness.refetch();
    });

    const handlePressAdd = useEvent(() => navigate('SettingsProfileNewMemberScreen'));

    const handleSubmit = useEvent(async () =>
        loader.from(async () => {
            try {
                await businessEndpoint.staffRank(rankedMembers);
                await userBusiness.refetch();
                setRankedMembers([]);
                setHasChanges(false);
                notifications.success(t('settingsUpdated'));
            } catch (e) {
                const message = getErrorMessage(e, 'staffMembersOrderCouldNotBeSaved');
                log.error("staff members couldn't be ordered", { e, message });
                notifications.error(t(message));
            }
        }),
    );

    return (
        <>
            <Screen disableScroll>
                <ScreenHeader
                    leftSlot={<ScreenHeaderBackButton />}
                    headline={t('staffMembers')}
                    headlineTypographyProps={{ variant: 'title2' }}
                    headlineActions={
                        <Button
                            label={t('add')}
                            size={'xsmall'}
                            color={'accent'}
                            pr={1}
                            startIcon={<MaterialIcons name={'add'} />}
                            onPress={handlePressAdd}
                        />
                    }
                />

                <VStack flex mx={-1}>
                    <StaffMembers
                        loading={userBusiness.isLoading}
                        items={staffMembers}
                        onOrder={handleOrder}
                        onReload={handleReload}
                    />
                </VStack>
            </Screen>

            <SettingsSaveFooter
                disableFormik
                onPress={handleSubmit}
                loading={loader.isLoading()}
                disabled={!hasChanges || !rankedMembers?.length}
            />
        </>
    );
}
