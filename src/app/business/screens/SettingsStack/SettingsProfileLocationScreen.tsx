import { Formik } from 'formik';
import { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api';
import { FormikLocationMapView } from '~/app/common/components/FormikLocationMapView';
import { FormikButton } from '~/components/ui/Button';
import { HStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { getBusinessKey } from '~/hooks/useBusiness';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { reactQueryClient } from '~/lib/clients/reactQuery';

export function SettingsProfileLocationScreen(): ReactElement {
    const { t } = useTranslation();
    const user = useUser();
    const validation = useValidationSchema({});
    const notifications = useNotifications();
    const userBusiness = useUserBusiness();

    const [loading, setLoading] = useState(false);

    const initialValues = useMemo(
        () => ({
            formattedAddress: userBusiness.data?.formattedAddress,
            latitude: userBusiness.data?.latitude,
            latitudeDelta: userBusiness.data?.latitudeDelta,
            longitude: userBusiness.data?.longitude,
            longitudeDelta: userBusiness.data?.longitudeDelta,
        }),
        [userBusiness.data],
    );

    const handleSubmit = useEvent(async (values: Partial<typeof initialValues>) => {
        if (!user?.business?.id) {
            notifications.error(t('weCouldNotFindYourBusiness'));
            return;
        }

        const data = await businessEndpoint.update(user.business.id, values);
        notifications.success(t('locationWasUpdated'));

        reactQueryClient.setQueryData(getBusinessKey(user.business.id), data);
    });

    return (
        <Formik
            validateOnMount
            enableReinitialize
            initialValues={initialValues}
            onSubmit={handleSubmit}
            {...validation}
        >
            <Screen disableScroll>
                <ScreenHeader
                    leftSlot={<ScreenHeaderBackButton />}
                    headlineTypographyProps={{ variant: 'title2' }}
                    headline={t('businessLocation')}
                />

                <FormikLocationMapView onLoading={setLoading} isLoading={userBusiness.isLoading}>
                    <HStack>
                        <FormikButton
                            color={'primary'}
                            size={'large'}
                            label={t('saveLocation')}
                            fullWidth
                            loading={loading || userBusiness.isLoading}
                        />
                    </HStack>
                </FormikLocationMapView>
            </Screen>
        </Formik>
    );
}
