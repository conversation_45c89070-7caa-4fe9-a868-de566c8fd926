import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'react-native';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { VirtualTourModal } from '~/components/VirtualTourModal';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';
import { useModal } from '~/hooks/useModal';
import { useUser } from '~/hooks/useUser';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { useApplicationStore } from '~/store/useApplicationStore';

export function SettingsVirtual3DTourScreen(): ReactElement {
    const { t } = useTranslation();
    const confirmModal = useModal();
    const demoModal = useModal();
    const containerPadding = useContainerPadding();
    const user = useUser();
    const loading = useLoading();
    const requestVirtualTour = useApplicationStore((state) => state.requestVirtualTour);

    const handleRequestPress = useEvent(() => {
        if (user?.business?.id) {
            loading.from(requestVirtualTour(user.business.id));
            confirmModal.open();
        }
    });

    const handleDemoOpen = useEvent(() => {
        logAnalyticsEvent(AnalyticsEvent.Showcase3DVirtualTourOpened);
        demoModal.open();
    });

    return (
        <Screen stickyHeaderIndices={[0]} safeArea>
            <ScreenHeader
                disableSafeViewArea
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('virtual3dTourHeadline')}
                caption={t('virtual3dTourCaption')}
                headlineTypographyProps={{ variant: 'title2' }}
            />

            <VStack flex mx={-containerPadding}>
                <Image
                    resizeMode={'contain'}
                    source={require('~/assets/screenBackgrounds/virtualTourBackground.png')}
                    style={{ width: '100%', height: '100%' }}
                />
            </VStack>

            <Button
                size={'large'}
                color={'accent'}
                startIcon={<MaterialIcons name={'view-in-ar'} />}
                label={t('askForVirtualTour')}
                align={'space-between'}
                onPress={handleRequestPress}
                loading={loading.any()}
                keepIcons
            />
            <Button
                mt={1}
                variant={'text'}
                size={'large'}
                color={'typography.disabled'}
                label={t('viewDemo')}
                onPress={handleDemoOpen}
            />

            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Info}
                headline={t('requestHasBeenSent')}
                subHeadline={t('ourTeamWillContactYouAsSoonAsPossible')}
                primaryText={t('gotIt')}
                {...confirmModal.props}
            />

            <VirtualTourModal source={'https://my.matterport.com/show/?m=4EhzVVWn31c'} {...demoModal.props} />
        </Screen>
    );
}
