import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ReactElement } from 'react';
import { BusinessApplicationSettingsParamList } from '~/RoutesParams';
import { SettingsOnlinePaymentsSetupScreen } from '~/app/business/screens/SettingsStack/SettingsOnlinePaymentsSetupScreen';
import { SettingsPaymentsMessagesConfigureNotificationTypesScreen } from '~/app/business/screens/SettingsStack/SettingsPaymentsMessagesConfigureNotificationTypesScreen';
import { SettingsPaymentsMessagesConfigureStaffScreen } from '~/app/business/screens/SettingsStack/SettingsPaymentsMessagesConfigureStaffScreen';
import { SettingsAvailabilityScreen } from './SettingsAvailabilityScreen';
import { SettingsMyInfoScreen } from './SettingsMyInfoScreen';
import { SettingsPaymentsBillingScreen } from './SettingsPaymentsBillingScreen';
import { SettingsPaymentsBuyMessagesScreen } from './SettingsPaymentsBuyMessagesScreen';
import { SettingsPaymentsMessagesScreen } from './SettingsPaymentsMessagesScreen';
import { SettingsPaymentsMessagesStoryScreen } from './SettingsPaymentsMessagesStoryScreen';
import { SettingsPaymentsScreen } from './SettingsPaymentsScreen';
import { SettingsPaymentsSubscriptionScreen } from './SettingsPaymentsSubscriptionScreen';
import { SettingsProfileCategoriesScreen } from './SettingsProfileCategoriesScreen';
import { SettingsProfileGalleryScreen } from './SettingsProfileGalleryScreen';
import { SettingsProfileInfoScreen } from './SettingsProfileInfoScreen';
import { SettingsProfileLocationScreen } from './SettingsProfileLocationScreen';
import { SettingsProfileNewMemberScreen } from './SettingsProfileNewMemberScreen';
import { SettingsProfileScreen } from './SettingsProfileScreen';
import { SettingsProfileStaffMembersScreen } from './SettingsProfileStaffMembersScreen';
import { SettingsProfileWorkingProgramScreen } from './SettingsProfileWorkingProgramScreen';
import { SettingsPromoLinkScreen } from './SettingsPromoLinkScreen';
import { SettingsServiceScreen } from './SettingsServiceScreen';
import { SettingsServicesScreen } from './SettingsServicesScreen';
import { SettingsVirtual3DTourScreen } from './SettingsVirtual3DTourScreen';

const Stack = createNativeStackNavigator<BusinessApplicationSettingsParamList>();

export function SettingsStack(): ReactElement {
    return (
        <Stack.Navigator initialRouteName="SettingsProfileScreen" screenOptions={{ headerShown: false }}>
            <Stack.Screen name="SettingsProfileScreen" component={SettingsProfileScreen} />
            <Stack.Screen name="SettingsProfileCategoriesScreen" component={SettingsProfileCategoriesScreen} />
            <Stack.Screen name="SettingsProfileGalleryScreen" component={SettingsProfileGalleryScreen} />
            <Stack.Screen name="SettingsProfileInfoScreen" component={SettingsProfileInfoScreen} />
            <Stack.Screen name="SettingsProfileLocationScreen" component={SettingsProfileLocationScreen} />
            <Stack.Screen name="SettingsProfileNewMemberScreen" component={SettingsProfileNewMemberScreen} />
            <Stack.Screen name="SettingsProfileStaffMembersScreen" component={SettingsProfileStaffMembersScreen} />
            <Stack.Screen name="SettingsProfileWorkingProgramScreen" component={SettingsProfileWorkingProgramScreen} />

            <Stack.Screen name="SettingsPaymentsScreen" component={SettingsPaymentsScreen} />
            <Stack.Screen name="SettingsOnlinePaymentsSetupScreen" component={SettingsOnlinePaymentsSetupScreen} />
            <Stack.Screen name="SettingsPaymentsBillingScreen" component={SettingsPaymentsBillingScreen} />
            <Stack.Screen name="SettingsPaymentsMessagesScreen" component={SettingsPaymentsMessagesScreen} />
            <Stack.Screen
                name="SettingsPaymentsMessagesConfigureStaffScreen"
                component={SettingsPaymentsMessagesConfigureStaffScreen}
            />
            <Stack.Screen
                name="SettingsPaymentsMessagesConfigureNotificationTypesScreen"
                component={SettingsPaymentsMessagesConfigureNotificationTypesScreen}
            />
            <Stack.Screen name="SettingsPaymentsBuyMessagesScreen" component={SettingsPaymentsBuyMessagesScreen} />
            <Stack.Screen name="SettingsPaymentsMessagesStoryScreen" component={SettingsPaymentsMessagesStoryScreen} />
            <Stack.Screen name="SettingsPaymentsSubscriptionScreen" component={SettingsPaymentsSubscriptionScreen} />

            <Stack.Screen name="SettingsVirtual3DTourScreen" component={SettingsVirtual3DTourScreen} />

            <Stack.Screen name="SettingsPromoLinkScreen" component={SettingsPromoLinkScreen} />

            <Stack.Screen name="SettingsMyInfoScreen" component={SettingsMyInfoScreen} />

            <Stack.Screen name="SettingsAvailabilityScreen" component={SettingsAvailabilityScreen} />

            <Stack.Screen name="SettingsServiceScreen" component={SettingsServiceScreen} />
            <Stack.Screen name="SettingsServicesScreen" component={SettingsServicesScreen} />
        </Stack.Navigator>
    );
}
