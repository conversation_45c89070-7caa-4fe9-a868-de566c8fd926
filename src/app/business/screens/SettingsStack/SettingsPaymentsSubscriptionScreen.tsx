import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { businessEndpoint } from '@bookr-technologies/api';
import { formatDate } from '@bookr-technologies/core';
import { BillingHistory } from '~/app/business/components/BillingHistory';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { CurrentSubscriptionPlan } from '../../components/CurrentSubscriptionPlan';

export function SettingsPaymentsSubscriptionScreen(): ReactElement {
    const { t } = useTranslation();
    const theme = useTheme();
    const confirmModal = useModal();
    const subscription = useQuery('session/business/subscription', () => businessEndpoint.getSubscription());
    const refreshControl = useRefreshControl(() => subscription.refetch());
    const { navigate } = useNavigation();

    const handleCancelSubscription = useEvent(async () => {
        if (subscription?.data?.commission) {
            return;
        }
        await businessEndpoint.cancelSubscription(subscription?.data?.stripeSubscriptionId ?? '');
        await subscription.refetch();
        navigate('ChooseSubscriptionPlanScreen', {});
    });

    return (
        <>
            <Screen bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
                <ScreenHeader
                    bgColor={'backgroundSecondary'}
                    leftSlot={<ScreenHeaderBackButton />}
                    headline={t('currentPlan')}
                    headlineTypographyProps={{ variant: 'title2' }}
                />

                <VStack scrollable scrollViewProps={{ refreshControl }}>
                    {subscription.isLoading ? (
                        <VStack alignItems={'center'} justifyContent={'center'}>
                            <CircularProgress />
                        </VStack>
                    ) : subscription.data && !subscription.error ? (
                        <>
                            <CurrentSubscriptionPlan subscription={subscription.data} />

                            {subscription.data?.nextPaymentDate && (
                                <Paper
                                    bgColor={'backgroundPrimary'}
                                    direction={'row'}
                                    alignItems={'center'}
                                    flexWrap={'nowrap'}
                                    p={2}
                                    mt={2}
                                >
                                    <MaterialIcons name={'event'} size={20} color={theme.palette.accent.main} />
                                    <VStack pl={2} flex>
                                        {/* <Typography variant={'subhead'} fontWeight={500} mb={0.5}>*/}
                                        {/*    123Lei*/}
                                        {/* </Typography>*/}
                                        <Typography variant={'footnote'} fontWeight={500}>
                                            {t('nextPaymentDate', {
                                                value: subscription.data.nextPaymentDate
                                                    ? formatDate(subscription.data.nextPaymentDate, 'll')
                                                    : t('unknown'),
                                            })}
                                        </Typography>
                                    </VStack>
                                </Paper>
                            )}

                            <BillingHistory />
                        </>
                    ) : (
                        <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                            {t('noSubscriptionFound')}
                        </Typography>
                    )}
                </VStack>
            </Screen>
            {!subscription.data?.commission && (
                <SettingsSaveFooter
                    disableFormik
                    label={t('cancelSubscription')}
                    variant={'subtle'}
                    onPress={confirmModal.open}
                />
            )}
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                headline={t('cancelSubscriptionConfirmationTitle')}
                subHeadline={t('cancelSubscriptionConfirmationCaption')}
                onAction={handleCancelSubscription}
                primaryText={t('yesCancel')}
                secondaryText={t('no')}
                snapPoints={['50%']}
                {...confirmModal.props}
            />
        </>
    );
}
