import { Formik } from 'formik';
import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { string } from 'yup';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { ProfileAvatar } from '~/app/business/components/ProfileAvatar';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { FormikTextField } from '~/components/ui/TextField';
import { FormikPhoneField } from '~/components/ui/TextField/FormikPhoneField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { str } from '~/lib/utils/string';
import { useAuthStore } from '~/store/useAuthStore';

export function SettingsMyInfoScreen(): ReactElement {
    const { t } = useTranslation();
    const log = useLogger('SettingsMyInfoScreen');
    const user = useUser();
    const notifications = useNotifications();

    const resolveUser = useAuthStore((state) => state.resolveUser);

    const initialValues = useMemo(
        () => ({
            displayName: str(user?.displayName),
            phoneNumber: str(user?.phoneNumber),
            email: str(user?.email),
        }),
        [user],
    );

    const validation = useValidationSchema({
        email: string().email().required(t('requiredField')),
        phoneNumber: string().required(t('requiredField')),
    });

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        try {
            if (!user?.uid) {
                notifications.error(t('userNotFoundError'));
                return;
            }

            await usersEndpoint.update(user.uid, {
                displayName: values.displayName,
                phoneNumber: values.phoneNumber,
            });
            await resolveUser();

            notifications.success(t('changesWereSuccessfullySaved'));
        } catch (e) {
            log.error('Error updating user', { e });
            notifications.error(t('somethingWentWrong'));
        }
    });

    return (
        <Formik initialValues={initialValues} onSubmit={handleSubmit} {...validation}>
            <>
                <Screen stickyHeaderIndices={[0]}>
                    <ScreenHeader>
                        <HStack alignItems={'center'} justifyContent={'space-between'}>
                            <VStack alignItems={'flex-start'}>
                                <ScreenHeaderBackButton />
                                <Typography variant={'title2'} fontWeight={700} mt={1}>
                                    {t('yourInformation')}
                                </Typography>
                            </VStack>

                            <ProfileAvatar />
                        </HStack>
                    </ScreenHeader>
                    <Typography mt={2} mb={1} variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                        {t('personalDetails')}
                    </Typography>

                    <FormikTextField type={'email'} label={t('email')} name={'email'} mb={2} disabled />
                    <FormikTextField label={t('displayName')} name={'displayName'} mb={2} />
                    <FormikPhoneField name={'phoneNumber'} />
                </Screen>
                <SettingsSaveFooter validateDirty />
            </>
        </Formik>
    );
}
