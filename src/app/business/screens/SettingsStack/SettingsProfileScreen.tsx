import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api';
import { BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Switch } from '~/components/ui/Switch';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useHandler } from '~/hooks/useHandler';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { useWatchValue } from '~/hooks/useWatch';

export function SettingsProfileScreen(): ReactElement {
    const { t } = useTranslation();
    const userBusiness = useUserBusiness();
    const handler = useHandler();
    const { navigate } = useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsProfileScreen'>>();
    const [isHidden, setIsHidden] = useState(userBusiness.data?.hidden);

    const handleHiddenChange = useEvent(async (hidden) => {
        if (userBusiness.data?.id) {
            setIsHidden(hidden);
            await businessEndpoint.update(userBusiness.data?.id, { hidden });
            await userBusiness.refetch();
        }
    });

    useWatchValue(userBusiness.data?.hidden, setIsHidden);

    return (
        <Screen bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
            <ScreenHeader
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('businessProfile')}
                bgColor={'backgroundSecondary'}
            />

            <Paper bgColor={'backgroundPrimary'} fullWidth mb={2}>
                <List>
                    <ListItemButton onPress={handler(() => navigate('SettingsProfileInfoScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'store'} />
                        </ListItemIcon>
                        <ListItemText primary={t('businessInfo')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsProfileLocationScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'location-on'} />
                        </ListItemIcon>
                        <ListItemText primary={t('businessLocation')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsProfileCategoriesScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'category'} />
                        </ListItemIcon>
                        <ListItemText primary={t('categoriesLabel')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsProfileGalleryScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'image'} />
                        </ListItemIcon>
                        <ListItemText primary={t('gallery')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsProfileWorkingProgramScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'event-note'} />
                        </ListItemIcon>
                        <ListItemText primary={t('workingSchedule')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsProfileStaffMembersScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'group'} />
                        </ListItemIcon>
                        <ListItemText primary={t('staffMembers')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                </List>
            </Paper>

            {userBusiness.isLoading ? (
                <HStack mt={2} alignItems={'center'} justifyContent={'center'}>
                    <CircularProgress />
                </HStack>
            ) : (
                <Paper
                    direction={'row'}
                    bgColor={'backgroundPrimary'}
                    fullWidth
                    mb={2}
                    flexWrap={'nowrap'}
                    flexShrink={1}
                    p={2}
                >
                    <MaterialIcons name={'visibility-off'} size={24} />
                    <VStack px={2} flex>
                        <Typography variant={'subhead'} fontWeight={600} mb={0.5}>
                            {t('hideBusiness')}
                        </Typography>
                        <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                            {t('hideBusinessDescription')}
                        </Typography>
                    </VStack>
                    <Switch value={isHidden} onValueChange={handleHiddenChange} />
                </Paper>
            )}
        </Screen>
    );
}
