import { Formik } from 'formik';
import { ReactElement, useCallback, useMemo } from 'react';
import { useQuery } from 'react-query';
import * as Yup from 'yup';
import { businessEndpoint } from '@bookr-technologies/api';
import { categoriesEndpoint } from '@bookr-technologies/api/endpoints/categoriesEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { FormikCheckbox } from '~/components/ui/Checkbox';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { useValidationSchema } from '~/hooks/useValidationSchema';

export function SettingsProfileCategoriesScreen(): ReactElement {
    const log = useLogger('SettingsProfileCategoriesScreen');
    const t = useI18n();
    const notifications = useNotifications();
    const userBusiness = useUserBusiness();
    const categories = useQuery('business/categories/available', () => categoriesEndpoint.fetchAvailableCategories());

    const initialValues = useMemo(
        () => ({
            categories: (userBusiness.data?.categories ?? []).map(({ name }) => name),
        }),
        [userBusiness.data],
    );

    const validation = useValidationSchema({
        categories: Yup.array().required(t('requiredField')).min(1, t('requiredField')),
    });

    const handleSubmit = useCallback(
        async (values: typeof initialValues) => {
            try {
                await businessEndpoint.updateCategories(values.categories.map((name) => ({ name })));

                notifications.success(t('categoriesWereUpdatedSuccessfully'));
            } catch (e) {
                const message = getErrorMessage(e, 'categoriesCouldNotBeUpdated');
                log.error("categories couldn't be updated", { e, message });
                notifications.error(t(message));
            }
        },
        [log, notifications, t],
    );

    return (
        <Formik
            validateOnMount
            enableReinitialize
            initialValues={initialValues}
            onSubmit={handleSubmit}
            {...validation}
        >
            <>
                <Screen stickyHeaderIndices={[0]}>
                    <ScreenHeader
                        leftSlot={<ScreenHeaderBackButton />}
                        headline={t('settingsProfileCategoriesScreenHeadline')}
                        caption={t('settingsProfileCategoriesScreenCaption')}
                        headlineTypographyProps={{ variant: 'title2' }}
                    />

                    <VStack mx={-2}>
                        {categories.data?.map((category) => (
                            <FormikCheckbox
                                key={category.id}
                                name={'categories'}
                                label={t(`categories.${category.code.toLowerCase()}`)}
                                value={category.code}
                            />
                        ))}
                    </VStack>
                </Screen>
                <SettingsSaveFooter />
            </>
        </Formik>
    );
}
