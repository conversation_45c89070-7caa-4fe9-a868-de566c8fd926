import { RouteProp, useRoute } from '@react-navigation/native';
import { Formik } from 'formik';
import { ReactElement, useMemo } from 'react';
import { businessEndpoint } from '@bookr-technologies/api';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { BusinessApplicationSettingsParamList } from '~/RoutesParams';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { WorkingProgramDay } from '~/components/WorkingProgramDay';
import { VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { useAuthStore } from '~/store/useAuthStore';
import { workingProgramFromWorkingHours, workingProgramToWorkingHours } from '~/store/useBusinessStore';

export function SettingsProfileWorkingProgramScreen(): ReactElement {
    const t = useI18n();
    const { params } =
        useRoute<RouteProp<BusinessApplicationSettingsParamList, 'SettingsProfileWorkingProgramScreen'>>();
    const containerPadding = useContainerPadding();
    const user = useUser();
    const userBusiness = useUserBusiness();
    const notifications = useNotifications();
    const log = useLogger('SettingsProfileWorkingProgramScreen');

    const resolveUser = useAuthStore((state) => state.resolveUser);

    const isEmployee = params?.isEmployee || user?.accountType !== AccountType.BusinessOwner;

    const initialValues = useMemo(() => {
        const workingHours = !isEmployee ? userBusiness.data?.workingHours : user?.workingHours;

        return {
            workingProgram: workingProgramFromWorkingHours(workingHours ?? []),
        };
    }, [isEmployee, user?.workingHours, userBusiness.data?.workingHours]);

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        try {
            const hours = workingProgramToWorkingHours(values.workingProgram);

            if (!isEmployee) {
                await businessEndpoint.updateWorkingHours(hours);
            } else {
                await usersEndpoint.updateWorkingHours(hours);
            }

            notifications.success(t('settingsUpdated'));

            await Promise.all([userBusiness.refetch(), resolveUser()]);
        } catch (e) {
            const message = getErrorMessage(e, 'workingProgramCouldNotBeSaved');
            log.error("working program couldn't be updated", { e, message });
            notifications.error(t(message));
        }
    });

    return (
        <Formik initialValues={initialValues} onSubmit={handleSubmit} enableReinitialize>
            <>
                <Screen stickyHeaderIndices={[0]} disablePadding>
                    <ScreenHeader
                        px={containerPadding}
                        leftSlot={<ScreenHeaderBackButton />}
                        headline={t('workingSchedule')}
                        caption={
                            !isEmployee
                                ? t('settingsProfileWorkingProgramBusinessDescription')
                                : t('settingsProfileWorkingProgramStaffMemberDescription')
                        }
                        headlineTypographyProps={{ variant: 'title2' }}
                    />
                    <VStack flex px={containerPadding}>
                        <VStack flex mb={2} mx={-containerPadding}>
                            {!userBusiness.isLoading ? (
                                <VStack flex>
                                    <WorkingProgramDay
                                        label={t('days.monday')}
                                        name={'workingProgram.monday'}
                                        divider
                                    />
                                    <WorkingProgramDay
                                        label={t('days.tuesday')}
                                        name={'workingProgram.tuesday'}
                                        divider
                                    />
                                    <WorkingProgramDay
                                        label={t('days.wednesday')}
                                        name={'workingProgram.wednesday'}
                                        divider
                                    />
                                    <WorkingProgramDay
                                        label={t('days.thursday')}
                                        name={'workingProgram.thursday'}
                                        divider
                                    />
                                    <WorkingProgramDay
                                        label={t('days.friday')}
                                        name={'workingProgram.friday'}
                                        divider
                                    />
                                    <WorkingProgramDay
                                        label={t('days.saturday')}
                                        name={'workingProgram.saturday'}
                                        divider
                                    />
                                    <WorkingProgramDay label={t('days.sunday')} name={'workingProgram.sunday'} />
                                </VStack>
                            ) : null}
                        </VStack>
                    </VStack>
                </Screen>
                <SettingsSaveFooter />
            </>
        </Formik>
    );
}
