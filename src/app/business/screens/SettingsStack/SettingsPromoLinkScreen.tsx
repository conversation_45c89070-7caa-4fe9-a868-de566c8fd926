import * as Clipboard from 'expo-clipboard';
import React, { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components';
import Button from '~/components/ui/Button';
import { Grid, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Screen, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { TextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useUserBusiness } from '~/hooks/useUserBusiness';

const DISPLAY_BASE_URL = 'bookr.ro/b/';

export function SettingsPromoLinkScreen(): ReactElement {
    const { t } = useTranslation();
    const theme = useTheme();
    const business = useUserBusiness();
    const [promoLinkCopied, setPromoLinkCopied] = useState(false);

    const promoLink = useMemo(() => {
        if (!business?.data?.slug) {
            return undefined;
        }

        return DISPLAY_BASE_URL.concat(business.data.slug);
    }, [business]);

    const handleCopyLink = useEvent(async () => {
        setPromoLinkCopied(await Clipboard.setStringAsync('https://' + promoLink));
    });

    return (
        <Screen px={3} safeArea disableScroll>
            <VStack alignItems={'flex-start'}>
                <ScreenHeaderBackButton />
                <Typography variant={'title2'} fontWeight={700} mt={1}>
                    {t('promoLink')}
                </Typography>
            </VStack>
            <TextField
                mt={4.25}
                placeholder={promoLink}
                placeholderTextColor={theme.palette.contentSecondary.main}
                mb={1}
                disabled
            />
            <Button
                size={'medium'}
                variant={'contained'}
                label={promoLinkCopied ? t('linkCopied') : t('copyLink')}
                onPress={handleCopyLink}
                startIcon={<Icon name={'insert-link'} />}
                mb={4}
                disabled={!promoLink}
            />
            <Grid bgColor={'backgroundSecondary'} flexDirection={'row'} py={2} pl={2} pr={3} borderRadius={16}>
                <Icon name={'info-outline'} color={'accent'} size={20} />
                <VStack ml={1.5}>
                    <Typography variant={'subhead'} fontWeight={500} mb={0.5}>
                        {t('promoLinkUse')}
                    </Typography>
                    <Typography variant={'caption1'} color={'contentTertiary'}>
                        {t('promoLinkDescription')}
                    </Typography>
                </VStack>
            </Grid>
        </Screen>
    );
}
