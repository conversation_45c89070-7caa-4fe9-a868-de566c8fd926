import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Slider } from '@miblanchard/react-native-slider';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { Formik, FormikProps } from 'formik';
import { castArray } from 'lodash';
import moment from 'moment';
import { hsl, parseToHsl, readableColor } from 'polished';
import { ReactElement, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useTheme } from 'styled-components/native';
import { servicesEndpoint } from '@bookr-technologies/api/endpoints/servicesEndpoint';
import { IntegrationTypeEnum } from '@bookr-technologies/api/integrations/integration.enum';
import { formatDate } from '@bookr-technologies/core';
import { BusinessApplicationSettingsParamList, BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import { SettingsSaveFooter } from '~/app/business/components/SettingsSaveFooter';
import { BottomSheet } from '~/components/BottomSheet';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { Box } from '~/components/ui/Box';
import { FormikCheckbox } from '~/components/ui/Checkbox';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { Container, Grid, HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Menu } from '~/components/ui/Menu';
import { MenuItem } from '~/components/ui/MenuItem';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Tab } from '~/components/ui/Tab';
import { TabContainer } from '~/components/ui/TabContainer';
import { useTabs } from '~/components/ui/TabContainer/TabContainerContext';
import { Tabs } from '~/components/ui/Tabs';
import { FormikTextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useUser } from '~/hooks/useUser';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { isBusinessOwner } from '~/lib/utils/appointments';
import { num } from '~/lib/utils/number';
import { useAuthStore } from '~/store/useAuthStore';
import { useBusinessStore } from '~/store/useBusinessStore';

enum ScreenTabs {
    Service = 'service',
    Subscription = 'subscription',
}

export function SettingsServiceScreen(): ReactElement {
    const { t } = useTranslation();
    const containerPadding = useContainerPadding();
    const confirmModal = useModal();
    const notifications = useNotifications();
    const { navigate } = useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsServiceScreen'>>();
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const { params } = useRoute<RouteProp<BusinessApplicationSettingsParamList, 'SettingsServiceScreen'>>();
    const isNew = !params?.service?.id;
    const user = useUser();

    const handleDelete = useEvent(async () => {
        if (!params?.service?.id || !user?.business?.id) {
            notifications.error(t('somethingWentWrong'));
            return;
        }

        try {
            await servicesEndpoint.destroy(params.service.id);
            notifications.success(t('serviceDeleted'));
            await resolveUser();
            navigate('SettingsServicesScreen');
        } catch (e) {
            notifications.error(t('somethingWentWrong'));
        }
    });

    return (
        <TabContainer value={ScreenTabs.Service}>
            <Screen disableScroll disablePadding stickyHeaderIndices={[0]}>
                <ScreenHeader
                    leftSlot={<ScreenHeaderBackButton />}
                    rightSlot={
                        !isNew && (
                            <>
                                <IconButton color="contentTertiary" onPress={confirmModal.open}>
                                    <Icon name="delete" />
                                </IconButton>
                                <BottomSheetAlert
                                    variant={BottomSheetAlertVariant.Error}
                                    subHeadline={t('deleteServiceHeadline')}
                                    primaryText={t('delete')}
                                    secondaryText={t('cancel')}
                                    onAction={handleDelete}
                                    {...confirmModal.props}
                                />
                            </>
                        )
                    }
                    px={containerPadding}
                >
                    {isNew && (
                        <Tabs mt={1}>
                            <Tab value={ScreenTabs.Service}>{t('service')}</Tab>
                            <Tab value={ScreenTabs.Subscription}>{t('subscription')}</Tab>
                        </Tabs>
                    )}
                </ScreenHeader>
                <Content />
            </Screen>
        </TabContainer>
    );
}

function Content(): ReactElement {
    const styles = useStyles();
    const t = useI18n();
    const tabs = useTabs();
    const theme = useTheme();
    const containerPadding = useContainerPadding();
    const currencyModal = useModal();
    const notifications = useNotifications();
    const { navigate, goBack, canGoBack } =
        useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsServiceScreen'>>();
    const { params } = useRoute<RouteProp<BusinessApplicationSettingsParamList, 'SettingsServiceScreen'>>();
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const formikRef = useRef<FormikProps<any>>(null);
    const currencyFieldRef = useRef<View>(null);
    const start = useMemo(() => moment().startOf('day').add(10, 'hours'), []);
    const user = useUser();
    const business = useBusinessStore((state) => state.currentBusiness);
    const [isDaysInAdvanceCanBeBookedHelpOpen, setIsDaysInAdvanceCanBeBookedHelpOpen] = useState(false);

    const initialValues = useMemo(() => {
        const service = params?.service;
        const color = service?.color || randomColor();
        const { hue } = parseToHsl(color);

        return {
            serviceId: service?.id,
            name: service?.name || '',
            currency: service?.currency || 'LEI',
            description: service?.description || '',
            hiddenFromClients: !!service?.hiddenFromClients,
            price: String(service?.price || ''),
            numberOfSessions: String(service?.numberOfSessions || ''),
            duration: String(service?.duration || ''),
            breakBetweenServices: String(service?.breakBetweenServices || ''),
            acceptsOnlinePayments: !!service?.acceptsOnlinePayments,
            onlineEvent: !!service?.onlineEvent,
            daysInAdvanceSessionsCanBeBooked: String(service?.daysInAdvanceSessionsCanBeBooked || ''),

            color,
            hue,
        };
    }, [params?.service]);

    const sessionsSchema = useEvent((mixed, number) => {
        if (tabs?.currentTab === ScreenTabs.Subscription) {
            return number()
                .required(t('requiredField'))
                .transform(num)
                .min(2, t('minNumberOfSessionsError', { value: 1 }));
        }

        return mixed();
    });

    const validationProps = useValidationSchema(({ string, mixed, number }) => ({
        name: string().required(t('requiredField')),
        currency: string().required(t('requiredField')),
        price: number().transform(num).required(t('requiredField')).min(0, t('priceMustBePositive')),
        numberOfSessions: mixed()
            .transform(num)
            .when(() => sessionsSchema(mixed, number)),
        duration: number().transform(num).required(t('requiredField')).min(1),
    }));

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        if (!user?.business?.id) {
            notifications.error(t('somethingWentWrong'));
            return;
        }

        // noinspection PointlessBooleanExpressionJS
        const data = {
            name: values.name,
            currency: values.currency,
            description: values.description,
            color: values.color,
            hiddenFromClients: !!values.hiddenFromClients,
            price: num(values.price),
            numberOfSessions: num(values.numberOfSessions),
            daysInAdvanceSessionsCanBeBooked: num(values.daysInAdvanceSessionsCanBeBooked),
            duration: num(values.duration),
            breakBetweenServices: num(values.breakBetweenServices),
            acceptsOnlinePayments: !!values.acceptsOnlinePayments,
            onlineEvent: !!values.onlineEvent,
            onlineEventIntegrationType: IntegrationTypeEnum.GoogleCalendar,
        };

        try {
            if (values.serviceId) {
                await servicesEndpoint.update(values.serviceId, data);
            } else {
                await servicesEndpoint.create(data);
            }
            await resolveUser();
            notifications.success(t('changesWereSuccessfullySaved'));

            if (params?.comingFromCreateAppointment && canGoBack()) {
                goBack();
            } else {
                navigate('SettingsServicesScreen');
            }
        } catch (e) {
            notifications.error(t('somethingWentWrong'));
        }
    });

    const handlePressRandomColor = useEvent(() => {
        const color = randomColor();
        const h = parseToHsl(color);

        formikRef.current?.setFieldValue('hue', h.hue);
        formikRef.current?.setFieldValue('color', color);
    });

    const handleColorSlider = useEvent((values: number | number[]) => {
        const value = castArray(values)[0];
        formikRef.current?.setFieldValue('hue', value);
        formikRef.current?.setFieldValue('color', hsl(value, 1, 0.5));
    });

    const handleCurrencyPress = useEvent((item, value) => {
        formikRef.current?.setFieldValue('currency', value);
    });

    const handlePressOnlinePayments = useEvent(() => {
        navigate('SettingsOnlinePaymentsSetupScreen');
    });

    const businessOwnerHasConnectedStripeAccount = business?.staffMembers?.find(({ accountType }) =>
        isBusinessOwner(accountType),
    )?.stripeConnectedAccountId;

    const haveOnlinePaymentConfigured =
        user?.stripeConnectedAccountId || // the user has configured his own stripe account
        (!business?.multipleStripeConnectedAccountsEnabled && businessOwnerHasConnectedStripeAccount); // or the business has a single stripe account configured

    const isSubscription =
        tabs?.currentTab === ScreenTabs.Subscription || Number(params?.service?.numberOfSessions) > 0;
    const isNew = !params?.service?.id;

    useEffect(() => {
        formikRef.current?.validateField('numberOfSessions');
    }, [formikRef, tabs?.currentTab]);

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            innerRef={formikRef}
            enableReinitialize
            validateOnMount
            initialErrors={{}}
            {...validationProps}
        >
            {({ values }): ReactElement => (
                <>
                    <VStack flex scrollable pt={isNew ? 2 : 0} px={containerPadding}>
                        <Typography variant="subhead" fontWeight={500} mb={0.5}>
                            {t('generalInformation')}
                        </Typography>
                        <FormikTextField
                            name="name"
                            label={t('serviceName')}
                            startAdornment={
                                <TextFieldAdornment variant={'start'}>
                                    <Icon name="edit" size={20} color="contentSecondary" />
                                </TextFieldAdornment>
                            }
                        />
                        <HStack my={2}>
                            <Grid xs pr={1}>
                                <FormikTextField
                                    type="numeric"
                                    name="price"
                                    label={t('price')}
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <Icon name="credit-card" size={20} color="contentSecondary" />
                                        </TextFieldAdornment>
                                    }
                                />
                            </Grid>
                            <Grid xs pl={1}>
                                <FormFieldBox
                                    onPress={currencyModal.open}
                                    px={2}
                                    flexDirection={'row'}
                                    alignItems={'center'}
                                    alignContent={'center'}
                                    flexWrap={'nowrap'}
                                    ref={currencyFieldRef}
                                >
                                    <Icon name="monetization-on" size={20} color="contentSecondary" />
                                    <VStack pl={1.5}>
                                        <Typography
                                            variant={values.currency ? 'caption2' : 'body'}
                                            fontSize={values.currency ? 12 : 16}
                                            fontWeight={500}
                                            color="textSecondary"
                                        >
                                            {t('currency')}
                                        </Typography>

                                        {values.currency ? (
                                            <Typography variant="subhead" fontWeight={500}>
                                                {t(`currencies.${values.currency.toUpperCase()}`)}
                                            </Typography>
                                        ) : null}
                                    </VStack>
                                </FormFieldBox>

                                <Menu
                                    selected={values.currency}
                                    onPress={handleCurrencyPress}
                                    anchorEl={currencyFieldRef}
                                    {...currencyModal.props}
                                >
                                    <MenuItem value={'LEI'}>{t('currencies.Lei')}</MenuItem>
                                    <MenuItem value={'EUR'}>{t('currencies.EUR')}</MenuItem>
                                    <MenuItem value={'USD'}>{t('currencies.USD')}</MenuItem>
                                    <MenuItem value={'GBP'}>{t('currencies.GBP')}</MenuItem>
                                </Menu>
                            </Grid>
                        </HStack>
                        {isSubscription && (
                            <FormikTextField
                                type="number"
                                name="numberOfSessions"
                                label={t('numberOfSessions')}
                                mb={2}
                                startAdornment={
                                    <TextFieldAdornment variant={'start'}>
                                        <Icon name="featured-play-list" size={20} color="contentSecondary" />
                                    </TextFieldAdornment>
                                }
                            />
                        )}
                        <FormikTextField
                            type="number"
                            name="duration"
                            label={t('duration')}
                            startAdornment={
                                <TextFieldAdornment variant={'start'}>
                                    <Icon name="timer" size={20} color="contentSecondary" />
                                </TextFieldAdornment>
                            }
                            mb={2}
                        />

                        <Typography variant="subhead" fontWeight={500} mb={0.5}>
                            {t('breakBetweenServices')}
                        </Typography>
                        <FormikTextField
                            type="number"
                            name="breakBetweenServices"
                            label={t('breakBetweenServices')}
                            startAdornment={
                                <TextFieldAdornment variant={'start'}>
                                    <Icon
                                        name="timeline-clock-outline"
                                        variant="secondary"
                                        size={20}
                                        color="contentSecondary"
                                    />
                                </TextFieldAdornment>
                            }
                            mb={2}
                        />

                        <Typography variant="subhead" fontWeight={500} mb={0.5}>
                            {t('serviceDescription')}
                        </Typography>
                        <FormikTextField
                            name="description"
                            label={t('serviceDescription')}
                            startAdornment={
                                <TextFieldAdornment variant={'start'}>
                                    <Icon name="description" size={20} color="contentSecondary" />
                                </TextFieldAdornment>
                            }
                            multiline
                        />

                        {isSubscription && (
                            <>
                                <HStack alignItems={'center'}>
                                    <Typography variant="subhead" fontWeight={500} mb={0.5} mt={2} mr={0.5}>
                                        {t('daysInAdvanceCanBeBooked')}
                                    </Typography>
                                    <Icon
                                        name={'help'}
                                        size={16}
                                        color="contentTertiary"
                                        onPress={() => setIsDaysInAdvanceCanBeBookedHelpOpen(true)}
                                    />
                                </HStack>
                                <FormikTextField
                                    name="daysInAdvanceSessionsCanBeBooked"
                                    label={t('daysLabel')}
                                    type="number"
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <Icon name="event-busy" size={20} color="contentSecondary" />
                                        </TextFieldAdornment>
                                    }
                                />
                            </>
                        )}

                        <HStack mt={2} mb={0.5} alignItems="center" justifyContent="space-between">
                            <Typography variant="subhead" fontWeight={500}>
                                {t('addColorToYourService')}
                            </Typography>

                            <IconButton color="accent" onPress={handlePressRandomColor}>
                                <MaterialIcons name="refresh" />
                            </IconButton>
                        </HStack>
                        <Box bgColor={values.color} p={1} borderRadius={8} mb={1}>
                            <Typography variant="footnote" fontWeight={500} color={readableColor(values.color)}>
                                {t('johnDoe')}
                            </Typography>
                            <Typography
                                variant="footnote"
                                fontWeight={500}
                                color={readableColor(values.color)}
                                mb={0.5}
                            >
                                {values.name || 'Service Name'}
                            </Typography>
                            <Typography variant="caption2" fontWeight={500} color={readableColor(values.color)}>
                                {`${formatDate(start, 'LT')} - ${formatDate(
                                    start.clone().add(values.duration, 'minutes'),
                                    'LT',
                                )}`}
                            </Typography>
                        </Box>

                        <Slider
                            animationType="timing"
                            animateTransitions
                            minimumValue={0}
                            maximumValue={360}
                            value={values.hue}
                            onValueChange={handleColorSlider}
                            trackStyle={styles.track}
                            thumbStyle={styles.thumb}
                            thumbTintColor={theme.palette.accent.main}
                            minimumTrackTintColor={theme.palette.backgroundTertiary.main}
                        />

                        <Typography variant="subhead" fontWeight={500} mt={2} mb={0.5}>
                            {t('serviceCreatesVideoCall')}
                        </Typography>
                        <FormikCheckbox
                            name="onlineEvent"
                            label={t('createAVideoCallForThisService')}
                            labelProps={{ ml: 2 }}
                        />
                        <Typography variant="subhead" fontWeight={500} mt={2} mb={0.5}>
                            {t('serviceAvailableForOnlinePayments')}
                        </Typography>
                        {!haveOnlinePaymentConfigured && (
                            <Typography
                                variant="footnote"
                                fontWeight={500}
                                color={'textSecondary'}
                                textDecorationLine={'underline'}
                                onPress={handlePressOnlinePayments}
                            >
                                {t('acceptsOnlinePaymentInformation')}
                            </Typography>
                        )}
                        {haveOnlinePaymentConfigured && (
                            <FormikCheckbox
                                name="acceptsOnlinePayments"
                                label={t('acceptsOnlinePayment')}
                                labelProps={{ ml: 2 }}
                            />
                        )}
                        <Typography variant="subhead" fontWeight={500} mt={2} mb={0.5}>
                            {t('serviceHiddenForClients')}
                        </Typography>
                        <FormikCheckbox name="hiddenFromClients" label={t('hideService')} labelProps={{ ml: 2 }} />
                    </VStack>

                    <SettingsSaveFooter validateDirty label={!params?.service ? t('createAService') : undefined} />
                    <BottomSheet
                        open={isDaysInAdvanceCanBeBookedHelpOpen}
                        onClose={() => setIsDaysInAdvanceCanBeBookedHelpOpen(false)}
                    >
                        <Container flex safeBottom={2}>
                            <Typography variant="title2" fontWeight={700} mb={1}>
                                {t('daysInAdvanceCanBeBooked')}
                            </Typography>
                            <Typography variant="subhead" fontWeight={500} color="textSecondary" mb={6}>
                                {t('daysInAdvanceSessionsCanBeBookedDescription')}
                            </Typography>
                        </Container>
                    </BottomSheet>
                </>
            )}
        </Formik>
    );
}

function randomColor(): string {
    return hsl(Math.floor(Math.random() * 360), 1, 0.5);
}

const useStyles = makeStyles(({ theme }) => ({
    track: {
        backgroundColor: theme.palette.backgroundTertiary.main,
        height: 16,
        borderRadius: 8,
    },
    thumb: {
        borderWidth: 3,
        borderColor: theme.palette.backgroundPrimary.main,
        height: 28,
        width: 28,
        borderRadius: 14,
        ...theme.shadows.small,
    },
}));
