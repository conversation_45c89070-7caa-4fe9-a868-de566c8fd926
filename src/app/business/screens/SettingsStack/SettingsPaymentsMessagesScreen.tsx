import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import { Button } from '~/components/ui/Button';
import { Divider } from '~/components/ui/Divider';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Spacer } from '~/components/ui/Spacer';
import { Switch } from '~/components/ui/Switch';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useUserBusiness } from '~/hooks/useUserBusiness';

export function SettingsPaymentsMessagesScreen(): ReactElement {
    const { t } = useTranslation();
    const insets = useSafeAreaInsets(true);
    const business = useUserBusiness();
    const { navigate } =
        useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsPaymentsMessagesScreen'>>();

    const [smsRemindersEnabled, setSmsRemindersEnabled] = useState(business.data?.smsRemindersEnabled || false);

    const handleInfoPress = useEvent(() => navigate('SettingsPaymentsMessagesStoryScreen'));

    const handleEnableSmsReminders = async (smsRemindersEnabled: boolean) => {
        setSmsRemindersEnabled(smsRemindersEnabled);
        if (!business.data?.id) return;

        await businessEndpoint.update(business.data.id, { smsRemindersEnabled: smsRemindersEnabled });
    };

    const handleChooseStaffMembers = useEvent(() => navigate('SettingsPaymentsMessagesConfigureStaffScreen'));

    const handleChooseNotificationTypes = useEvent(() =>
        navigate('SettingsPaymentsMessagesConfigureNotificationTypesScreen'),
    );

    return (
        <Screen bgColor={'backgroundPrimary'}>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                leftSlot={<ScreenHeaderBackButton />}
                rightSlot={
                    <IconButton disablePadding onPress={handleInfoPress}>
                        <MaterialIcons name={'info-outline'} />
                    </IconButton>
                }
                headline={t('textMessages')}
                caption={t('textMessagesDescription')}
                headlineTypographyProps={{ variant: 'title1' }}
                captionTypographyProps={{ variant: 'subhead' }}
            />

            <Paper
                bgColor={'backgroundPrimary'}
                mt={2}
                direction={'row'}
                alignItems={'center'}
                justifyContent={'space-between'}
            >
                <Typography variant={'footnote'} fontWeight={500}>
                    {t('enableSmsReminders')}
                </Typography>

                <Switch
                    style={{
                        transform: [{ scaleX: 0.85 }, { scaleY: 0.85 }],
                    }}
                    value={smsRemindersEnabled}
                    onValueChange={handleEnableSmsReminders}
                />
            </Paper>

            <Divider />

            <Paper
                bgColor={'backgroundPrimary'}
                direction={'row'}
                alignItems={'center'}
                justifyContent={'space-between'}
                my={1}
            >
                <HStack flex alignItems={'center'}>
                    <IconButton variant={'text'} color={'accent'}>
                        <MaterialIcons name={'email'} />
                    </IconButton>
                    <VStack flex ml={1}>
                        <Typography variant={'title3'} fontWeight={700} mb={0.25} textTransform={'capitalize'}>
                            {t('numberOfLeftSms', { value: business.data?.totalSMS })}
                        </Typography>
                        <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                            {t('smsBalance')}
                        </Typography>
                    </VStack>
                </HStack>
            </Paper>

            <Divider size={8} />

            <Paper bgColor={'backgroundPrimary'} my={1}>
                <VStack flex mb={1}>
                    <Typography variant={'title3'} fontWeight={700} mb={0.5}>
                        {t('configureSms')}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                        {t('configureSmsDescription')}
                    </Typography>
                </VStack>
                <TouchableOpacity onPress={handleChooseStaffMembers}>
                    <HStack mt={2} justifyContent={'space-between'} alignItems={'center'}>
                        <HStack flex alignItems={'center'}>
                            <MaterialCommunityIcons name={'account-group'} size={20} />
                            <Typography variant={'footnote'} ml={1} fontWeight={500}>
                                {t('chooseStaffMembers')}
                            </Typography>
                        </HStack>
                        <MaterialIcons name={'chevron-right'} size={20} />
                    </HStack>
                </TouchableOpacity>
                <Divider my={2} />
                <TouchableOpacity onPress={handleChooseNotificationTypes}>
                    <HStack mt={2} justifyContent={'space-between'} alignItems={'center'}>
                        <HStack flex alignItems={'center'}>
                            <MaterialCommunityIcons name={'calendar-sync'} size={20} />
                            <Typography variant={'footnote'} ml={1} fontWeight={500}>
                                {t('chooseNotificationTypes')}
                            </Typography>
                        </HStack>
                        <MaterialIcons name={'chevron-right'} size={20} />
                    </HStack>
                </TouchableOpacity>
            </Paper>

            <Spacer />
            <Button
                label={t('buySmsMessages')}
                size={'large'}
                variant={'contained'}
                color={'accent'}
                mb={insets.bottom}
                to={'/BusinessSettingsStack/SettingsPaymentsBuyMessagesScreen'}
            />
        </Screen>
    );
}
