import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { AxiosError } from 'axios';
import { Formik } from 'formik';
import { tint } from 'polished';
import React, { ReactElement, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { useTheme } from 'styled-components/native';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { Accordion } from '~/app/common/components/Accordion/Accordion';
import { FormikSelect } from '~/app/common/components/FormikSelect';
import { StripeUrlModal } from '~/components/StripeUrlModal/StripeUrlModal';
import { Button, FormikButton } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Grid } from '~/components/ui/Grid/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { StripeConnectAccountSuccess } from '~/data/applicationData';
import { useNotifications } from '~/hooks/useNotifications';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';
import { isBusinessOwner } from '~/lib/utils/appointments';
import { useAuthStore } from '~/store/useAuthStore';

export function SettingsOnlinePaymentsSetupScreen(): ReactElement {
    const { t } = useTranslation();
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const applyUserPartialUpdate = useAuthStore((state) => state.applyUserPartialUpdate);

    const user = useUser();
    const userBusiness = useUserBusiness();
    const insets = useSafeAreaInsets(true);
    const theme = useTheme();
    const notifications = useNotifications();
    const connectAccountMutation = useMutation(
        'getStripeConnectUrl',
        () => {
            if (!user?.stripeConnectedAccountId) {
                return paymentEndpoint.createConnectAccount({
                    returnUrl: StripeConnectAccountSuccess,
                });
            }

            const path = (user?.stripeConnectedAccountId || '') + '?uid=' + user?.uid;
            return paymentEndpoint.reauthorizeConnectAccount(path);
        },
        {
            onSuccess: (data) => setUrl(data?.url || ''),
            onError: function (error) {
                if ((error as AxiosError).response?.status === 403) {
                    notifications.error(t('ownerMustEnablePaymentsForAllStaff'));
                } else {
                    notifications.error(getErrorMessage(error));
                }
            },
        },
    );
    const [url, setUrl] = useState('');
    const [isVatModalVisible, setIsVatModalVisible] = useState(false);

    let color: 'accent' | 'error';
    let icon: 'verified' | 'report';
    let text: string;
    const faqs = [];

    for (let i = 0; i < 8; i++) {
        faqs.push({
            // eslint-disable-next-line
            question: t(`faqQuestion${i + 1}` as any),
            // eslint-disable-next-line
            answer: t(`faqAnswer${i + 1}` as any),
        });
    }

    if (user?.stripeConnectedAccountId) {
        color = 'accent';
        icon = 'verified';
        text = t('configured');
    } else {
        color = 'error';
        icon = 'report';
        text = t('needsSetup');
    }

    const onClose = async () => {
        setUrl('');
        await resolveUser();
        await userBusiness.refetch();
    };

    const initialValues = {
        vatCode:
            (userBusiness?.data?.multipleStripeConnectedAccountsEnabled
                ? user?.userSettings?.vatCode
                : userBusiness?.data?.staffMembers?.find(({ accountType }) => isBusinessOwner(accountType))
                      ?.userSettings?.vatCode) || '',
        isVatRegistered:
            (userBusiness?.data?.multipleStripeConnectedAccountsEnabled
                ? user?.userSettings?.isVatRegistered
                : userBusiness?.data?.staffMembers?.find(({ accountType }) => isBusinessOwner(accountType))
                      ?.userSettings?.isVatRegistered) || false,
    };

    const handleSubmitUserVatCode = useCallback(
        async (values: typeof initialValues) => {
            if (!user) {
                return;
            }
            const userSettings = await usersEndpoint.updateSettings(user.uid, {
                id: user.userSettings?.id,
                minimumIntervalToCancelInMinutes: Number(user.userSettings?.minimumIntervalToCancelInMinutes),
                minimumIntervalToCreateInMinutes: Number(user.userSettings?.minimumIntervalToCreateInMinutes),
                vatCode: values.vatCode,
                isVatRegistered: values.isVatRegistered,
            });
            applyUserPartialUpdate({ userSettings });
            setIsVatModalVisible(false);
            connectAccountMutation.mutate();
        },
        [applyUserPartialUpdate, connectAccountMutation, user],
    );

    return (
        <>
            {!isVatModalVisible && (
                <Screen bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
                    <ScreenHeader
                        leftSlot={<ScreenHeaderBackButton />}
                        headline={t('onlinePayments')}
                        bgColor={'backgroundSecondary'}
                    />

                    <Paper bgColor={'backgroundPrimary'} fullWidth mb={2} flexWrap={'nowrap'} p={2}>
                        <Typography variant={'subhead'} fontWeight={700} lineHeight={22}>
                            {t('onlinePaymentsHeader')}
                        </Typography>
                        <Typography variant={'footnote'} fontWeight={500} lineHeight={19} mt={1.2}>
                            {t('onlinePaymentsDescription')}
                        </Typography>
                    </Paper>

                    <Typography variant={'title3'} fontWeight={700} lineHeight={25} mt={1}>
                        {t('faq')}
                    </Typography>

                    <Grid pb={5}>
                        {faqs.map((faq, index) => (
                            <Accordion title={faq.question as string} key={index} mt={1}>
                                <Typography variant={'footnote'} fontWeight={500} lineHeight={19}>
                                    {faq.answer as string}
                                </Typography>
                            </Accordion>
                        ))}
                        <Accordion title={t('contactPayments')} mt={1}>
                            <Typography variant={'footnote'} fontWeight={500} lineHeight={19}>
                                {t('contactPaymentsDescription')}
                            </Typography>
                        </Accordion>
                    </Grid>
                </Screen>
            )}

            {!isVatModalVisible && (
                <Paper bgColor={'backgroundPrimary'} fullWidth flexWrap={'nowrap'} px={3} pt={1.2} pb={insets.bottom}>
                    <Paper
                        bgColor={tint(0.9, theme.mixins.getColor(color, 'accent'))}
                        fullWidth
                        flexWrap={'nowrap'}
                        justifyContent={'center'}
                        alignItems={'center'}
                        flexDirection={'row'}
                        px={3}
                        py={2}
                    >
                        <MaterialIcons name={icon} size={24} color={theme.palette[color].main} />
                        <Typography
                            variant={'subhead'}
                            color={theme.palette[color].main}
                            fontWeight={400}
                            fontSize={15}
                            lineHeight={22}
                            pl={1}
                        >
                            {t('onlinePayments')}: {text}
                        </Typography>
                    </Paper>
                    <Button
                        mt={1}
                        variant={'contained'}
                        color={'primary'}
                        size={'large'}
                        label={user?.stripeConnectedAccountId ? t('updateOnlinePayment') : t('configureOnlinePayment')}
                        loading={connectAccountMutation.isLoading}
                        onPress={(): void => setIsVatModalVisible(true)}
                    />
                </Paper>
            )}
            {isVatModalVisible && (
                <Screen stickyHeaderIndices={[0]}>
                    <ScreenHeader
                        leftSlot={
                            <IconButton size={'large'} disablePadding onPress={() => setIsVatModalVisible(false)}>
                                <MaterialIcons name={'arrow-back'} />
                            </IconButton>
                        }
                    >
                        <Typography variant={'title1'} fontWeight={700} fontSize={22} pt={1}>
                            {t('addYourVatInformation')}
                        </Typography>

                        <Typography variant={'subhead'} mt={1}>
                            {t('addYourVatInformationMessage')}
                        </Typography>
                    </ScreenHeader>
                    <VStack flex bgColor={'backgroundPrimary'} pt={1} pb={insets.bottom}>
                        <Formik initialValues={initialValues} onSubmit={handleSubmitUserVatCode}>
                            {({ getFieldMeta }) => (
                                <>
                                    <FormikTextField name={'vatCode'} label={t('taxId')} mb={2} />
                                    <Typography variant={'footnote'} fontWeight={500} lineHeight={19} mb={1}>
                                        {t('isVatRegistered')}
                                    </Typography>
                                    <FormikSelect
                                        items={[
                                            { label: t('yes'), value: true },
                                            { label: t('no'), value: false },
                                        ]}
                                        name={'isVatRegistered'}
                                    />
                                    <FormikButton
                                        mt={2}
                                        label={t('save')}
                                        size={'large'}
                                        disabled={!getFieldMeta('vatCode').value}
                                    />
                                </>
                            )}
                        </Formik>
                    </VStack>
                </Screen>
            )}
            {url ? <StripeUrlModal uri={url} onClose={onClose} /> : null}
        </>
    );
}
