import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api';
import { BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import BookrIcon from '~/components/icons/Bookr';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Switch } from '~/components/ui/Switch';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useHandler } from '~/hooks/useHandler';
import { useUserBusiness } from '~/hooks/useUserBusiness';

export function SettingsPaymentsScreen(): ReactElement {
    const { t } = useTranslation();
    const handler = useHandler();
    const userBusiness = useUserBusiness();
    const { navigate } = useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsPaymentsScreen'>>();
    const [isMultiplePaymentsEnabled, setIsMultiplePaymentsEnabled] = useState(
        userBusiness.data?.multipleStripeConnectedAccountsEnabled,
    );

    const handleMultiStripeEnabled = useEvent(async (multipleStripeConnectedAccountsEnabled) => {
        if (userBusiness.data?.id) {
            setIsMultiplePaymentsEnabled(multipleStripeConnectedAccountsEnabled);
            await businessEndpoint.update(userBusiness.data?.id, { multipleStripeConnectedAccountsEnabled });
            await userBusiness.refetch();
        }
    });

    return (
        <Screen bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
            <ScreenHeader
                leftSlot={<ScreenHeaderBackButton />}
                headline={t('payments')}
                bgColor={'backgroundSecondary'}
            />

            <Paper bgColor={'backgroundPrimary'} fullWidth mb={2}>
                <List>
                    <ListItemButton onPress={handler(() => navigate('SettingsPaymentsSubscriptionScreen'))}>
                        <ListItemIcon>
                            <BookrIcon />
                        </ListItemIcon>
                        <ListItemText primary={t('subscription')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsPaymentsMessagesScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'sms'} />
                        </ListItemIcon>
                        <ListItemText primary={t('textMessages')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                    <ListItemButton onPress={handler(() => navigate('SettingsPaymentsBillingScreen'))}>
                        <ListItemIcon>
                            <MaterialIcons name={'insert-drive-file'} />
                        </ListItemIcon>
                        <ListItemText primary={t('billingInfo')} primaryTypographyProps={{ fontWeight: 500 }} />
                    </ListItemButton>
                </List>
            </Paper>
            <Paper
                bgColor={'backgroundPrimary'}
                fullWidth
                mb={2}
                p={2}
                justifyContent={'space-between'}
                alignItems={'flex-start'}
                flexDirection={'row'}
            >
                <HStack>
                    <MaterialIcons size={24} name={'account-balance-wallet'} />
                    <VStack flex ml={2}>
                        <HStack justifyContent={'space-between'} alignItems={'center'}>
                            <Typography variant={'subhead'} color={'primary'} lineHeight={22.5} fontWeight={500}>
                                {t('multipleSelectPayment')}
                            </Typography>
                            <Switch value={isMultiplePaymentsEnabled} onValueChange={handleMultiStripeEnabled} />
                        </HStack>
                        <Typography variant={'caption1'} color={'disabled'} lineHeight={15} fontWeight={500}>
                            {t('multipleSelectPaymentDescription')}
                        </Typography>
                    </VStack>
                </HStack>
                <Button
                    variant={'subtle'}
                    color={'accent'}
                    label={t('configureOnlinePayment')}
                    to={'/SettingsOnlinePaymentsSetupScreen'}
                    mt={3}
                    fullWidth
                />
            </Paper>
        </Screen>
    );
}
