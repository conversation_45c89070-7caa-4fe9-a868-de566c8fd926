import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { RootStackParamList } from '~/RoutesParams';
import { FreePlanCountAppointmentsPieChart } from '~/app/business/components/FreePlanCountAppointmentsPieChart';
import { ProfileAvatar } from '~/app/business/components/ProfileAvatar';
import { SettingsAboutUs } from '~/app/common/components/SettingsAboutUs';
import { SettingsActions } from '~/app/common/components/SettingsActions';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import BookrIcon from '~/components/icons/Bookr';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { useEvent } from '~/hooks/useEvent';
import { useHandler } from '~/hooks/useHandler';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshOnFocus } from '~/hooks/useRefreshOnFocus';
import { useUser } from '~/hooks/useUser';
import { Features } from '~/lib/Features';
import { isBusinessOwner } from '~/lib/utils/appointments';
import { useAuthStore } from '~/store/useAuthStore';
import { useBusinessStore } from '~/store/useBusinessStore';

export function SettingsScreen(): ReactElement {
    const user = useUser();
    const currentBusiness = useBusinessStore((state) => state.currentBusiness);
    const { t } = useTranslation();
    const handler = useHandler();
    const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
    const notifications = useNotifications();
    const [showCancelSubscriptionSheet, setShowCancelSubscriptionSheet] = useState(false);
    const subscription = useAuthStore((state) => state.subscription);
    const freePlanCountModal = useModal();

    const isFreePlan = !!user && subscription?.subscriptionPlan === 'FREE';
    const { data: appointmentCount, refetch } = useQuery({
        queryKey: ['myAppointmentsAsStaff'],
        enabled: isFreePlan,
        queryFn: usersEndpoint.countMyAppointmentsAsStaff,
        cacheTime: 0,
    });

    useRefreshOnFocus(() => {
        if (isFreePlan) {
            return refetch();
        }
    });

    const handleServicesNavigation = useEvent(() => {
        navigate('BusinessSettingsStack', { screen: 'SettingsServicesScreen' });
    });

    const cancelSubscriptionMutation = useMutation(
        'importClient',
        () => businessEndpoint.cancelSubscription(subscription?.stripeSubscriptionId ?? ''),
        {
            onError: () => {
                notifications.error(t('somethingWentWrong'));
            },
            onSuccess: () => navigate('ChooseSubscriptionPlanScreen', {}),
        },
    );

    const handleUpgradePlanPress = useEvent(async () => {
        setShowCancelSubscriptionSheet(true);
    });

    const handleSheetAction = useEvent(async () => {
        await cancelSubscriptionMutation.mutateAsync();
        setShowCancelSubscriptionSheet(false);
        navigate('ChooseSubscriptionPlanScreen', {});
    });

    const handleSheetClose = useEvent(() => {
        setShowCancelSubscriptionSheet(false);
    });

    return (
        <Screen bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
            <VStack bgColor={'backgroundSecondary'}>
                <ScreenHeader
                    bgColor={'transparent'}
                    headline={t('myProfile')}
                    caption={user?.displayName}
                    headlineActions={<ProfileAvatar />}
                />
                <HStack mb={1.5} mx={-0.5}>
                    {isBusinessOwner(user?.accountType) && !subscription?.commission && (
                        <Button
                            color={'accent'}
                            label={t('upgradePlan')}
                            startIcon={<BookrIcon size={18} />}
                            m={0.5}
                            onPress={handleUpgradePlanPress}
                        />
                    )}
                    <Button
                        variant={'subtle'}
                        color={'accent'}
                        label={t('switchToClient')}
                        to={'/ClientApplication/Home'}
                        m={0.5}
                        startIcon={<MaterialIcons name={'person'} />}
                    />
                </HStack>
            </VStack>
            <VStack flexGrow={0} fullWidth>
                {isBusinessOwner(user?.accountType) && (
                    <>
                        {appointmentCount !== undefined && appointmentCount !== null && (
                            <Paper label={t('limits')} bgColor={'backgroundPrimary'} fullWidth mb={2}>
                                <FreePlanCountAppointmentsPieChart
                                    modal={freePlanCountModal}
                                    count={appointmentCount}
                                    p={2}
                                />
                            </Paper>
                        )}
                        <Paper label={t('business')} bgColor={'backgroundPrimary'} fullWidth mb={2}>
                            <List>
                                <ListItemButton
                                    onPress={handler(() =>
                                        navigate('BusinessSettingsStack', { screen: 'SettingsProfileScreen' }),
                                    )}
                                >
                                    <ListItemIcon>
                                        <MaterialIcons name={'store'} />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={t('businessProfile')}
                                        primaryTypographyProps={{ fontWeight: 500 }}
                                    />
                                </ListItemButton>
                                <ListItemButton
                                    onPress={handler(
                                        () =>
                                            user?.business?.id &&
                                            navigate('BusinessProfileScreen', {
                                                businessId: user.business.id,
                                            }),
                                    )}
                                >
                                    <ListItemIcon>
                                        <MaterialIcons name={'visibility'} />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={t('publicProfileOfBusiness')}
                                        primaryTypographyProps={{ fontWeight: 500 }}
                                    />
                                </ListItemButton>
                                <ListItemButton
                                    onPress={handler(() =>
                                        navigate('BusinessSettingsStack', { screen: 'SettingsPaymentsScreen' }),
                                    )}
                                >
                                    <ListItemIcon>
                                        <MaterialIcons name={'credit-card'} />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={t('payments')}
                                        primaryTypographyProps={{ fontWeight: 500 }}
                                    />
                                </ListItemButton>
                                <ListItemButton
                                    onPress={handler(() =>
                                        navigate('BusinessSettingsStack', { screen: 'SettingsVirtual3DTourScreen' }),
                                    )}
                                >
                                    <ListItemIcon>
                                        <MaterialIcons name={'view-in-ar'} />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={t('virtualTour')}
                                        primaryTypographyProps={{ fontWeight: 500 }}
                                    />
                                </ListItemButton>

                                <ListItemButton
                                    onPress={handler(() =>
                                        navigate('BusinessSettingsStack', { screen: 'SettingsPromoLinkScreen' }),
                                    )}
                                >
                                    <ListItemIcon>
                                        <MaterialIcons name={'insert-link'} />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={t('promoLink')}
                                        primaryTypographyProps={{ fontWeight: 500 }}
                                    />
                                </ListItemButton>
                            </List>
                        </Paper>
                    </>
                )}

                <Paper label={t('employee')} bgColor={'backgroundPrimary'} fullWidth mb={2}>
                    <List>
                        <ListItemButton
                            onPress={handler(() =>
                                navigate('BusinessSettingsStack', { screen: 'SettingsMyInfoScreen' }),
                            )}
                        >
                            <ListItemIcon>
                                <MaterialIcons name={'person'} />
                            </ListItemIcon>
                            <ListItemText primary={t('yourInformation')} primaryTypographyProps={{ fontWeight: 500 }} />
                        </ListItemButton>
                        <ListItemButton
                            onPress={handler(() =>
                                navigate('BusinessSettingsStack', {
                                    screen: 'SettingsProfileWorkingProgramScreen',
                                    params: { isEmployee: true },
                                }),
                            )}
                        >
                            <ListItemIcon>
                                <MaterialIcons name={'schedule'} />
                            </ListItemIcon>
                            <ListItemText primary={t('workingHours')} primaryTypographyProps={{ fontWeight: 500 }} />
                        </ListItemButton>
                        <ListItemButton onPress={handler(handleServicesNavigation)}>
                            <ListItemIcon>
                                <MaterialIcons name={'event-seat'} />
                            </ListItemIcon>
                            <ListItemText primary={t('yourServices')} primaryTypographyProps={{ fontWeight: 500 }} />
                        </ListItemButton>
                        <ListItemButton
                            onPress={handler(() =>
                                navigate('BusinessSettingsStack', { screen: 'SettingsAvailabilityScreen' }),
                            )}
                        >
                            <ListItemIcon>
                                <MaterialIcons name={'hourglass-top'} />
                            </ListItemIcon>
                            <ListItemText primary={t('availability')} primaryTypographyProps={{ fontWeight: 500 }} />
                        </ListItemButton>
                        {(currentBusiness?.multipleStripeConnectedAccountsEnabled ||
                            user?.accountType === 'BUSINESS_OWNER') && (
                            <ListItemButton
                                onPress={handler(() =>
                                    navigate('BusinessSettingsStack', { screen: 'SettingsOnlinePaymentsSetupScreen' }),
                                )}
                            >
                                <ListItemIcon>
                                    <MaterialIcons name={'credit-card'} />
                                </ListItemIcon>
                                <ListItemText
                                    primary={t('onlinePayments')}
                                    primaryTypographyProps={{ fontWeight: 500 }}
                                />
                            </ListItemButton>
                        )}
                        {/* <ListItem */}
                        {/*    button */}
                        {/*    onPress={handler(() => */}
                        {/*        navigate('BusinessSettingsStack', { */}
                        {/*            screen: 'SettingsProfileGalleryScreen', */}
                        {/*            params: { isEmployee: true }, */}
                        {/*        }), */}
                        {/*    )} */}
                        {/* > */}
                        {/*    <ListItemIcon> */}
                        {/*        <MaterialIcons name={'collections'} /> */}
                        {/*    </ListItemIcon> */}
                        {/*    <ListItemText primary={t('gallery')} primaryTypographyProps={{ fontWeight: 500 }} /> */}
                        {/* </ListItem> */}
                    </List>
                </Paper>

                {Features.SettingsHelpSection && (
                    <Paper bgColor={'backgroundPrimary'} fullWidth mb={2}>
                        <List>
                            <ListItem>
                                <ListItemIcon>
                                    <MaterialIcons name={'help'} />
                                </ListItemIcon>
                                <ListItemText primary={t('help')} primaryTypographyProps={{ fontWeight: 500 }} />
                            </ListItem>
                        </List>
                    </Paper>
                )}

                <SettingsAboutUs />
                <SettingsActions />
            </VStack>
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                open={showCancelSubscriptionSheet}
                headline={t('cancelAllAppointmentsTitle')}
                subHeadline={t('cancelSubscriptionConfirmationCaption')}
                primaryText={t('yesCancel')}
                secondaryText={t('no')}
                onAction={handleSheetAction}
                onClose={handleSheetClose}
            />
        </Screen>
    );
}
