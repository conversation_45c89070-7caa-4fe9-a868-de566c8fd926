import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment';
import { Moment } from 'moment-timezone';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation, Modal, ScrollView } from 'react-native';
import { CalendarList, CalendarProps, DateData } from 'react-native-calendars';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { businessEndpoint } from '@bookr-technologies/api';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { TotalEarning } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { formatDate } from '@bookr-technologies/core';
import { PrivilegeBasedAccess } from '~/components/PrivilegeBasedAccess/PrivilegeBasedAccess';
import { useUserHasAccess } from '~/components/PrivilegeBasedAccess/useUserHasAccess';
import { Avatar } from '~/components/ui/Avatar';
import { Button } from '~/components/ui/Button';
import { Chip } from '~/components/ui/Chip';
import { Container, Grid, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { isEmployee } from '~/lib/utils/appointments';
import { AppointmentUserStatistics } from '../components/PerformanceScreen/AppointmentUserStatistics';
import { BookingChart } from '../components/PerformanceScreen/BookingChart';
import { EarningsChart } from '../components/PerformanceScreen/EarningsChart';
import { PeriodPickerButton } from '../components/PerformanceScreen/PeriodPickerButton';
import { TotalVisitsCard } from '../components/PerformanceScreen/TotalVisitsCard';

const previousMonthToday = moment(new Date()).subtract(1, 'M').format('YYYY-MM-DD');
const today = moment(new Date()).format('YYYY-MM-DD');

export function PerformanceScreen(): JSX.Element {
    const user = useUser();
    const accountTypeEmployee = useMemo(() => isEmployee(user?.accountType), [user]);
    const theme = useTheme();
    const { t } = useTranslation();
    const notifications = useNotifications();

    const [calendarOpen, setCalendarOpen] = useState(false);
    const [selectedMember, setSelectedMember] = useState<string | undefined>(
        accountTypeEmployee ? user?.uid : undefined,
    );
    const [startDate, setStartDate] = useState(previousMonthToday);
    const [endDate, setEndDate] = useState(today);
    const [markedDates, setMarkedDates] = useState<CalendarProps['markedDates']>({});
    const [startDayPicked, setStartDatePicked] = useState(false);
    const loadPerformanceEnabled = useUserHasAccess([UserPrivilegeType.Standard, UserPrivilegeType.Professional]);

    const { data: business } = useQuery(
        ['business', user?.business.id],
        async () => {
            if (user?.business.id) {
                return businessEndpoint.show(user?.business.id);
            }
        },
        {
            enabled: !!user?.business?.id,
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
        },
    );

    const staffMembers = useMemo(() => {
        if (!user) {
            return [];
        }
        let staff = business?.staffMembers ?? [];
        staff = [user, ...staff.filter((staffMember) => staffMember.uid !== user?.uid)];
        return staff;
    }, [business?.staffMembers, user]);

    useEffect(() => {
        updateMarkedDates(moment(endDate, 'YYYY-MM-DD').diff(moment(startDate, 'YYYY-MM-DD'), 'days'), startDate);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const selectedStaffMember = staffMembers.find((member) => member.uid === selectedMember);

    const updateMarkedDates = useEvent((range: number, firstDay: string): void => {
        const updatedMarkedDates: CalendarProps['markedDates'] = {};

        updatedMarkedDates[firstDay] = {
            startingDay: true,
            color: theme.palette.typography.textPrimary,
            textColor: theme.palette.backgroundPrimary.main,
        };
        for (let i = 1; i <= range; i++) {
            const date = formatDate(moment(firstDay).add(i, 'day'), 'YYYY-MM-DD');
            if (i < range) {
                updatedMarkedDates[date] = {
                    color: theme.palette.backgroundSecondary.main,
                    textColor: theme.palette.typography.disabled,
                };
            } else {
                updatedMarkedDates[date] = {
                    endingDay: true,
                    color: theme.palette.typography.textPrimary,
                    textColor: theme.palette.backgroundPrimary.main,
                };
            }
        }

        const days = Object.keys(updatedMarkedDates);
        if (firstDay === days[days.length - 1]) {
            updatedMarkedDates[firstDay] = {
                startingDay: true,
                endingDay: true,
                color: theme.palette.typography.textPrimary,
                textColor: theme.palette.backgroundPrimary.main,
            };
        }
        setMarkedDates(updatedMarkedDates);
        setStartDatePicked(false);
    });

    const handleDayPress = useEvent(({ dateString }: DateData) => {
        if (!startDayPicked) {
            const updatedMarkedDates: CalendarProps['markedDates'] = {};
            updatedMarkedDates[dateString] = {
                startingDay: true,
                color: theme.palette.typography.textPrimary,
                textColor: theme.palette.backgroundPrimary.main,
            };
            setMarkedDates(updatedMarkedDates);
            setStartDatePicked(true);
        } else {
            const startDay = Object.keys(markedDates ?? {})[0];
            const range = moment(dateString).diff(moment(startDay), 'days');
            if (range > 0) {
                updateMarkedDates(range, startDay);
            } else {
                updateMarkedDates(Math.abs(range), dateString);
            }
        }
    });

    const { data: sales } = useQuery(
        `sales/${moment(startDate).unix()}/${moment(endDate).unix()}`,
        async () => {
            return await businessEndpoint.getSales(moment(startDate), moment(endDate));
        },
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
            enabled: !!startDate && !!endDate && loadPerformanceEnabled,
        },
    );

    const selectedStaffDetails = useMemo(() => {
        return sales?.staffStats.find((staff) => staff.staff.uid === selectedStaffMember?.uid);
    }, [sales?.staffStats, selectedStaffMember?.uid]);

    const handleDonePress = useEvent(() => {
        const days = Object.keys(markedDates ?? {});
        setStartDate(days[0]);
        setEndDate(days[days.length - 1]);
        setCalendarOpen(false);
    });

    const handleCloseModal = useEvent(() => {
        setCalendarOpen(false);
    });

    const handleOpenModal = useEvent(() => {
        setCalendarOpen(true);
    });

    const handleStaffPress = useEvent((id): void => {
        LayoutAnimation.easeInEaseOut();
        if (selectedStaffMember?.uid === id || !id) {
            setSelectedMember(undefined);
        } else {
            setSelectedMember(id);
        }
    });

    const businessTotalEarnings: TotalEarning[] = [];
    sales?.staffStats.map((stat) => {
        stat.totalEarnings.forEach((totalEarning) => {
            businessTotalEarnings.push(totalEarning);
        });
    });

    const totalEarningsData = useMemo(
        () =>
            (
                sales?.staffStats.filter((it) => {
                    if (selectedStaffMember) {
                        return it.staff.uid === selectedStaffMember.uid;
                    } else {
                        return true;
                    }
                }) ?? []
            ).reduce(
                (prev, { totalEarnings }) =>
                    totalEarnings.reduce((accum, total) => {
                        const date = moment(total.date);
                        const key = date.format('YYYY-MM-DD');
                        if (!accum[key]) {
                            accum[key] = {
                                date,
                                totals: {},
                            };
                        }
                        if (!accum[key].totals[total.currency.toUpperCase()]) {
                            accum[key].totals[total.currency.toUpperCase()] = 0;
                        }
                        accum[key].totals[total.currency.toUpperCase()] += total.amount;
                        return accum;
                    }, prev),
                {} as Record<string, { date: Moment; totals: { [key: string]: number } }>,
            ),
        [sales?.staffStats, selectedStaffMember],
    );

    return (
        <Screen disablePadding bgColor={'backgroundTertiary'} disableScroll>
            <Grid bgColor={'backgroundPrimary'}>
                <ScreenHeader
                    px={3}
                    headline={t('performance')}
                    headlineTypographyProps={{
                        variant: 'title2',
                    }}
                />
                <Modal visible={calendarOpen} animationType={'slide'}>
                    <ScreenHeader
                        pl={3}
                        pt={3}
                        rightSlot={
                            <IconButton disablePadding pr={3.5} pt={3} onPress={handleCloseModal}>
                                <MaterialIcons name={'close'} />
                            </IconButton>
                        }
                    >
                        <Typography variant={'title1'} fontWeight={700} fontSize={22} pt={2.5}>
                            {t('chooseInterval')}
                        </Typography>
                    </ScreenHeader>
                    <VStack safeArea>
                        <CalendarList
                            pastScrollRange={10}
                            futureScrollRange={10}
                            staticHeader={true}
                            markingType={'period'}
                            markedDates={markedDates}
                            hideDayNames
                            firstDay={moment().localeData().firstDayOfWeek()}
                            onDayPress={handleDayPress}
                            theme={{
                                calendarBackground: theme.palette.backgroundPrimary.main,
                                dayTextColor: theme.palette.typography.textPrimary,
                                textDisabledColor: theme.palette.typography.disabled,
                                textDayFontFamily: theme.typography.fontFamilyWeightMap['500'] as string,
                                textDayFontSize: Number(theme.typography.variants.footnote.fontSize),
                                textMonthFontFamily: theme.typography.fontFamilyWeightMap['700'] as string,
                                monthTextColor: theme.palette.typography.textPrimary,
                            }}
                        />
                    </VStack>
                    {!startDayPicked && Object.keys(markedDates ?? {}).length > 0 && (
                        <Grid fullWidth bgColor={'backgroundPrimary'} pt={2} px={3} pb={5}>
                            <Button label={t('done')} size={'large'} color={'accent'} onPress={handleDonePress} />
                        </Grid>
                    )}
                </Modal>
                <Container mb={2}>
                    <PeriodPickerButton
                        disabled={!loadPerformanceEnabled}
                        onPress={handleOpenModal}
                        startDate={startDate}
                        endDate={endDate}
                    />
                </Container>
                {staffMembers && (
                    <HStack pb={0.5} fullWidth>
                        <ScrollView
                            horizontal={true}
                            bounces={false}
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={{ paddingHorizontal: 24 }}
                        >
                            {accountTypeEmployee && (
                                <Chip
                                    key={user?.uid}
                                    label={user?.displayName}
                                    value={user?.uid}
                                    checked={true}
                                    avatar={
                                        <Avatar
                                            bgColor={'backgroundSecondary'}
                                            source={user?.photoURL}
                                            name={user?.displayName}
                                        />
                                    }
                                />
                            )}
                            {!accountTypeEmployee &&
                                staffMembers.map((staffMember) => (
                                    <Chip
                                        key={staffMember.uid}
                                        label={staffMember.displayName}
                                        value={staffMember.uid}
                                        checked={selectedMember === staffMember.uid}
                                        onChecked={handleStaffPress}
                                        avatar={
                                            <Avatar
                                                bgColor={'backgroundSecondary'}
                                                source={staffMember.photoURL}
                                                name={staffMember.displayName}
                                            />
                                        }
                                    />
                                ))}
                        </ScrollView>
                    </HStack>
                )}
            </Grid>
            <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]} m={2}>
                <VStack flex>
                    <ScrollView bounces={false}>
                        <VStack pb={3}>
                            {(sales?.businessStats || selectedStaffDetails) && (
                                <EarningsChart
                                    staffStats={selectedStaffDetails}
                                    businessStats={sales?.businessStats}
                                    startDate={startDate}
                                    endDate={endDate}
                                    earnings={totalEarningsData}
                                />
                            )}
                            {!accountTypeEmployee && !selectedStaffDetails && (
                                <TotalVisitsCard
                                    value={sales?.businessStats?.visits}
                                    period={`${formatDate(startDate, 'll')} - ${formatDate(endDate, 'll')}`}
                                />
                            )}
                            <Grid py={3} ml={3}>
                                <AppointmentUserStatistics
                                    staffStats={selectedStaffDetails}
                                    businessStats={sales?.businessStats}
                                />
                            </Grid>
                            <BookingChart
                                bookingStats={selectedStaffDetails?.bookingStats ?? sales?.businessStats?.bookingStats}
                            />
                        </VStack>
                    </ScrollView>
                </VStack>
            </PrivilegeBasedAccess>
        </Screen>
    );
}
