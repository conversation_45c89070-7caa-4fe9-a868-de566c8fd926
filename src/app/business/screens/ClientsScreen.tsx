import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { AxiosError } from 'axios';
import { debounce } from 'lodash';
import { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList } from 'react-native';
import { useQuery } from 'react-query';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { ClientCard } from '~/app/business/components/ClientCard';
import { Loader } from '~/app/common/components/Loader';
import emptyClients from '~/assets/emptyBackgrounds/emptyClients.png';
import { EmptyStateCard } from '~/components/EmptyStateCard';
import { PrivilegeBasedAccess } from '~/components/PrivilegeBasedAccess/PrivilegeBasedAccess';
import { useUserHasAccess } from '~/components/PrivilegeBasedAccess/useUserHasAccess';
import { Button } from '~/components/ui/Button';
import { Grid } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useAuthStore } from '~/store/useAuthStore';

const useStyles = makeStyles<{ padding: number }, Styles<'listContent'>>(({ padding, theme }) => ({
    listContent: {
        width: '100%',
        paddingHorizontal: theme.mixins.spacingValue(padding),
    },
}));

const debouncedCall = debounce((func) => func(), 500);

interface SearchData {
    page: number;
    textSearch: string;
}
export function ClientsScreen(): ReactElement {
    const padding = useContainerPadding();
    const styles = useStyles({ padding });
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const notifications = useNotifications();
    const user = useAuthStore((state) => state.user);
    const businessId = user?.business.id;

    const [searchData, setSearchData] = useState<SearchData>({
        page: 0,
        textSearch: '',
    });
    const [clients, setClients] = useState<UserModel[]>([]);
    const loadClientsEnabled = useUserHasAccess([UserPrivilegeType.Standard, UserPrivilegeType.Professional]);

    const { isFetching, isError, data, refetch } = useQuery(
        `business:clients`,
        () =>
            businessClientsEndpoint.getClients({
                businessId,
                ...searchData,
                size: 15,
            }),
        {
            enabled: loadClientsEnabled,
            retry: (failureCount: number, error: any) => {
                const { response } = error as AxiosError;
                return ![401, 403].includes(response?.status || 0) && failureCount <= 3;
            },
            cacheTime: 0,
            onSuccess: (data) => {
                if (searchData.page > 0) {
                    setClients([...clients, ...(data?.content ?? [])]);
                } else {
                    setClients(data?.content ?? []);
                }
                setSearchData((prevState) => ({
                    ...prevState,
                    page: prevState.page + 1,
                }));
            },
            onError: () => {
                notifications.error(t('cannotLoadClients'));
            },
        },
    );

    const refreshControl = useRefreshControl(refetch);
    const handleOnImportPress = useEvent((): void => navigate('ImportClientScreen'));

    const handleChangeText = (text: string): void => {
        let { page, textSearch } = searchData;
        if (textSearch === '' || text === '' || textSearch !== text) {
            page = 0;
        }
        setSearchData({ page, textSearch: text });
        debouncedCall(refetch);
    };

    const updatePage = (): void => {
        if (data?.last) return;
        refetch();
    };

    return (
        <Screen disablePadding bgColor={'backgroundSecondary'} disableScroll>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                headline={t('clients')}
                px={padding}
                headlineActions={
                    <Button
                        disabled={!loadClientsEnabled}
                        color={'accent'}
                        size={'xsmall'}
                        label={t('addClient') as string}
                        startIcon={<MaterialIcons name={'add'} />}
                        pr={2}
                        onPress={handleOnImportPress}
                    />
                }
            >
                <TextField
                    disabled={!loadClientsEnabled}
                    label={t('searchClients') as string}
                    onChangeText={handleChangeText}
                    mt={2}
                    startAdornment={
                        <TextFieldAdornment variant={'start'}>
                            <Icon name={'search'} />
                        </TextFieldAdornment>
                    }
                />
            </ScreenHeader>

            <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]} m={2}>
                <Grid
                    bgColor={'backgroundSecondary'}
                    flex
                    height={'100%'}
                    scrollable={!isFetching && !isError && clients && clients.length === 0}
                    scrollViewProps={{ refreshControl }}
                >
                    {isFetching ? <Loader p={3} /> : null}
                    {!isFetching && !isError && clients && clients.length === 0 && (
                        <Grid p={padding}>
                            <EmptyStateCard
                                title={t('addClientsTitle')}
                                subtitle={t('addClientsSubtitle')}
                                actionLabel={t('addClients')}
                                onPress={handleOnImportPress}
                                source={emptyClients}
                            />
                        </Grid>
                    )}
                    {!isError && clients && clients.length > 0 && (
                        <FlatList
                            refreshControl={refreshControl}
                            contentContainerStyle={styles.listContent}
                            data={clients}
                            initialNumToRender={10}
                            maxToRenderPerBatch={10}
                            renderItem={({ item }): ReactElement => (
                                <ClientCard
                                    key={item.uid}
                                    client={{
                                        ...item,
                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                        // @ts-ignore
                                        photoURL: clients.length < 10 ? item.photoURL : null, // performance optimization
                                    }}
                                    my={1.5}
                                />
                            )}
                            keyExtractor={(item): string => item.uid}
                            onEndReachedThreshold={0.95}
                            onEndReached={updatePage}
                        />
                    )}
                </Grid>
            </PrivilegeBasedAccess>
        </Screen>
    );
}
