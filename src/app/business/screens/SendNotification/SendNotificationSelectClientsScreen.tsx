import { useNavigation } from '@react-navigation/native';
import { AxiosError } from 'axios';
import { debounce } from 'lodash';
import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, LayoutAnimation, Pressable } from 'react-native';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { BusinessApplicationNavigationProp } from '~/RoutesParams';
import { Loader } from '~/app/common/components/Loader';
import { PrivilegeBasedAccess } from '~/components/PrivilegeBasedAccess/PrivilegeBasedAccess';
import { useUserHasAccess } from '~/components/PrivilegeBasedAccess/useUserHasAccess';
import { Button } from '~/components/ui/Button';
import { Checkbox } from '~/components/ui/Checkbox';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useAuthStore } from '~/store/useAuthStore';

const useStyles = makeStyles<{ padding: number }, Styles<'listContent'>>(({ padding, theme }) => ({
    listContent: {
        width: '100%',
        paddingHorizontal: theme.mixins.spacingValue(padding),
        paddingBottom: 80,
        backgroundColor: theme.palette.backgroundPrimary.main,
    },
}));

const debouncedCall = debounce((func) => func(), 500);

interface SearchData {
    page: number;
    textSearch: string;
}

export function SendNotificationSelectClientsScreen(): ReactElement {
    const padding = useContainerPadding();
    const styles = useStyles({ padding });
    const { t } = useTranslation();
    const { navigate } = useNavigation<BusinessApplicationNavigationProp<'SendNotificationSelectClientsScreen'>>();
    const notifications = useNotifications();
    const theme = useTheme();
    const user = useAuthStore((state) => state.user);
    const businessId = user?.business.id;
    const [userIds, setUserIds] = useState<string[]>([]);
    const [excludedUserIds, setExcludedUserIds] = useState<string[]>([]);
    const [allSelected, setAllSelected] = useState(false);
    const insets = useSafeAreaInsets();
    const [totalClients, setTotalClients] = useState(-1);

    const [searchData, setSearchData] = useState<SearchData>({
        page: 0,
        textSearch: '',
    });
    const [clients, setClients] = useState<UserModel[]>([]);
    const loadClientsEnabled = useUserHasAccess([UserPrivilegeType.Standard, UserPrivilegeType.Professional]);

    const { isFetching, isError, data, refetch } = useQuery(
        `business:clients`,
        () =>
            businessClientsEndpoint.getClients({
                businessId,
                ...searchData,
                size: 10,
                withPushNotificationsEnabled: true,
            }),
        {
            enabled: loadClientsEnabled,
            retry: (failureCount: number, error: any) => {
                const { response } = error as AxiosError;
                return ![401, 403].includes(response?.status || 0) && failureCount <= 3;
            },
            cacheTime: 0,
            onSuccess: (data) => {
                LayoutAnimation.easeInEaseOut();
                if (totalClients === -1) {
                    setTotalClients(data?.totalElements ?? 0);
                }
                if (searchData.page > 0) {
                    setClients([...clients, ...(data?.content ?? [])]);
                } else {
                    setClients(data?.content ?? []);
                }
                setSearchData((prevState) => ({
                    ...prevState,
                    page: prevState.page + 1,
                }));
            },
            onError: () => {
                notifications.error(t('cannotLoadClients'));
            },
        },
    );

    const refreshControl = useRefreshControl(refetch);

    const handleChangeText = (text: string): void => {
        let { page, textSearch } = searchData;
        if (textSearch === '' || text === '' || textSearch !== text) {
            page = 0;
        }
        setSearchData({ page, textSearch: text });
        debouncedCall(refetch);
    };

    const updatePage = (): void => {
        if (data?.last) return;
        refetch();
    };

    const handleOnNextStep = () => {
        navigate('SendNotificationComposeScreen', { userIds, excludedUserIds, allSelected, totalClients });
    };

    const handleClientPress = (userId: string): void => {
        if (allSelected) {
            setExcludedUserIds((prevState) => {
                if (prevState.includes(userId)) {
                    return prevState.filter((id) => id !== userId);
                }
                return [...prevState, userId];
            });
        } else {
            setUserIds((prevState) => {
                if (prevState.includes(userId)) {
                    return prevState.filter((id) => id !== userId);
                }
                return [...prevState, userId];
            });
        }
    };

    const handleOnAllSelected = (newValue: boolean) => {
        if (!newValue) {
            setExcludedUserIds([]);
            setUserIds([]);
            setAllSelected(newValue);
        } else {
            setAllSelected(newValue);
        }
    };

    return (
        <Screen disablePadding bgColor={'backgroundPrimary'} disableScroll>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                headline={t('selectClients')}
                px={padding}
                leftSlot={<ScreenHeaderBackButton />}
                caption={t('selectClientsToSendNotification')}
                style={{
                    borderBottomColor: theme.palette.backgroundTertiary.main,
                    borderBottomWidth: 1,
                    borderStyle: 'solid',
                }}
            >
                <TextField
                    disabled={!loadClientsEnabled}
                    label={t('searchClients') as string}
                    onChangeText={handleChangeText}
                    mt={2}
                    startAdornment={
                        <TextFieldAdornment variant={'start'}>
                            <Icon name={'search'} />
                        </TextFieldAdornment>
                    }
                />
            </ScreenHeader>

            <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]} m={2}>
                <>
                    <Grid
                        bgColor={'backgroundPrimary'}
                        flex
                        height={'100%'}
                        scrollable={!isFetching && !isError && clients && clients.length === 0}
                        pb={insets.bottom / 4}
                        scrollViewProps={{ refreshControl }}
                    >
                        {isFetching ? <Loader p={3} /> : null}
                        {!isError && clients && clients.length > 0 && (
                            <FlatList
                                contentContainerStyle={styles.listContent}
                                data={clients}
                                initialNumToRender={10}
                                maxToRenderPerBatch={10}
                                renderItem={({ item }): ReactElement => {
                                    const isSelected =
                                        !excludedUserIds.includes(item.uid) &&
                                        (allSelected || userIds.includes(item.uid));
                                    const handlePress = () => handleClientPress(item.uid);
                                    return (
                                        <Pressable onPress={handlePress}>
                                            <HStack
                                                py={1}
                                                fullWidth
                                                alignItems={'center'}
                                                justifyContent={'space-between'}
                                                borderRadius={12}
                                            >
                                                <VStack>
                                                    <Typography variant={'callout'} fontWeight={500}>
                                                        {item.displayName}
                                                    </Typography>
                                                    <Typography
                                                        variant={'caption1'}
                                                        color={'textSecondary'}
                                                        fontWeight={500}
                                                    >
                                                        {item.phoneNumber}
                                                    </Typography>
                                                </VStack>
                                                <Checkbox checked={isSelected} onPress={handlePress} noFeedback />
                                            </HStack>
                                        </Pressable>
                                    );
                                }}
                                keyExtractor={(item): string => item.uid}
                                onEndReachedThreshold={0.95}
                                onEndReached={updatePage}
                            />
                        )}
                    </Grid>
                    <Grid
                        bgColor={'backgroundPrimary'}
                        position={'absolute'}
                        bottom={0}
                        width={'100%'}
                        pb={insets.bottom / 8}
                        px={2}
                        pt={1}
                        justifyContent={'center'}
                        style={{
                            borderTopColor: theme.palette.backgroundTertiary.main,
                            borderTopWidth: 1,
                            borderStyle: 'solid',
                        }}
                    >
                        <HStack alignItems={'center'} justifyContent={'space-between'} pt={1}>
                            <Checkbox
                                disablePadding
                                label={t('selectAllClients')}
                                labelProps={{
                                    style: {
                                        marginLeft: 16,
                                    },
                                }}
                                checked={excludedUserIds.length ? false : allSelected}
                                onPress={() => handleOnAllSelected(!allSelected)}
                            />
                            {totalClients !== -1 && (
                                <Typography variant={'body'} color={'contentTertiary'} fontWeight={500}>
                                    {t('xOutOfY', {
                                        x: allSelected
                                            ? excludedUserIds.length
                                                ? totalClients - excludedUserIds.length
                                                : totalClients
                                            : userIds.length,
                                        y: totalClients,
                                    })}
                                </Typography>
                            )}
                        </HStack>
                        <Button
                            mt={2}
                            size={'large'}
                            color={'accent'}
                            label={t('nextStep')}
                            onPress={handleOnNextStep}
                            disabled={!userIds.length && !allSelected}
                        />
                    </Grid>
                </>
            </PrivilegeBasedAccess>
        </Screen>
    );
}
