import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Keyboard } from 'react-native';
import { BusinessApplicationNavigationProp, BusinessApplicationParamList } from '~/RoutesParams';
import { Button } from '~/components/ui/Button';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { TextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useContainerPadding } from '~/hooks/useContainerPadding';

export function SendNotificationComposeScreen(): ReactElement {
    const padding = useContainerPadding();
    const { t } = useTranslation();
    const { navigate } = useNavigation<BusinessApplicationNavigationProp<'SendNotificationComposeScreen'>>();
    const route = useRoute<RouteProp<BusinessApplicationParamList, 'SendNotificationComposeScreen'>>();
    const [message, setMessage] = useState('');
    const { userIds, excludedUserIds, totalClients, allSelected } = route.params;

    const handleOnNextStep = () => {
        navigate('SendNotificationConfirmationScreen', {
            allSelected,
            excludedUserIds,
            message,
            totalClients,
            userIds: userIds || [],
        });
    };

    const handleChangeText = (text: string) => {
        if (text.length) {
            const checkLineBreak = /\r|\n/.exec(text[text.length - 1]);
            if (checkLineBreak) {
                Keyboard.dismiss();
            } else {
                setMessage(text);
            }
        }
    };

    return (
        <Screen disablePadding bgColor={'backgroundPrimary'} disableScroll>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                headline={t('composeNotification')}
                px={padding}
                leftSlot={<ScreenHeaderBackButton />}
                caption={t('composeYourNotification')}
            />
            <Grid px={2} safeBottom flex mt={2}>
                <VStack justifyContent={'space-between'} flex>
                    <VStack flex={1} py={2}>
                        <HStack alignItems={'center'}>
                            <Typography variant={'callout'} fontWeight={500}>
                                {t('notificationMessage')}:
                            </Typography>
                            <Typography ml={1} variant={'callout'} fontWeight={500} color={'textSecondary'}>
                                {t('maxCharacters', { max: 100 })}
                            </Typography>
                        </HStack>

                        <TextField
                            textAlignVertical={'top'}
                            mt={1}
                            multiline
                            value={message.replace(/(\r\n|\n|\r)/gm, '')}
                            numberOfLines={1}
                            label={t('typeYourMessage')}
                            maxLength={100}
                            minHeight={157}
                            textBreakStrategy={'highQuality'}
                            lineBreakStrategyIOS={'push-out'}
                            onChangeText={handleChangeText}
                            onKeyPress={(e) => {
                                if (e.nativeEvent.key === 'Enter') {
                                    Keyboard.dismiss();
                                    setMessage((prev) => prev.replace(/(\r\n|\n|\r)/gm, ''));
                                }
                            }}
                        />
                    </VStack>

                    <Button
                        size={'large'}
                        color={'accent'}
                        label={t('nextStep')}
                        onPress={handleOnNextStep}
                        disabled={!message || message.replace(/(\r\n|\n|\r)/gm, '').length === 0}
                    />
                </VStack>
            </Grid>
        </Screen>
    );
}
