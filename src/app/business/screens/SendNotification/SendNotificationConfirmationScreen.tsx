import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import React, { ReactElement } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { businessNotificationsEndpoint } from '@bookr-technologies/api/endpoints/businessNotificationsEndpoint';
import { BusinessApplicationParamList } from '~/RoutesParams';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { Button } from '~/components/ui/Button';
import { Divider } from '~/components/ui/Divider';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useLocalPushNotificationScheduler } from '~/hooks/useLocalPushNotificationScheduler';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useBusinessStore } from '~/store/useBusinessStore';

const ListItem = ({
    icon,
    title,
    subtitle,
    onEditPress,
}: {
    icon: ReactElement;
    onEditPress: () => void;
    subtitle: string;
    title: string;
}) => {
    const { t } = useTranslation();
    return (
        <HStack justifyContent={'space-between'} py={2}>
            <HStack flex={1} alignItems={'flex-start'} justifyContent={'flex-start'}>
                <VStack>
                    <HStack alignItems={'center'}>
                        {icon}
                        <Typography ml={1} variant={'footnote'} fontWeight={500} numberOfLines={5}>
                            {title}
                        </Typography>
                    </HStack>
                    <Typography
                        ml={3.5}
                        variant={'footnote'}
                        fontWeight={500}
                        color={'textSecondary'}
                        numberOfLines={5}
                    >
                        {subtitle}
                    </Typography>
                </VStack>
            </HStack>
            <TouchableOpacity onPress={onEditPress}>
                <Typography variant={'footnote'} fontWeight={500} color={'accent'}>
                    {t('edit')}
                </Typography>
            </TouchableOpacity>
        </HStack>
    );
};

export function SendNotificationConfirmationScreen(): ReactElement {
    const padding = useContainerPadding();
    const { t } = useTranslation();
    const { goBack, canGoBack } = useNavigation();
    const theme = useTheme();
    const route = useRoute<RouteProp<BusinessApplicationParamList, 'SendNotificationConfirmationScreen'>>();
    const { userIds = [], excludedUserIds = [], totalClients = 0, allSelected = false, message = '' } = route.params;
    const business = useBusinessStore((state) => state.currentBusiness);
    const notifications = useNotifications();
    const modal = useModal();
    const { scheduleLocalPushNotification } = useLocalPushNotificationScheduler();
    const { data } = useQuery({
        queryKey: ['notifications', 'count'],
        cacheTime: 0,
        queryFn: async () => {
            return await businessNotificationsEndpoint.countSentNotificationsForToday();
        },
        onError: (error) => {
            console.log('Error while fetching count of notifications', error);
        },
    });

    const { isLoading: isPreviewLoading, mutateAsync: previewNotification } = useMutation({
        mutationKey: ['previewNotification'],
        retry: 3,
        mutationFn: async () => {
            await scheduleLocalPushNotification(
                {
                    title: `New message from ${business?.name}`,
                    body: message,
                    data: {},
                },
                null,
            );
        },
        onError: () => {
            notifications.error(t('errorPreviewingNotification'));
        },
        onSuccess: () => {
            notifications.success(t('notificationSent'));
        },
    });

    const { isLoading: isSendingLoading, mutateAsync: sendNotification } = useMutation({
        mutationKey: ['sendNotification'],
        retry: 3,
        mutationFn: async () => {
            console.log(
                `Send notification ${message} to clients ${userIds}, excluded ${excludedUserIds}, all ${allSelected}`,
            );
            await businessNotificationsEndpoint.sendNotificationToClients({
                message,
                selectedUserIds: userIds,
                excludedUsersIds: excludedUserIds,
                allSelected,
            });
        },
        onError: () => {
            notifications.error(t('errorSendingNotification'));
        },
        onSuccess: () => {
            modal.open();
        },
    });

    const handleSendPush = () => {
        sendNotification();
    };

    const handlePreview = () => {
        previewNotification();
    };

    const exitScreen = () => {
        if (canGoBack()) {
            goBack();
        }
        if (canGoBack()) {
            goBack();
        }
    };

    return (
        <Screen disablePadding bgColor={'backgroundPrimary'} disableScroll>
            <ScreenHeader
                bgColor={'backgroundPrimary'}
                headline={t('confirmNotification')}
                px={padding}
                leftSlot={<ScreenHeaderBackButton />}
                caption={t('confirmNotificationCaption')}
            />
            <Grid px={2} safeBottom flex mt={2}>
                <VStack justifyContent={'space-between'} flex>
                    <VStack flex={1} py={2}>
                        <ListItem
                            icon={
                                <MaterialCommunityIcons
                                    name={'account-multiple-outline'}
                                    size={20}
                                    color={theme.palette.primary.main}
                                />
                            }
                            onEditPress={exitScreen}
                            subtitle={t('numberOfClients', {
                                count: allSelected
                                    ? excludedUserIds?.length
                                        ? (totalClients || 0) - excludedUserIds.length
                                        : totalClients
                                    : userIds.length,
                            })}
                            title={t('clients')}
                        />
                        <Divider />
                        <ListItem
                            icon={
                                <MaterialCommunityIcons
                                    name={'send-outline'}
                                    size={20}
                                    color={theme.palette.primary.main}
                                />
                            }
                            onEditPress={() => canGoBack() && goBack()}
                            subtitle={message}
                            title={t('notificationMessage')}
                        />
                        <Divider size={8} />
                        <Typography mt={3} mb={1} variant={'title3'} fontWeight={700}>
                            {t('yourBusinessLimit')}
                        </Typography>
                        <Typography mb={4} variant={'footnote'} color={'textSecondary'}>
                            {t('yourBusinessLimitCaption')}
                        </Typography>

                        <VStack>
                            <HStack alignItems={'center'}>
                                <MaterialCommunityIcons name="timer-sand-empty" size={20} />
                                <Typography ml={1} variant={'footnote'} fontWeight={500}>
                                    {t('yourBusinessLimit')}:
                                </Typography>
                            </HStack>
                            <Trans
                                i18nKey={'yourNotificationsUsage'}
                                values={{ used: data || 0, total: totalClients || 0 }}
                                components={{
                                    Text: (
                                        <Typography
                                            ml={3.5}
                                            variant={'footnote'}
                                            fontWeight={500}
                                            color={'textSecondary'}
                                        />
                                    ),
                                    Bold: <Typography variant={'footnote'} fontWeight={500} color={'primary'} />,
                                }}
                            />
                            <Divider mt={2} />
                        </VStack>
                    </VStack>

                    <Button
                        variant={'subtle'}
                        size={'large'}
                        color={'accent'}
                        label={t('previewNotification')}
                        loading={isPreviewLoading}
                        onPress={handlePreview}
                        mb={1}
                    />
                    <Button
                        loading={isSendingLoading}
                        size={'large'}
                        color={'accent'}
                        label={t('sendNotification')}
                        onPress={handleSendPush}
                        disabled={(data || 0) >= (totalClients || 0)}
                    />
                </VStack>
            </Grid>
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Success}
                icon={<MaterialCommunityIcons name={'check'} size={48} color={theme.palette.accent.main} />}
                open={modal.isOpen}
                headline={t('notificationSent')}
                primaryColor={'accent'}
                primaryText={t('continue')}
                enablePanDownToClose={false}
                enableDismissOnClose={false}
                onAction={exitScreen}
                onClose={exitScreen}
            />
        </Screen>
    );
}
