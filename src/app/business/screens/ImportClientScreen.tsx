import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import { LayoutAnimation, View } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useMutation } from 'react-query';
import { useTheme } from 'styled-components/native';
import * as Yup from 'yup';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { PrivilegeBasedAccess } from '~/components/PrivilegeBasedAccess/PrivilegeBasedAccess';
import Button, { FormikButton } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { ScreenHeaderBackButton } from '~/components/ui/Screen/ScreenHeader';
import { Spacer } from '~/components/ui/Spacer';
import { FormikTextField } from '~/components/ui/TextField';
import { FormikPhoneField } from '~/components/ui/TextField/FormikPhoneField';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useI18n } from '~/hooks/useTranslation';
import { useValidationSchema } from '~/hooks/useValidationSchema';
import { ContactsListPage } from './ContactsListPage';

interface ImportClientForm {
    displayName: string;
    phoneNumber: string;
}

const IMPORT_OPTIONS = [
    {
        id: 'phoneNumber',
        label: 'withPhoneNumber',
    },
    {
        id: 'contacts',
        label: 'fromContacts',
    },
];

export function ImportClientScreen(): ReactElement {
    const t = useI18n();
    const { goBack } = useNavigation();
    const notifications = useNotifications();
    const [activeImportOption, setActiveImportOption] = useState(IMPORT_OPTIONS[0]);
    const pagerRef = useRef<PagerView>(null);
    const styles = useStyles();
    const theme = useTheme();

    const importClientMutation = useMutation(
        'importClient',
        (values: ImportClientForm) =>
            businessClientsEndpoint.importBulk([
                {
                    displayName: values.displayName,
                    phoneNumber: values.phoneNumber,
                },
            ]),
        {
            onError: () => {
                goBack();
                setTimeout(() => {
                    notifications.error(t('clientImportFailed'));
                }, 500);
            },
            onSuccess: () => {
                goBack();
                setTimeout(() => {
                    notifications.info(t('clientImportStarted'));
                }, 500);
            },
        },
    );

    useEffect(() => {
        LayoutAnimation.easeInEaseOut();
    }, []);

    const validation = useValidationSchema({
        displayName: Yup.string().required(t('requiredField')),
        phoneNumber: Yup.string().required(t('requiredField')),
    });

    const handleSubmit = useCallback(
        async (values: ImportClientForm) => {
            importClientMutation.mutate(values);
        },
        [importClientMutation],
    );

    return (
        <Formik
            validateOnMount
            enableReinitialize
            initialValues={{
                displayName: '',
                phoneNumber: '',
            }}
            onSubmit={handleSubmit}
            {...validation}
        >
            <Screen disableScroll>
                <ScreenHeader
                    disableSafeViewArea
                    pt={2}
                    headline={t('addClients')}
                    leftSlot={<ScreenHeaderBackButton />}
                />
                <HStack bgColor={'backgroundTertiary'} p={0.25} borderRadius={12} fullWidth mb={4}>
                    {IMPORT_OPTIONS.map(({ id, label }, index) => {
                        const isSelected = activeImportOption.id === IMPORT_OPTIONS[index].id;
                        const handleButtonPress = useEvent(() => {
                            pagerRef.current?.setPage(index);
                            setActiveImportOption(IMPORT_OPTIONS[index]);
                        });
                        return (
                            <Button
                                key={id}
                                color={isSelected ? 'backgroundPrimary' : 'backgroundTertiary'}
                                size={'xsmall'}
                                borderRadius={12}
                                textColor={
                                    (isSelected
                                        ? theme.palette.typography.textPrimary
                                        : theme.palette.typography.textSecondary) as ThemeColorKeys
                                }
                                variant={'contained'}
                                label={t(`${label}`)}
                                style={{ width: '50%' }}
                                onPress={handleButtonPress}
                            />
                        );
                    })}
                </HStack>
                <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]}>
                    <PagerView
                        style={styles.pagerView}
                        initialPage={0}
                        ref={pagerRef}
                        onPageSelected={(event): void =>
                            setActiveImportOption(IMPORT_OPTIONS[event.nativeEvent.position])
                        }
                    >
                        <View>
                            <VStack
                                safeBottom={2}
                                flex
                                scrollable
                                scrollViewProps={{ contentContainerStyle: { flex: 1 }, scrollEnabled: false }}
                            >
                                <FormikTextField name={'displayName'} label={t('clientName')} mb={2} />
                                <FormikPhoneField name={'phoneNumber'} />

                                <Spacer />

                                <FormikButton
                                    loading={importClientMutation.isLoading}
                                    color={'accent'}
                                    size={'large'}
                                    label={t('addClient')}
                                />
                            </VStack>
                        </View>
                        <View>
                            <ContactsListPage />
                        </View>
                    </PagerView>
                </PrivilegeBasedAccess>
            </Screen>
        </Formik>
    );
}

const useStyles = makeStyles(() => ({
    pagerView: {
        flex: 1,
    },
}));
