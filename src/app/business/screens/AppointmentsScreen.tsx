import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import moment from 'moment';
import { transparentize } from 'polished';
import { ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation, ScrollView } from 'react-native';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { breaksEndpoint } from '@bookr-technologies/api/endpoints/breaksEndpoint';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { Calendar, CalendarEventType } from '@bookr-technologies/react-native-calendar';
import { BusinessNotVisibleBanner } from '~/app/business/components/BusinessNotVisibleBanner';
import { FreePlanCountAppointmentsPieChart } from '~/app/business/components/FreePlanCountAppointmentsPieChart';
import { WaitingList } from '~/app/business/components/WaitingList';
import { BottomSheet } from '~/components/BottomSheet';
import { CalendarGridEventTypeEnum } from '~/components/CalendarGrid/CalendarGridEventType';
import { DatePicker } from '~/components/DatePicker';
import { Avatar } from '~/components/ui/Avatar';
import { Button } from '~/components/ui/Button';
import { Chip } from '~/components/ui/Chip';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { Container, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { getNearestDatetime, timeFromUtcToLocal } from '~/components/utils/time';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';
import { useAuthStore } from '~/store/useAuthStore';

// TODO fix this hack when the implementation will be available on the backend
const HIDE_CALENDAR_FOR_BUSINESSES = [
    '5694e78d-4725-4914-b3f1-5b5ef46b28a2', // Clinica Optimum
];

interface AppointmentsScreenProps {
    actionAfterLoad?: () => void;
}

export function AppointmentsScreen(): ReactElement {
    const { t } = useTranslation();
    const user = useUser();
    const route = useRoute();
    const theme = useTheme();
    const logger = useLogger('AppointmentsScreen');
    const { navigate } = useNavigation();
    const [selectedMember, setSelectedMember] = useState<string | null>(null);
    const [selectedDay, setSelectedDay] = useState<string>(() => new Date().toISOString());
    const [selectedEvent, setSelectedEvent] = useState<CalendarEventType | null>(null);
    const notifications = useNotifications();
    const subscription = useAuthStore((state) => state.subscription);
    const freePlanCountModal = useModal();

    const actionAfterLoad = (route.params as AppointmentsScreenProps)?.actionAfterLoad;

    const { data: days } = useQuery(`business/staff/${selectedMember}/days`, async () => {
        if (!selectedMember) {
            return [];
        }

        return appointmentsEndpoint.getDatesOfAppointments(selectedMember);
    });

    const isFreePlan = !!user && subscription?.subscriptionPlan === 'FREE';
    const { data: appointmentCount, refetch } = useQuery({
        queryKey: ['myAppointmentsAsStaff'],
        enabled: isFreePlan,
        queryFn: usersEndpoint.countMyAppointmentsAsStaff,
        cacheTime: 0,
    });

    const {
        data: businessData,
        isLoading: isBusinessLoading,
        refetch: refetchBusiness,
    } = useQuery(`business/${user?.business?.id}/${selectedDay}`, () => {
        if (!user?.business?.id) {
            return null;
        }

        const params = {
            includeAppointments: true,
            timestampFrom: moment(selectedDay).startOf('day').unix(),
            timestampTo: moment(selectedDay).endOf('day').unix(),
        };

        return businessEndpoint.show(user.business.id, { params });
    });

    const staffMembers = useMemo(() => {
        if (!user) {
            return [];
        }
        let staff = businessData?.staffMembers ?? [];
        staff = [user, ...staff.filter((staffMember) => staffMember.uid !== user?.uid)];
        setSelectedMember((prev) => prev || staff[0]?.uid);
        return staff;
    }, [businessData?.staffMembers, user]);

    const { appointments, breaks } = useMemo((): { appointments: AppointmentModel[]; breaks: BreakModel[] } => {
        const { staffMembers } = businessData || {};
        if (!staffMembers) {
            return {
                appointments: [],
                breaks: [],
            };
        }

        const firstStaffMember = staffMembers[0];
        const selectedStaffMember = staffMembers.find((member) => member.uid === selectedMember);
        const appointments = selectedStaffMember?.appointmentsAsStaff || firstStaffMember?.appointmentsAsStaff || [];
        const breaks = selectedStaffMember?.breaks || firstStaffMember?.breaks || [];

        return { appointments, breaks };
    }, [businessData, selectedMember]);

    const activeIntervals = useMemo(() => {
        const staffMember = staffMembers.find((staff) => staff.uid === selectedMember);

        return (staffMember?.workingHours || [])
            .filter((interval) => {
                const today = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][
                    moment(selectedDay).weekday()
                ];

                return interval.day.toLowerCase() === today;
            })
            .map(({ start, end, lastUpdatedAt }) => ({
                start: timeFromUtcToLocal(String(start), lastUpdatedAt),
                end: timeFromUtcToLocal(String(end), lastUpdatedAt),
            }));
    }, [staffMembers, selectedMember, selectedDay]);

    const businessEvents = useMemo(
        () =>
            ([] as CalendarEventType[])
                .concat(
                    appointments.map((item) => ({
                        color: item.service.color,
                        summary: item.service.name,
                        end: moment(item.dateTime).add(item.service.duration, 'minutes').toISOString(),
                        title: item.client.displayName,
                        id: item.id,
                        start: moment(item.dateTime).toISOString(),
                        type: CalendarGridEventTypeEnum.Appointment,
                    })),
                    breaks.map((item) => ({
                        color: theme.palette.accent.main,
                        end: moment(item.toDateTime).toISOString(),
                        title: item.title || t('break'),
                        summary: '',
                        id: item.id,
                        start: moment(item.fromDateTime).toISOString(),
                        type: CalendarGridEventTypeEnum.Break,
                    })),
                )
                .filter((item) => {
                    const currentDay = moment(selectedDay);
                    const itemStart = moment(item.start);

                    return (
                        itemStart.isSameOrAfter(currentDay.startOf('day')) &&
                        itemStart.isSameOrBefore(currentDay.endOf('day'))
                    );
                }),
        [appointments, breaks, selectedDay, t, theme.palette.accent.main],
    );

    const handleCreateEvent = useCallback(
        (time: string) => {
            if (!!appointmentCount && appointmentCount >= 100) {
                freePlanCountModal.open();
                return;
            }
            navigate('SelectEventTypeScreen', {
                date: moment(`${moment(selectedDay).format('YYYY-MM-DD')} ${time}`, 'YYYY-MM-DD HH:mm').toISOString(),
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                staffMemberId: selectedMember!,
            });
        },
        [appointmentCount, freePlanCountModal, navigate, selectedDay, selectedMember],
    );

    const handlePressEvent = useEvent((event: CalendarEventType) => {
        if (event.type === CalendarGridEventTypeEnum.Appointment) {
            navigate('AppointmentDetailsScreen', {
                appointmentId: event.id,
                businessView: true,
            });
        } else if (event.type === CalendarGridEventTypeEnum.Break) {
            setSelectedEvent(event);
        }
    });

    // const handleAddNewEvent = useCallback(() => handleCreateEvent(getNearestTime()), [handleCreateEvent]);

    const handleBreakButton = useEvent(() => {
        if (selectedMember) {
            logger.log('create break', { selectedDay, selectedMember });

            navigate('CreateBreakScreen', {
                date: getNearestDatetime(
                    `${moment(selectedDay).format('YYYY-MM-DD')} ${moment().format('HH:mm')}`,
                    'YYYY-MM-DD HH:mm',
                ).toISOString(),
                staffMemberId: selectedMember,
            });
        }
    });

    const handleSeeAllBreaks = () => {
        setSelectedEvent(null);
        if (!selectedMember) {
            return;
        }
        navigate('CreateBreakScreen', {
            staffMemberId: selectedMember,
        });
    };

    const handleDeleteBreak = async () => {
        if (!selectedEvent) {
            return;
        }
        try {
            await breaksEndpoint.destroy(selectedEvent.id);
            refetchBusiness();
            LayoutAnimation.easeInEaseOut();
            setSelectedEvent(null);
        } catch (e) {
            notifications.error(t('somethingWentWrongError', { error: 'breakCouldNotBeDeleted' }));
        }
    };

    useEffect(() => actionAfterLoad?.(), [actionAfterLoad]);

    useFocusEffect(
        useEvent(() => {
            refetchBusiness();
            if (isFreePlan) {
                refetch();
            }
        }),
    );

    const shouldNotDisplayAllStaffMembers =
        HIDE_CALENDAR_FOR_BUSINESSES.includes(businessData?.id || '') &&
        user?.accountType !== AccountType.BusinessOwner;

    return (
        <Screen disablePadding disableScroll bgColor={'backgroundSecondary'}>
            <Container bgColor={'backgroundPrimary'} pb={1.5} zIndex={3}>
                <ScreenHeader
                    bgColor={'transparent'}
                    headline={t('appointments')}
                    headlineActions={
                        <>
                            <IconButton disablePadding mr={2} onPress={() => refetchBusiness()}>
                                <MaterialIcons name={'refresh'} />
                            </IconButton>
                            <IconButton disablePadding mr={2} onPress={handleBreakButton}>
                                <MaterialCommunityIcons name={'coffee-outline'} />
                            </IconButton>
                            <IconButton disablePadding onPress={() => navigate('NotificationsScreen')}>
                                <MaterialIcons name={'notifications'} />
                            </IconButton>
                        </>
                    }
                />
                {businessData?.hidden && <BusinessNotVisibleBanner />}
                <WaitingList date={selectedDay} staffId={selectedMember || user?.uid || ''} />
                <DatePicker
                    value={selectedDay}
                    onChange={setSelectedDay}
                    availableDays={days || []}
                    withinScreen
                    closeOnSelect
                />
                {!shouldNotDisplayAllStaffMembers && staffMembers.length > 1 && (
                    <VStack pt={2}>
                        <HStack fullWidth>
                            <ScrollView horizontal={true} bounces={false} showsHorizontalScrollIndicator={false}>
                                {staffMembers.map((staffMember) => (
                                    <Chip
                                        key={staffMember.uid}
                                        label={staffMember.displayName}
                                        value={staffMember.uid}
                                        checked={selectedMember === staffMember.uid}
                                        onChecked={setSelectedMember}
                                        avatar={
                                            <Avatar
                                                bgColor={'backgroundSecondary'}
                                                source={staffMember.photoURL}
                                                name={staffMember.displayName}
                                            />
                                        }
                                    />
                                ))}
                            </ScrollView>
                        </HStack>
                    </VStack>
                )}
            </Container>

            <VStack justifyContent={'center'} alignItems={'center'} flex zIndex={1}>
                <Calendar
                    events={businessEvents}
                    activeHours={activeIntervals}
                    onRefresh={refetchBusiness}
                    onCreate={handleCreateEvent}
                    onPressEvent={handlePressEvent}
                />

                {isBusinessLoading ? (
                    <VStack
                        bgColor={transparentize(0.3, theme.palette.backgroundSecondary.main)}
                        position={'absolute'}
                        alignContent={'center'}
                        justifyContent={'center'}
                        alignItems={'center'}
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        zIndex={5}
                    >
                        <CircularProgress />
                    </VStack>
                ) : null}

                {/* <VStack position={'absolute'} bottom={24} right={24}> */}
                {/*     <IconButton */}
                {/*         onPress={handleAddNewEvent} */}
                {/*         variant={'contained'} */}
                {/*         color={'accent'} */}
                {/*         size={'large'} */}
                {/*         borderRadius={32} */}
                {/*         style={theme.shadows.medium} */}
                {/*     > */}
                {/*         <MaterialCommunityIcons name={'plus-circle-outline'} size={32} /> */}
                {/*     </IconButton> */}
                {/* </VStack> */}
                {!!appointmentCount && appointmentCount >= 75 && (
                    <VStack position={'absolute'} bottom={24} left={24}>
                        <FreePlanCountAppointmentsPieChart
                            modal={freePlanCountModal}
                            minimal
                            count={appointmentCount}
                            p={0.5}
                            width={56}
                            height={56}
                            borderRadius={99}
                            bgColor={theme.palette.backgroundPrimary.main}
                        />
                    </VStack>
                )}
            </VStack>
            <BottomSheet open={selectedEvent !== null} onClose={() => setSelectedEvent(null)} snapPoints={[300]}>
                <Container pt={2}>
                    <Typography variant={'title2'} fontWeight={700} mb={3}>
                        {selectedEvent?.title}
                    </Typography>
                    <Button
                        startIcon={<MaterialCommunityIcons name={'coffee-outline'} />}
                        label={t('seeAllBreaks')}
                        align={'flex-start'}
                        size={'large'}
                        color={'primary'}
                        variant={'subtle'}
                        mb={2}
                        onPress={handleSeeAllBreaks}
                    />
                    <Button
                        startIcon={<MaterialIcons name={'delete'} color={theme.palette.error.main} />}
                        label={t('deleteBreak')}
                        align={'flex-start'}
                        size={'large'}
                        color={'primary'}
                        variant={'subtle'}
                        onPress={handleDeleteBreak}
                    />
                </Container>
            </BottomSheet>
        </Screen>
    );
}
