import { useIsFocused, useNavigation } from '@react-navigation/native';
import * as Contacts from 'expo-contacts';
import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Linking, Pressable } from 'react-native';
import { useMutation } from 'react-query';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import Button from '~/components/ui/Button';
import { Checkbox } from '~/components/ui/Checkbox';
import { HStack, VStack } from '~/components/ui/Grid';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { ImportClientRow } from '../components/ImportClientRow';

export type ImportContact = {
    displayName: string;
    phoneNumber: string;
};

export function ContactsListPage(): ReactElement {
    const { t } = useTranslation();
    const notifications = useNotifications();
    const { goBack } = useNavigation();
    const styles = useStyles();
    const [contacts, setContacts] = useState<Contacts.Contact[]>([]);
    const [permissionDenied, setPermissionDenied] = useState(false);
    const [contactsToImport, setContactsToImport] = useState<ImportContact[]>([]);
    const [selectAll, setSelectAll] = useState(false);
    const isFocused = useIsFocused();

    const getContacts = useEvent(async () => {
        const { data } = await Contacts.getContactsAsync({
            fields: [Contacts.Fields.PhoneNumbers],
        });

        if (data.length > 0) {
            setContacts(data);
        }
    });

    useEffect(() => {
        const checkPermission = async (): Promise<void> => {
            const contactsPermission = await Contacts.requestPermissionsAsync();
            if (contactsPermission.status === Contacts.PermissionStatus.GRANTED) {
                setPermissionDenied(false);
                getContacts();
            } else {
                setPermissionDenied(true);
            }
        };
        if (isFocused) {
            checkPermission();
        }
    }, [getContacts, isFocused]);

    const importClientMutation = useMutation(
        'importClient',
        () => businessClientsEndpoint.importBulk(contactsToImport),
        {
            onError: () => {
                goBack();
                setTimeout(() => {
                    notifications.error(t('clientImportFailed'));
                }, 500);
            },
            onSuccess: () => {
                goBack();
                setTimeout(() => {
                    notifications.info(t('clientImportStarted'));
                }, 500);
            },
        },
    );

    const handleGrantPermissionPress = useEvent(async () => {
        await Linking.openSettings();
    });

    const handleSubmit = useEvent(async () => {
        await importClientMutation.mutateAsync();
    });

    const handleContactPress = useEvent((importContact: ImportContact, isSelected: boolean): void => {
        if (isSelected) {
            setContactsToImport(contactsToImport.filter((it) => it.displayName !== importContact.displayName));
        } else {
            setContactsToImport([...contactsToImport, importContact]);
        }
    });

    const handleSelectAllPress = useEvent(() => {
        setSelectAll(!selectAll);
        if (selectAll) {
            setContactsToImport([]);
        } else {
            const importContacts = contacts.map((contact) => {
                return {
                    displayName: [contact.lastName, contact.firstName].filter(Boolean).join(' '),
                    phoneNumber: contact.phoneNumbers?.[0].number,
                } as ImportContact;
            });
            setContactsToImport(importContacts);
        }
    });

    if (permissionDenied) {
        return (
            <VStack alignItems={'center'} flex safeBottom={2}>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={1} textAlign={'center'}>
                    {t('contactPermission')}
                </Typography>
                <Spacer />
                <Button
                    color={'accent'}
                    size={'large'}
                    width={'100%'}
                    variant={'contained'}
                    label={t('grantAccess')}
                    onPress={handleGrantPermissionPress}
                />
            </VStack>
        );
    }

    if (contacts.length === 0) {
        return (
            <VStack alignItems={'center'} flex safeBottom={2}>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={1} textAlign={'center'}>
                    {t('noContacts')}
                </Typography>
            </VStack>
        );
    }

    return (
        <VStack flex safeBottom={2}>
            <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500}>
                {t('yourContacts')}:
            </Typography>
            <FlatList
                showsVerticalScrollIndicator={false}
                data={contacts}
                ListHeaderComponent={
                    <Pressable onPress={handleSelectAllPress}>
                        <HStack
                            py={2}
                            pl={2}
                            pr={4}
                            fullWidth
                            alignItems={'center'}
                            justifyContent={'space-between'}
                            bgColor={selectAll ? 'accent.shade10' : 'transparent'}
                            borderRadius={12}
                        >
                            <VStack>
                                <Typography variant={'callout'} fontWeight={500}>
                                    {t('allContacts')}
                                </Typography>
                                <Typography variant={'caption1'} color={'textSecondary'} fontWeight={500}>
                                    {t('addNoOfContacts', { noOfContacts: contacts.length })}
                                </Typography>
                            </VStack>
                            <Checkbox checked={selectAll} onPress={handleSelectAllPress} noFeedback />
                        </HStack>
                    </Pressable>
                }
                keyExtractor={(item): string => `${item.id}`}
                style={styles.flatList}
                initialNumToRender={20}
                maxToRenderPerBatch={10}
                renderItem={({ item }): ReactElement => (
                    <ImportClientRow
                        onPress={handleContactPress}
                        contactDetails={item}
                        contactsToImport={contactsToImport}
                    />
                )}
            />
            <Button
                color={'accent'}
                size={'large'}
                loading={importClientMutation.isLoading}
                disabled={contactsToImport.length === 0}
                variant={'contained'}
                label={t('addClients')}
                onPress={handleSubmit}
            />
        </VStack>
    );
}

const useStyles = makeStyles(() => ({
    flatList: {
        marginBottom: 16,
    },
}));
