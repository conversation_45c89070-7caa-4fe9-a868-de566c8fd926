import { useRoute } from '@react-navigation/native';
import { ReactElement } from 'react';
import { FlatList } from 'react-native';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { AppointmentCard } from '~/components/AppointmentCard';
import { Icon } from '~/components/ui/Icon';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { navigate, navigateBack } from '~/lib/utils/navigation';

const useStyles = makeStyles<{ padding: number }, Styles<'list'>>(({ padding, theme }) => ({
    list: {
        flex: 1,
        paddingHorizontal: theme.mixins.spacingValue(padding),
    },
}));

interface ClientDetailsAppointmentsScreenParams {
    appointments?: AppointmentModel[];
    client?: UserModel;
}

export function ClientDetailsAppointmentsScreen(): ReactElement {
    const insets = useSafeAreaInsets();
    const padding = useContainerPadding();
    const styles = useStyles({ padding });
    const route = useRoute();
    const { appointments = [], client } = route.params as ClientDetailsAppointmentsScreenParams;

    return (
        <Screen bgColor={'backgroundSecondary'} disableScroll disablePadding>
            <ScreenHeader
                disableSafeViewArea
                bgColor={'backgroundSecondary'}
                pt={2}
                px={padding}
                rightSlot={<ScreenHeaderBackButton icon={<Icon name="close" />} />}
            />
            <FlatList
                style={styles.list}
                contentContainerStyle={{ paddingBottom: insets.bottom }}
                data={appointments}
                initialNumToRender={20}
                keyExtractor={(item): string => `${item.id}`}
                renderItem={({ item }): ReactElement => (
                    <AppointmentCard
                        businessView
                        mb={1}
                        appointment={item}
                        client={client}
                        onPress={(): void => {
                            navigateBack();
                            navigate('AppointmentDetailsScreen', {
                                appointment: item,
                                client: client,
                                businessView: true,
                            });
                        }}
                    />
                )}
            />
        </Screen>
    );
}
