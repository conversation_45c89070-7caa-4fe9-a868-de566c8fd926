import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as Sentry from '@sentry/react-native';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { MediaTypeOptions } from 'expo-image-picker';
import * as Sharing from 'expo-sharing';
import React, { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking, Platform } from 'react-native';
import { useMutation, useQuery } from 'react-query';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { businessBlockEndpoint } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { ClientDetailsModel } from '@bookr-technologies/api/models/ClientDetailsModel';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { BusinessApplicationNavigationProp } from '~/RoutesParams';
import { ContactDetails } from '~/app/business/components/ContactDetails';
import { StatsCard } from '~/app/business/components/StatsCard';
import { TotalRevenue } from '~/app/business/components/TotalRevenue';
import { Loader } from '~/app/common/components/Loader';
import { AppointmentCard } from '~/components/AppointmentCard';
import { BottomSheet } from '~/components/BottomSheet';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { NotesList } from '~/components/NotesList/NotesList';
import { PrivilegeBasedAccess } from '~/components/PrivilegeBasedAccess/PrivilegeBasedAccess';
import { Avatar } from '~/components/ui/Avatar';
import Button from '~/components/ui/Button';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { Container, Grid, HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Tab } from '~/components/ui/Tab';
import { TabContainer } from '~/components/ui/TabContainer';
import { useTabs } from '~/components/ui/TabContainer/TabContainerContext';
import { Tabs } from '~/components/ui/Tabs';
import { Typography, TypographyLink } from '~/components/ui/Typography';
import { useBusiness } from '~/hooks/useBusiness';
import { useBusinessClient } from '~/hooks/useBusinessClients';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useImagePicker } from '~/hooks/useImagePicker';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { getFirebaseUser } from '~/lib/firebase/auth';
import { num } from '~/lib/utils/number';
import { useAuthStore } from '~/store/useAuthStore';
import { ClientActions } from '../components/ClientActions';

interface ClientDetailsScreenParams {
    clientId?: string;
}

enum ScreenTabs {
    Appointments = 'appointments',
    Documents = 'documents',
    Notes = 'notes',
}

export function ClientDetailsScreen(): ReactElement {
    const route = useRoute();
    const [showExtraActions, setShowExtraActions] = useState(false);
    const [showBlockClientSheet, setShowBlockClientSheet] = useState(false);
    const [showDeleteClientSheet, setShowDeleteClientSheet] = useState(false);
    const user = useAuthStore((state) => state.user);
    const { clientId = '' } = route.params as ClientDetailsScreenParams;
    const business = useBusiness(user?.business.id);
    const { t } = useTranslation();
    const { goBack } = useNavigation<BusinessApplicationNavigationProp<'ClientDetailsScreen'>>();
    const notifications = useNotifications();
    const containerPadding = useContainerPadding();
    const { data, isLoading, refetch } = useBusinessClient(clientId, {
        onError: () => {
            notifications.error(t('cannotLoadClient'));
            goBack();
        },
    });

    const refreshControl = useRefreshControl(refetch);
    const clientCta = useMemo(() => {
        if (data?.client?.phoneNumber) {
            return `tel:${data.client.phoneNumber}`;
        }

        if (data?.client?.email) {
            return `mailto:${data.client.email}`;
        }
    }, [data]);

    const isBlocked = useMemo(
        () => (business.data?.blocked ?? []).includes(clientId),
        [business.data?.blocked, clientId],
    );

    const blockUserMutation = useMutation('blockUser', () => businessBlockEndpoint.blockClient(clientId, !isBlocked), {
        onError: (e) => {
            notifications.error(getErrorMessage(e));
        },
        onSuccess: () => {
            business.refetch();
            handleCloseShowMore();
        },
    });

    const deleteClientMutation = useMutation('deleteClient', () => businessClientsEndpoint.destroy(clientId), {
        onError: (e) => {
            notifications.error(getErrorMessage(e));
        },
        onSuccess: async () => {
            handleCloseShowMore();
            goBack();
        },
    });

    const handleShowMorePress = useEvent(() => {
        setShowExtraActions(true);
    });

    const handleCloseShowMore = useEvent(() => {
        setShowExtraActions(false);
    });

    const handleBlockClientSheetOpen = useEvent(() => {
        if (isBlocked) {
            handleBlockUserPress();
        } else {
            setShowBlockClientSheet(true);
        }
    });

    const handleDeleteClientSheetOpen = useEvent(() => {
        setShowDeleteClientSheet(true);
    });

    const handleBlockSheetClose = useEvent(() => {
        handleCloseShowMore();
        setShowBlockClientSheet(false);
    });

    const handleDeleteSheetClose = useEvent(() => {
        handleCloseShowMore();
        setShowDeleteClientSheet(false);
    });

    const handleSendSMSPress = useEvent(() => {
        const message = '';
        const separator = Platform.OS === 'ios' ? '&' : '?';
        const url = `sms:${data?.client.phoneNumber}${separator}body=${message}`;
        Linking.openURL(url);
    });

    const handleSendEmailPress = useEvent(() => {
        const subject = 'BOOKR - ' + user?.displayName;
        const message = '';
        Linking.openURL(`mailto:${data?.client.email}?subject=${subject}&body=${message}`);
    });

    const handleBlockUserPress = useEvent(async () => {
        await blockUserMutation.mutateAsync();
        handleBlockSheetClose();
    });

    const handleDeleteClientPress = useEvent(async () => {
        deleteClientMutation.mutate();
        handleBlockSheetClose();
    });

    const blockMessage = isBlocked ? t('unblockClient') : t('blockClient');

    return (
        <Screen bgColor={'backgroundSecondary'} disableScroll disablePadding>
            <ScreenHeader px={containerPadding} bgColor={'backgroundSecondary'} leftSlot={<ScreenHeaderBackButton />} />
            {isLoading && <Loader width={'100%'} mb={2} />}

            {data && (
                <VStack
                    px={containerPadding}
                    alignItems={'flex-start'}
                    justifyContent={'flex-start'}
                    scrollable
                    scrollViewProps={{ refreshControl }}
                >
                    <Paper
                        fullWidth
                        bgColor={'secondary'}
                        px={2}
                        py={2}
                        mt={1}
                        flexWrap={'nowrap'}
                        flexShrink={0}
                        flexDirection={'column'}
                    >
                        <HStack>
                            <Avatar source={data.client?.photoURL} name={data.client?.displayName} size={48} />
                            <VStack ml={2} flexGrow>
                                <Typography variant={'title3'} fontWeight={700}>
                                    {data.client?.displayName}
                                </Typography>
                                <TypographyLink
                                    href={clientCta}
                                    variant={'footnote'}
                                    color={'textSecondary'}
                                    fontWeight={500}
                                    external
                                >
                                    {data.client?.phoneNumber || data.client?.email || t('noContactInformation')}
                                </TypographyLink>
                            </VStack>
                        </HStack>
                        {data?.client.accountType !== AccountType.Invalid && (
                            <ClientActions client={data.client} onShowMorePress={handleShowMorePress} />
                        )}
                    </Paper>

                    <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]}>
                        <>
                            <HStack my={1} justifyContent={'space-between'} flexWrap={'nowrap'}>
                                <StatsCard
                                    value={num(data.stats?.totalAppointments)}
                                    title={t('totalBookings')}
                                    mr={1}
                                />
                                <StatsCard value={num(data.stats?.totalFinished)} title={t('totalFinished')} mr={1} />
                                <StatsCard value={num(data.stats?.totalCancelled)} title={t('totalCancelled')} mr={1} />
                                <StatsCard value={num(data.stats?.totalNoShows)} title={t('totalNoShows')} />
                            </HStack>

                            <TotalRevenue earnings={data.stats?.totalRevenue ?? {}} mb={2} />
                            <ContactDetails phoneNumber={data.client?.phoneNumber} email={data.client?.email} />

                            <TabContainer value={ScreenTabs.Appointments}>
                                <Tabs mt={1}>
                                    <Tab value={ScreenTabs.Appointments}>{t('appointments')}</Tab>
                                    <Tab value={ScreenTabs.Documents}>{t('documents')}</Tab>
                                    <Tab value={ScreenTabs.Notes}>{t('notes')}</Tab>
                                </Tabs>
                                <ClientTabsContent data={data} />
                            </TabContainer>
                        </>
                    </PrivilegeBasedAccess>
                </VStack>
            )}
            <BottomSheet open={showExtraActions} onClose={handleCloseShowMore} snapPoints={[350]}>
                <Container px={3}>
                    <Button
                        color={'backgroundSecondary'}
                        size={'large'}
                        mb={1}
                        mt={3}
                        variant={'contained'}
                        label={t('sendSMS')}
                        onPress={handleSendSMSPress}
                        startIcon={<Icon variant={'secondary'} name={'message-processing'} />}
                        align={'flex-start'}
                    />
                    <Button
                        color={'backgroundSecondary'}
                        size={'large'}
                        mb={1}
                        variant={'contained'}
                        label={t('sendMail')}
                        onPress={handleSendEmailPress}
                        startIcon={<Icon variant={'secondary'} name={'email'} />}
                        align={'flex-start'}
                    />
                    <Button
                        size={'large'}
                        mb={1}
                        color={'backgroundSecondary'}
                        variant={'contained'}
                        label={blockMessage}
                        onPress={handleBlockClientSheetOpen}
                        startIcon={<Icon variant={'secondary'} name={'block-helper'} />}
                        align={'flex-start'}
                    />
                    <Button
                        size={'large'}
                        variant={'subtle'}
                        label={t('deleteClient')}
                        onPress={handleDeleteClientSheetOpen}
                        color={'error'}
                        startIcon={<Icon variant={'secondary'} name={'delete'} />}
                        align={'flex-start'}
                    />
                </Container>
            </BottomSheet>
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                open={showBlockClientSheet}
                onClose={handleBlockSheetClose}
                headline={t('sureBlock', { clientName: data?.client?.displayName })}
                subHeadline={t('blockInfo', { clientName: data?.client?.displayName })}
                onAction={handleBlockUserPress}
                primaryText={t('block')}
                secondaryText={t('cancel')}
            />
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                open={showDeleteClientSheet}
                onClose={handleDeleteSheetClose}
                headline={t('sureDelete', { clientName: data?.client?.displayName })}
                subHeadline={t('deleteInfo', { clientName: data?.client?.displayName })}
                onAction={handleDeleteClientPress}
                primaryText={t('delete')}
                secondaryText={t('cancel')}
            />
        </Screen>
    );
}

function ClientTabsContent({ data }: { data: ClientDetailsModel }) {
    const notifications = useNotifications();
    const { t } = useTranslation();
    const { navigate } = useNavigation<BusinessApplicationNavigationProp<'ClientDetailsScreen'>>();
    const tabs = useTabs();
    const { data: clientNotesData, refetch } = useQuery(
        ['notes', data.client.uid],
        () => businessClientsEndpoint.getClientNotes(data.client.uid),
        {
            retry: 1,
            onError: () => notifications.error(t('somethingWentWrong')),
        },
    );
    const createNoteMutation = useMutation(
        'createClientNote',
        async (text: string) =>
            businessClientsEndpoint.createClientNote(data.client.uid, {
                text,
                id: 0,
                createdAt: new Date().toISOString(),
            }),
        {
            onSuccess: () => {
                notifications.success(t('noteCreated'));
            },
        },
    );

    const updateNoteMutation = useMutation(
        'updateClientNote',
        async (note: NoteModel) => {
            if (!note.id) {
                notifications.error(t('somethingWentWrong'));
                return;
            }

            return businessClientsEndpoint.updateClientNote(data.client.uid, note);
        },
        {
            onSuccess: () => {
                notifications.success(t('noteUpdated'));
            },
        },
    );

    if (tabs?.currentTab === ScreenTabs.Appointments) {
        return (
            <VStack width={'100%'} mt={2}>
                {data?.appointments?.slice(0, 10)?.map((appointment) => (
                    <AppointmentCard
                        key={appointment.id}
                        mb={1}
                        appointment={appointment}
                        client={data.client}
                        businessView
                    />
                ))}
                {data?.appointments?.length > 10 && (
                    <TypographyLink
                        textAlign={'center'}
                        variant={'body'}
                        color={'accent'}
                        fontSize={14}
                        pt={1}
                        pb={6}
                        onPress={(): void =>
                            navigate('ClientDetailsAppointmentsScreen', {
                                appointments: data?.appointments ?? [],
                                client: data.client,
                            })
                        }
                    >
                        {t('showAllAppointments')}
                    </TypographyLink>
                )}
            </VStack>
        );
    }
    if (tabs?.currentTab === ScreenTabs.Documents) {
        return <DocumentList clientId={data.client.uid} />;
    }

    if (tabs?.currentTab === ScreenTabs.Notes) {
        const handleSubmitAddNotes = async (note: string) => {
            await createNoteMutation.mutateAsync(note);
            await refetch();
        };

        const handleSubmitEditNote = async (note: NoteModel) => {
            updateNoteMutation.mutate(note);
            await refetch();
        };

        return !!clientNotesData ? (
            <NotesList
                createNote={handleSubmitAddNotes}
                editNote={handleSubmitEditNote}
                notes={clientNotesData}
                mt={2}
                fullWidth
            />
        ) : (
            <VStack alignItems={'center'} justifyContent={'center'} pb={3}>
                <CircularProgress spinDuration={1000} />
            </VStack>
        );
    }
    return null;
}

function DocumentList({ clientId }: { clientId: string }) {
    const notifications = useNotifications();
    const { t } = useTranslation();
    const [showSelectFileSource, setShowSelectFileSource] = useState(false);
    const [showDocumentOptions, setShowDocumentOptions] = useState(false);
    const [currentDocument, setCurrentDocument] = useState<string>('');

    const imagePicker = useImagePicker({
        mediaTypes: MediaTypeOptions.Images,
    });

    const {
        data: documents,
        isLoading,
        isFetching,
        refetch,
    } = useQuery(`documents:${clientId}`, () => businessClientsEndpoint.getDocuments(clientId), {
        retry: 0,
        onError: () => notifications.error(t('somethingWentWrong')),
        enabled: true,
    });
    const handleAddDocument = async () => {
        const result = await DocumentPicker.getDocumentAsync({ type: '*/*' });
        if (!result.canceled && result.assets?.length > 0) {
            const file = {
                uri: result.assets[0].uri,
                name: result.assets[0].name,
                type: result.assets[0].mimeType,
            };

            try {
                const formData = new FormData();
                formData.append('file', file as any, file.name);
                setShowSelectFileSource(false);

                await businessClientsEndpoint.uploadDocument(clientId, formData, {
                    headers: {
                        Accept: 'application/json',
                        'Content-Type': 'multipart/form-data',
                    },
                    transformRequest: () => formData,
                });
                notifications.success(t('documentAdded'));
                refetch();
            } catch (e) {
                notifications.error(t(getErrorMessage(e, 'somethingWentWrong') as any));
            }
        }
    };

    const handleAddPhoto = async () => {
        await imagePicker.pick();
        const image = imagePicker.asUploadFile();
        setShowSelectFileSource(false);

        if (image) {
            const formData = new FormData();
            formData.append('file', image as any, image.name);

            try {
                await businessClientsEndpoint.uploadDocument(clientId, formData, {
                    headers: {
                        Accept: 'application/json',
                        'Content-Type': 'multipart/form-data',
                    },
                    transformRequest: () => formData,
                });
                notifications.success(t('imageAdded'));
                refetch();
            } catch (e) {
                console.log(e);
                notifications.error(t(getErrorMessage(e, 'somethingWentWrong') as any));
            }
        }
    };

    const onDocumentPress = (document: string) => {
        setCurrentDocument(document);
        setShowDocumentOptions(true);
    };

    const handleDeleteDocument = async () => {
        if (!currentDocument) {
            return;
        }
        try {
            await businessClientsEndpoint.deleteDocument(clientId, currentDocument.split('/').pop() as string);
            await refetch();
            setCurrentDocument('');
            setShowDocumentOptions(false);
            notifications.success(t('documentDeleted'));
        } catch (e) {
            notifications.error(t(getErrorMessage(e, 'somethingWentWrong') as any));
        }
    };

    const handleDownloadDocument = async () => {
        if (!currentDocument) {
            return;
        }
        const filename = currentDocument.split('/').pop() as string;
        setShowDocumentOptions(false);
        notifications.info(t('downloadingDocument'));

        const firebaseUser = getFirebaseUser();
        const token = (await firebaseUser?.getIdToken()) ?? null;
        const fileUri = `${FileSystem.documentDirectory}${filename}`;

        const downloadResumable = FileSystem.createDownloadResumable(
            `${businessClientsEndpoint.api.getUri()}/${clientId}/documents/${filename}`,
            fileUri,
            { headers: { Authorization: 'Bearer ' + token } },
            (downloadProgress) => {
                const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
                console.log(`Download progress: ${progress}`);
                // setProgress({
                //     downloadProgress: progress,
                // });
            },
        );

        try {
            const response = await downloadResumable.downloadAsync();
            await Sharing.shareAsync(response?.uri || '');
        } catch (e) {
            console.log('Error downloading file', e);
            notifications.error(t(getErrorMessage(e, 'somethingWentWrong') as any));
            Sentry.captureException(e, {
                extra: {
                    clientId,
                    filename,
                    uid: firebaseUser?.uid,
                },
            });
        }
    };

    return (
        <Paper flex width={'100%'} bgColor={'backgroundPrimary'} p={2} mt={2}>
            <HStack flex fullWidth justifyContent={'space-between'}>
                <Typography variant={'title3'} fontWeight={700} lineHeight={25.2}>
                    {t('documents')}
                </Typography>
                <Button
                    color={'accent'}
                    size={'xsmall'}
                    variant={'subtle'}
                    label={t('add')}
                    onPress={() => setShowSelectFileSource(true)}
                    startIcon={<MaterialCommunityIcons name={'plus'} />}
                />
            </HStack>
            {isLoading ||
                (isFetching && (
                    <VStack alignItems={'center'} justifyContent={'center'} pt={3}>
                        <CircularProgress spinDuration={500} />
                    </VStack>
                ))}
            {(!documents || documents?.length === 0) && !isLoading && (
                <Typography pt={2} variant={'footnote'} fontWeight={500} width={'50%'} color={'textSecondary'}>
                    {t('noDocuments')}
                </Typography>
            )}
            {documents &&
                documents.length > 0 &&
                documents.map((document) => (
                    <Paper
                        key={document}
                        bgColor={'backgroundSecondary'}
                        fullWidth
                        mt={1}
                        py={1.5}
                        px={2}
                        justifyContent={'space-between'}
                        alignItems={'center'}
                        flexDirection={'row'}
                    >
                        <HStack alignItems={'center'} xs={11}>
                            <Icon variant={'secondary'} name={'file-document'} size={20} />
                            <Typography ml={1} variant={'footnote'} fontWeight={500} overflow={'hidden'}>
                                {document.split('/').pop()}
                            </Typography>
                        </HStack>
                        <Grid xs={1}>
                            <IconButton size={'small'} variant={'text'} onPress={() => onDocumentPress(document)}>
                                <Icon name={'more-horiz'} />
                            </IconButton>
                        </Grid>
                    </Paper>
                ))}
            <BottomSheet open={showSelectFileSource} onClose={() => setShowSelectFileSource(false)} snapPoints={[250]}>
                <Container px={3}>
                    <Button
                        color={'backgroundSecondary'}
                        size={'large'}
                        mb={1}
                        mt={3}
                        variant={'contained'}
                        label={t('uploadDocument')}
                        onPress={handleAddDocument}
                        startIcon={<Icon variant={'secondary'} name={'file-document'} />}
                        align={'flex-start'}
                    />
                    <Button
                        color={'backgroundSecondary'}
                        size={'large'}
                        mb={1}
                        variant={'contained'}
                        label={t('uploadImage')}
                        onPress={handleAddPhoto}
                        startIcon={<Icon variant={'primary'} name={'photo'} />}
                        align={'flex-start'}
                    />
                </Container>
            </BottomSheet>
            <BottomSheet open={showDocumentOptions} onClose={() => setShowDocumentOptions(false)} snapPoints={[250]}>
                <Container px={3}>
                    <Button
                        color={'backgroundSecondary'}
                        size={'large'}
                        mb={1}
                        mt={3}
                        variant={'contained'}
                        label={t('downloadDocument')}
                        onPress={handleDownloadDocument}
                        startIcon={<Icon variant={'secondary'} name={'download'} />}
                        align={'flex-start'}
                    />
                    <Button
                        color={'backgroundSecondary'}
                        size={'large'}
                        mb={1}
                        variant={'contained'}
                        label={t('deleteDocument')}
                        onPress={handleDeleteDocument}
                        startIcon={<Icon variant={'primary'} color={'error'} name={'delete'} />}
                        align={'flex-start'}
                    />
                </Container>
            </BottomSheet>
        </Paper>
    );
}
