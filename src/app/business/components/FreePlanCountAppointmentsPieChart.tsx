import { MaterialCommunityIcons } from '@expo/vector-icons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import Svg from 'react-native-svg';
import { useTheme } from 'styled-components/native';
import { VictoryPie } from 'victory-native';
import { BottomSheet as BottomSheetModal } from '~/components/BottomSheet';
import { Button } from '~/components/ui/Button';
import { Container, HStack, VStack, VStackProps } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useModal } from '~/hooks/useModal';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';

interface Props extends VStackProps {
    count: number;
    minimal?: boolean;
    modal: ReturnType<typeof useModal>;
}

const useStyles = makeStyles(({ theme }) => ({
    textStyle: {
        fontSize: Number(theme.typography.variants.title1.fontSize),
        color: theme.palette.typography.textPrimary,
    },
    iconContainer: {
        position: 'absolute',
        left: '25%',
        top: '25%',
        justifyContent: 'center',
        alignItems: 'center',
    },

    statsContainer: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
}));

export function FreePlanCountAppointmentsPieChart({ modal, count, minimal = false, ...rest }: Props): JSX.Element {
    const theme = useTheme();
    const max = 100;
    const percentage = (count / max) * 100;
    const size = 48;
    const { t } = useTranslation();
    const styles = useStyles();
    const { navigate } = useNavigation();

    const handleOnPressModalOpen = () => {
        logAnalyticsEvent(AnalyticsEvent.FreePlanAppointmentsViewInfo);
        modal.open();
    };

    const handleOnIncreaseLimit = () => {
        logAnalyticsEvent(AnalyticsEvent.FreePlanAppointmentsIncreaseLimit);
        modal.close();
        navigate('ChooseSubscriptionPlanScreen', {});
    };
    const mainColor =
        count < 75 ? theme.palette.accent.main : count < 90 ? theme.palette.warning.main : theme.palette.error.main;

    return (
        <TouchableOpacity onPress={handleOnPressModalOpen} style={{ width: '100%' }}>
            <VStack width={'100%'} alignItems={'center'} {...rest}>
                <HStack width={'100%'} alignItems={'center'}>
                    <View style={{ width: size, height: size }}>
                        <Svg width={size} height={size}>
                            <VictoryPie
                                radius={size / 2}
                                labels={(): null => null}
                                innerRadius={size / 2.25}
                                animate={{ duration: 500 }}
                                width={size}
                                height={size}
                                data={[
                                    { key: 'progress', y: percentage },
                                    { key: 'remaining', y: 100 - percentage },
                                ]}
                                colorScale={[mainColor, theme.palette.backgroundTertiary.main]}
                            />
                        </Svg>
                        <View style={styles.iconContainer}>
                            <MaterialCommunityIcons name="calendar-blank-outline" size={size / 2} color={mainColor} />
                        </View>
                    </View>

                    {!minimal && (
                        <VStack ml={1}>
                            <Typography variant={'subhead'} fontWeight={500}>
                                {t('performanceStats.totalAppointments')}
                            </Typography>
                            <Typography
                                variant={'footnote'}
                                fontWeight={500}
                                color={'textSecondary'}
                                textTransform={'lowercase'}
                            >
                                {count} / {max} {t('appointments')}
                            </Typography>
                        </VStack>
                    )}
                </HStack>
                {!minimal && count >= 75 && (
                    <Button
                        mt={2}
                        fullWidth
                        label={t('upgradeLimit')}
                        variant={'subtle'}
                        color={'accent'}
                        onPress={handleOnPressModalOpen}
                    />
                )}
            </VStack>
            <BottomSheetModal snapPoints={['90%']} {...modal.props}>
                <Container flex safeBottom>
                    <HStack justifyContent={'space-between'} alignItems={'center'}>
                        <Typography variant={'title2'} fontWeight={700}>
                            {t('appointmentLimits')}
                        </Typography>
                        <IconButton
                            width={30}
                            height={30}
                            onPress={modal.close}
                            borderRadius={15}
                            variant={'contained'}
                            color={'backgroundTertiary'}
                            my={1}
                        >
                            <MaterialCommunityIcons name={'close'} size={20} />
                        </IconButton>
                    </HStack>

                    <Typography mt={2.5} variant={'subhead'} fontWeight={500} color={'contentTertiary'}>
                        {t('appointmentLimitsDescription')}
                    </Typography>
                    <Typography mt={1.25} variant={'subhead'} fontWeight={500}>
                        {t('appointmentLimitsDescriptionReset', {
                            date: moment().add(1, 'month').startOf('month').format('DD MMMM YYYY'),
                        })}
                    </Typography>

                    <VStack alignItems={'center'} justifyContent={'center'} flex={10} height={'100%'}>
                        <View style={{ width: 200, height: 200, position: 'relative' }}>
                            <Svg width={200} height={200}>
                                <VictoryPie
                                    radius={82}
                                    labels={(): null => null}
                                    innerRadius={200 / 2.25}
                                    endAngle={360}
                                    animate={true}
                                    width={200}
                                    height={200}
                                    data={[
                                        { key: '', y: percentage },
                                        { key: '', y: 100 - percentage },
                                    ]}
                                    colorScale={[mainColor, theme.palette.backgroundTertiary.main]}
                                />
                            </Svg>
                            <View style={styles.statsContainer}>
                                <VStack alignItems={'center'}>
                                    <Typography variant={'title1'} fontWeight={700}>
                                        {count}
                                    </Typography>
                                    <Typography variant={'footnote'} fontWeight={500} color={'contentTertiary'}>
                                        {t('outOf100Appointments')}
                                    </Typography>
                                </VStack>
                            </View>
                        </View>
                    </VStack>
                    <VStack flexGrow={2} justifyContent={'flex-end'}>
                        {count >= 75 && (
                            <HStack bgColor={'backgroundSecondary'} px={1} py={1.5} borderRadius={8} mb={1.25}>
                                <MaterialIcons name={'info-outline'} size={20} color={mainColor} />
                                <VStack pl={1} flex={1}>
                                    <Typography variant={'footnote'} fontWeight={500} color={'primary'}>
                                        {count < 100 ? t('limitAlmostReachedTitle') : t('limitReachedTitle')}
                                    </Typography>
                                    <Typography
                                        variant={'footnote'}
                                        fontWeight={500}
                                        color={'contentTertiary'}
                                        mt={0.5}
                                    >
                                        {count < 100
                                            ? t('limitAlmostReachedDescription')
                                            : t('limitReachedDescription')}
                                    </Typography>
                                </VStack>
                            </HStack>
                        )}
                        <Button
                            size={'large'}
                            label={t('upgradeLimit')}
                            color={'accent'}
                            onPress={handleOnIncreaseLimit}
                        />
                    </VStack>
                </Container>
            </BottomSheetModal>
        </TouchableOpacity>
    );
}
