import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment/moment';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components/native';
import { waitingListEndpoint } from '@bookr-technologies/api/endpoints/waitingListEndpoint';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';

interface WaitingListProps {
    date: string;
    staffId: string;
}

export function WaitingList({ date, staffId }: WaitingListProps): ReactElement | null {
    const theme = useTheme();
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const { data, isLoading, isError } = useQuery(
        `waitingList:${date}:${staffId}`,
        () => {
            return waitingListEndpoint.getWaitingList(staffId, moment(date).format('YYYY-MM-DD'));
        },
        {
            enabled: !!date && !!staffId,
        },
    );

    if (!data || isLoading || isError || (data && data.users.length === 0)) {
        return null;
    }

    const handleOnPress = () => {
        navigate('WaitingListScreen', { waitingList: data.users });
    };

    return (
        <TouchableOpacity onPress={handleOnPress}>
            <Paper
                bgColor={theme.palette.accent.shade10}
                flexDirection={'row'}
                px={2}
                py={1}
                alignItems={'center'}
                mb={1}
            >
                <MaterialIcons name="hourglass-top" size={24} color={theme.palette.accent.main} />
                <VStack ml={2}>
                    <Typography fontSize={12} fontWeight={500} variant={'caption1'} color={'accent'}>
                        {t('waitingListClients')}
                    </Typography>
                    <Typography pt={0.2} fontWeight={500} variant={'footnote'} color={'accent'}>
                        {data.users.length} {t('clients')}
                    </Typography>
                </VStack>
            </Paper>
        </TouchableOpacity>
    );
}
