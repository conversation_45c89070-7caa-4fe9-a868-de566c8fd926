import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useTheme } from 'styled-components/native';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';

interface Props {
    index: number;
    option: {
        smsAmount: string;
        smsPrice: string;
    };
    selected: boolean;

    onPress(index: number): void;
}

export function SmsOption({ option, selected, index, onPress }: Props): ReactElement {
    const theme = useTheme();
    const { t } = useTranslation();

    const handlePress = useEvent(() => onPress(index));

    return (
        <TouchableOpacity onPress={handlePress} activeOpacity={0.7}>
            <Paper
                key={option.smsAmount + ':' + option.smsPrice}
                bgColor={'backgroundPrimary'}
                mb={2}
                p={2}
                direction={'row'}
            >
                <MaterialIcons
                    name={selected ? 'radio-button-on' : 'radio-button-off'}
                    color={selected ? theme.palette.accent.main : theme.palette.contentSecondary.main}
                    size={24}
                />
                <VStack pl={2}>
                    <Typography variant={'subhead'} fontWeight={700} mb={0.5}>
                        {t('numberOfMessages', { value: option.smsAmount })}
                    </Typography>
                    <Typography variant={'subhead'} fontWeight={500} color={'disabled'}>
                        {option.smsPrice}
                    </Typography>
                </VStack>
            </Paper>
        </TouchableOpacity>
    );
}
