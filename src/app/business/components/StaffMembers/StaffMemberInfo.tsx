import { ReactElement } from 'react';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Avatar } from '~/components/ui/Avatar';
import { VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';

interface Props {
    item: UserModel;
}

export function StaffMemberInfo({ item }: Props): ReactElement {
    return (
        <>
            <Avatar source={item.photoURL} name={item.displayName} ml={1} size={40} />
            <VStack pl={1} flex>
                <Typography variant={'callout'} fontWeight={500} mb={0.5}>
                    {item.displayName}
                </Typography>
                <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                    {item.phoneNumber || item.email || 'N/A'}
                </Typography>
            </VStack>
        </>
    );
}
