import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DraggableFlatList, { DragEndParams } from 'react-native-draggable-flatlist';
import { useMutation } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { StaffMemberInfo } from '~/app/business/components/StaffMembers/StaffMemberInfo';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useWatchValue } from '~/hooks/useWatch';
import { StaffMemberRow } from './StaffMemberRow';

interface Props {
    items: UserModel[];
    loading: boolean;

    onReload(): Promise<void>;

    onOrder(records: Array<Pick<UserModel, 'uid'>>): void;
}

export function StaffMembers({ items, loading, onReload, onOrder }: Props): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const refreshControl = useRefreshControl(onReload);
    const deleteModal = useModal<UserModel>();
    const notifications = useNotifications();

    const [staffMembers, setStaffMembers] = useState(items);
    const listItems = useMemo(
        () =>
            staffMembers.sort((a, b) => {
                if (a.staffRank >= 0 && b.staffRank >= 0) {
                    return a.staffRank > b.staffRank ? 1 : -1;
                }

                return 0;
            }),
        [staffMembers],
    );

    const handleOnDragEnd = useEvent(({ data }: DragEndParams<UserModel>) => {
        setStaffMembers(
            data.map((item, index) => {
                item.staffRank = index;
                return item;
            }),
        );

        onOrder(data.map((s) => ({ uid: s.uid })));
    });

    const deleteStaffMutation = useMutation(
        'deleteStaff',
        async () => {
            if (!deleteModal.context?.uid) {
                return;
            }
            return businessEndpoint.removeStaffMember(deleteModal.context?.uid);
        },
        {
            onError: (e) => {
                notifications.error(getErrorMessage(e));
            },
        },
    );

    const handleDeleteStaffPress = useEvent(async () => {
        await deleteStaffMutation.mutateAsync();
        onReload();
    });

    useWatchValue(items, setStaffMembers);

    if (loading) {
        return (
            <VStack alignItems={'center'} p={4}>
                <CircularProgress />
            </VStack>
        );
    }

    if (!listItems?.length) {
        return (
            <VStack scrollable flex scrollViewProps={{ refreshControl }} py={3}>
                <Typography
                    variant={'caption2'}
                    color={'textSecondary'}
                    fontWeight={600}
                    textAlign={'center'}
                    fullWidth
                >
                    {t('noStaffMembersFound')}
                </Typography>
            </VStack>
        );
    }

    return (
        <>
            <DraggableFlatList
                refreshControl={refreshControl}
                data={listItems}
                containerStyle={styles.container}
                keyExtractor={(item): string => `draggable-item-${item.uid}`}
                onDragEnd={handleOnDragEnd}
                renderItem={({ item, getIndex, isActive, drag }): ReactElement => (
                    <StaffMemberRow
                        item={item}
                        index={getIndex()}
                        active={isActive}
                        drag={drag}
                        onDelete={deleteModal.openWithContext}
                    />
                )}
            />

            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                icon={<MaterialIcons name={'delete-forever'} />}
                snapPoints={[460]}
                headline={t('doYouWantToDeleteThisStaffMember')}
                secondaryText={t('iHaveChangedMyMind')}
                onAction={handleDeleteStaffPress}
                {...deleteModal.props}
            >
                {deleteModal.context && (
                    <HStack
                        alignItems={'center'}
                        py={2}
                        px={1.75}
                        borderRadius={12}
                        bgColor={'backgroundSecondary'}
                        mt={2}
                    >
                        <StaffMemberInfo item={deleteModal.context} />
                    </HStack>
                )}
            </BottomSheetAlert>
        </>
    );
}

const useStyles = makeStyles(() => ({
    container: {
        flex: 1,
    },
}));
