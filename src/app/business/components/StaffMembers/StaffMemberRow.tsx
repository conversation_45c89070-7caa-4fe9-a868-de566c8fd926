import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable } from 'react-native';
import { useTheme } from 'styled-components/native';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { StaffMemberInfo } from '~/app/business/components/StaffMembers/StaffMemberInfo';
import { Badge } from '~/components/ui/Badge';
import { Box } from '~/components/ui/Box';
import { Button } from '~/components/ui/Button';
import { HStack } from '~/components/ui/Grid';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { usePressable } from '~/hooks/usePressable';

interface Props {
    active: boolean;
    index?: number;
    item: UserModel;
    drag(): void;
    onDelete(user: UserModel): void;
}

export function StaffMemberRow({ item, active, drag, onDelete }: Props): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const theme = useTheme();
    const [isPressed, pressableProps] = usePressable();

    const handleDelete = useEvent(() => onDelete(item));

    return (
        <Pressable onLongPress={drag} {...pressableProps}>
            <HStack
                style={sx(styles.root, isPressed && styles.pressed, active && styles.active)}
                alignItems={'center'}
                p={1}
            >
                <MaterialIcons name={'drag-indicator'} size={20} color={theme.palette.primary.shade30} />
                <StaffMemberInfo item={item} />

                <Box minWidth={60}>
                    {item.accountType === AccountType.BusinessOwner ? (
                        <Badge title={'Owner'} />
                    ) : (
                        <Button
                            color={'error'}
                            label={t('delete')}
                            onPress={handleDelete}
                            px={0}
                            size={'xsmall'}
                            variant={'text'}
                        />
                    )}
                </Box>
            </HStack>
        </Pressable>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        borderRadius: 8,
    },
    pressed: {
        backgroundColor: theme.palette.backgroundSecondary.main,
    },
    active: {
        backgroundColor: theme.palette.backgroundTertiary.main,
    },
}));
