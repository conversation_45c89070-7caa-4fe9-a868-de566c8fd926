import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Paper, PaperProps } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';

interface TotalRevenueProps extends PaperProps {
    earnings: Record<string, number>;
}

export function TotalRevenue({ earnings = {}, ...rest }: TotalRevenueProps): ReactElement | null {
    const { t } = useTranslation();
    if (Object.keys(earnings).length === 0) {
        return null;
    }

    return (
        <Paper
            direction={'row'}
            bgColor={'backgroundPrimary'}
            justifyContent={'space-between'}
            alignItems={'center'}
            width={'100%'}
            px={2}
            py={1}
            {...rest}
        >
            <Typography color={'textSecondary'} variant={'footnote'} fontWeight={500} fontSize={13} lineHeight={19.5}>
                {t('totalRevenueFromClient') as string}
            </Typography>
            <Typography variant={'body'} fontWeight={700} fontSize={17} lineHeight={25.5}>
                {Object.keys(earnings)
                    .map((key) => `${earnings[key]} ${key}`)
                    .join('\n')}
            </Typography>
        </Paper>
    );
}
