import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useTheme } from 'styled-components/native';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { useModal } from '~/hooks/useModal';

export function BusinessNotVisibleBanner(): ReactElement | null {
    const theme = useTheme();
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const showAlert = useModal();

    const handleOnPress = () => {
        navigate('BusinessSettingsStack');
        showAlert.close();
    };

    return (
        <TouchableOpacity onPress={showAlert.open}>
            <Paper
                bgColor={theme.palette.accent.shade10}
                flexDirection={'row'}
                px={2}
                py={1}
                alignItems={'center'}
                mb={1}
            >
                <MaterialIcons name="business-center" size={24} color={theme.palette.accent.main} />
                <HStack flex alignItems={'center'} justifyContent={'space-between'}>
                    <VStack ml={2}>
                        <Typography fontWeight={500} variant={'caption1'} color={'accent'}>
                            {t('getMoreClientsBanner')}
                        </Typography>
                    </VStack>
                    <IconButton color={'accent'} size={'large'} disablePadding onPress={showAlert.open}>
                        <MaterialIcons name={'chevron-right'} size={24} />
                    </IconButton>
                </HStack>
            </Paper>
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Success}
                icon={<MaterialIcons name={'business-center'} size={53} color={'accent'} />}
                open={showAlert.isOpen}
                headline={t('getMoreClientsBanner')}
                primaryColor={'accent'}
                subHeadline={t('completeProfileToGetMoreClients')}
                primaryText={t('goToBusinessProfile')}
                onAction={handleOnPress}
                onClose={showAlert.close}
            />
        </TouchableOpacity>
    );
}
