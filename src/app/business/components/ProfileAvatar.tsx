import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement, useMemo } from 'react';
import { useTheme } from 'styled-components/native';
import { Avatar } from '~/components/ui/Avatar';
import { Badge } from '~/components/ui/Badge';
import { useEvent } from '~/hooks/useEvent';
import { useUser } from '~/hooks/useUser';
import { useUserBusiness } from '~/hooks/useUserBusiness';

interface ProfileAvatarProps {
    business?: boolean;
}

export function ProfileAvatar({ business }: ProfileAvatarProps): ReactElement {
    const theme = useTheme();
    const user = useUser();
    const { navigate } = useNavigation();
    const userBusiness = useUserBusiness();
    const handleOnPress = useEvent(() =>
        navigate('ProfilePicturePickerScreen', {
            navigateBack: true,
            forBusiness: business,
        }),
    );

    const { photoURL, displayName } = useMemo<Record<'photoURL' | 'displayName', string | undefined>>(() => {
        if (business) {
            return {
                photoURL: userBusiness.data?.profilePicture,
                displayName: userBusiness.data?.name,
            };
        }

        return {
            photoURL: user?.photoURL,
            displayName: user?.displayName,
        };
    }, [user, userBusiness, business]);

    return (
        <Avatar source={photoURL} name={displayName} size={56} onPress={handleOnPress}>
            <Badge
                absolute
                color={'accent.contrast'}
                icon={<MaterialIcons name="photo-camera" size={8} />}
                placement={{
                    horizontal: 'right',
                    vertical: 'bottom',
                }}
                styles={{
                    root: {
                        backgroundColor: theme.palette.accent.main,
                        borderColor: theme.palette.backgroundSecondary.main,
                        borderRadius: 9,
                        borderWidth: 2,
                        height: 17,
                        width: 17,
                    },
                }}
            />
        </Avatar>
    );
}
