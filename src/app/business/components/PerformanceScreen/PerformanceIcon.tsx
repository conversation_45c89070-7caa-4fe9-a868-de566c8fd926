import { MaterialIcons } from '@expo/vector-icons';
import { tint } from 'polished';
import React, { useMemo } from 'react';
import { useTheme } from 'styled-components/native';
import { Avatar } from '~/components/ui/Avatar';

type PerformanceIconProps = {
    iconName: string;
};

export function PerformanceIcon({ iconName }: PerformanceIconProps): JSX.Element {
    const theme = useTheme();
    const avatarColor = useMemo(() => tint(0.9, theme.mixins.getColor('accent')), [theme.mixins]);

    return (
        <Avatar
            icon={<MaterialIcons name={iconName as any} />}
            iconSize={20}
            iconColor={'accent'}
            size={40}
            bgColor={avatarColor}
        />
    );
}
