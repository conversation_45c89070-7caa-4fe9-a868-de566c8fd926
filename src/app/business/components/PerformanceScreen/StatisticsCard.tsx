import React from 'react';
import { Dimensions } from 'react-native';
import { Grid } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { PerformanceIcon } from './PerformanceIcon';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 56) / 2;

type StatisticsCardProps = {
    iconName: string;
    title: string;
    value: number;
};

export function StatisticsCard({ iconName, title, value }: StatisticsCardProps): JSX.Element {
    return (
        <Grid p={2} bgColor={'backgroundPrimary'} mr={1} mb={1} borderRadius={12} width={CARD_WIDTH}>
            <PerformanceIcon iconName={iconName} />
            <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'} mt={1}>
                {title}
            </Typography>
            <Typography variant={'title1'} fontWeight={700} fontSize={20}>
                {value}
            </Typography>
        </Grid>
    );
}
