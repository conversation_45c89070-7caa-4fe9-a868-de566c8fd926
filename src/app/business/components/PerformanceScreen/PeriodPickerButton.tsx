import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { useTheme } from 'styled-components/native';
import { formatDate } from '@bookr-technologies/core';
import { FormFieldBox, FormFieldBoxProps } from '~/components/ui/FormFieldBox';
import { Grid } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';

const DATE_FORMAT = 'DD MMMM';

interface PeriodPickerButtonProps extends FormFieldBoxProps {
    endDate: string;
    startDate: string;
}

export function PeriodPickerButton({ startDate, endDate, onPress, ...rest }: PeriodPickerButtonProps): JSX.Element {
    const theme = useTheme();

    return (
        <FormFieldBox onPress={onPress} flexWrap={'nowrap'} px={2} {...rest}>
            <MaterialIcons name={'event-note'} color={theme.palette.contentSecondary.main} size={24} />
            <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'} ml={1.5}>
                {formatDate(startDate, DATE_FORMAT)}
            </Typography>
            <Grid px={0.5}>
                <MaterialIcons name={'chevron-right'} color={theme.palette.contentSecondary.main} size={20} />
            </Grid>
            <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                {formatDate(endDate, DATE_FORMAT)}
            </Typography>
        </FormFieldBox>
    );
}
