import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessStats, StaffStat } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { Grid } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { StatisticsCard } from './StatisticsCard';

type PerformanceIconProps = {
    businessStats: BusinessStats | undefined;
    staffStats: StaffStat | undefined;
};

export function AppointmentUserStatistics({ staffStats, businessStats }: PerformanceIconProps): JSX.Element {
    const { t } = useTranslation();
    const stats = staffStats ?? businessStats;

    if (!stats) {
        return <React.Fragment />;
    }

    return (
        <Grid>
            <Typography variant={'footnote'} fontWeight={500} mb={0.5} color={'textSecondary'}>
                {t('statistics')}
            </Typography>
            <Grid flexDirection={'row'} mr={2} flexWrap={'wrap'}>
                <StatisticsCard
                    iconName={'event-note'}
                    title={t('performanceStats.totalAppointments')}
                    value={stats.appointments}
                />
                <StatisticsCard
                    iconName={'fact-check'}
                    title={t('performanceStats.finalizedAppointments')}
                    value={stats.finished}
                />
                <StatisticsCard
                    iconName={'people-alt'}
                    title={t('performanceStats.totalClients')}
                    value={stats.clients}
                />
                <StatisticsCard
                    iconName={'event-busy'}
                    title={t('performanceStats.cancelledAppointments')}
                    value={stats?.cancelled}
                />
            </Grid>
        </Grid>
    );
}
