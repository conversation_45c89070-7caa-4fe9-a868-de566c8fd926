import React from 'react';
import { useTranslation } from 'react-i18next';
import { HStack, VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { PerformanceIcon } from './PerformanceIcon';

type TotalVisitsCardProps = {
    period: string;
    value: number | undefined;
};

export function TotalVisitsCard({ period, value }: TotalVisitsCardProps): JSX.Element | null {
    const { t } = useTranslation();

    if (!value) {
        return null;
    }

    return (
        <Paper bgColor={'backgroundPrimary'} p={2} mx={3} mt={2}>
            <HStack>
                <PerformanceIcon iconName={'supervised-user-circle'} />
                <VStack justifyContent={'center'} pl={2} flex>
                    <HStack justifyContent={'space-between'}>
                        <Typography variant={'subhead'} fontWeight={500} mb={0.5}>
                            {t('totalVisits')}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={700}>
                            {value}
                        </Typography>
                    </HStack>
                    <Typography variant={'caption2'} fontWeight={500} color={'textSecondary'}>
                        {period}
                    </Typography>
                </VStack>
            </HStack>
        </Paper>
    );
}
