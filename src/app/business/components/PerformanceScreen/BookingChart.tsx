import React from 'react';
import { useTranslation } from 'react-i18next';
import Svg from 'react-native-svg';
import { useTheme } from 'styled-components/native';
import { VictoryPie, VictoryAnimation, VictoryLabel } from 'victory-native';
import { BookingStats } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';
import { Grid } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';

type BookingChartProps = {
    bookingStats: BookingStats | undefined;
};

const useStyles = makeStyles(({ theme }) => ({
    textStyle: {
        fontSize: Number(theme.typography.variants.title1.fontSize),
        color: theme.palette.typography.textPrimary,
    },
}));

export function BookingChart({ bookingStats }: BookingChartProps): JSX.Element | null {
    const theme = useTheme();
    const styles = useStyles();
    const { t } = useTranslation();

    if (!bookingStats) {
        return null;
    }

    const hours = bookingStats.totalWorkingMinutes / 60;
    const hoursWorked = bookingStats.totalBookedMinutes / 60;

    return (
        <Grid mx={3}>
            <Typography variant={'footnote'} fontWeight={500} mb={0.5} color={'textSecondary'}>
                {t('performanceStats.reservedTime')}
            </Typography>
            <Paper bgColor={'backgroundPrimary'} p={3} fullWidth>
                <Grid mb={2}>
                    <Typography variant={'caption2'} fontWeight={500} color={'textSecondary'}>
                        {t('performanceStats.totalTime')}
                    </Typography>
                    <Typography variant={'title2'} fontWeight={700}>
                        {t('performanceStats.hours', { hours: formatNumber(hours) })}
                    </Typography>
                </Grid>
                <Grid alignItems={'center'} fullWidth>
                    <Svg width={165} height={165}>
                        <VictoryPie
                            radius={65}
                            labels={(): null => null}
                            innerRadius={165 / 2}
                            endAngle={360}
                            animate={true}
                            width={165}
                            height={165}
                            data={[
                                { key: '', y: bookingStats.percentage },
                                { key: '', y: 100 - bookingStats.percentage },
                            ]}
                            colorScale={[theme.palette.accent.main, theme.palette.backgroundTertiary.main]}
                        />
                        <VictoryAnimation data={[{ percent: bookingStats.percentage }]}>
                            {(props): JSX.Element => {
                                return (
                                    <VictoryLabel
                                        textAnchor={'middle'}
                                        verticalAnchor={'middle'}
                                        x={165 / 2}
                                        y={165 / 2}
                                        // eslint-disable-next-line react/prop-types
                                        text={`${Math.round(props.percent as number)}%`}
                                        style={styles.textStyle}
                                    />
                                );
                            }}
                        </VictoryAnimation>
                    </Svg>
                    <Grid flexDirection={'row'} mt={3} justifyContent={'space-between'}>
                        <Grid alignItems={'center'} flex fullWidth>
                            <Grid bgColor={'accent'} width={10} height={10} borderRadius={5} mb={0.5} />
                            <Typography
                                variant={'caption1'}
                                fontWeight={500}
                                color={'textSecondary'}
                                textAlign={'center'}
                            >
                                {`${hoursWorked.toFixed(2)}\n${t('performanceStats.workedHours')}`}
                            </Typography>
                        </Grid>
                        <Grid alignItems={'center'} flex fullWidth>
                            <Grid
                                bgColor={theme.palette.backgroundTertiary.main}
                                width={10}
                                height={10}
                                borderRadius={5}
                                mb={0.5}
                            />
                            <Typography
                                variant={'caption1'}
                                fontWeight={500}
                                color={'textSecondary'}
                                textAlign={'center'}
                            >
                                {`${(hours - hoursWorked).toFixed(2)}\n${t('performanceStats.lostHours')}`}
                            </Typography>
                        </Grid>
                    </Grid>
                </Grid>
            </Paper>
        </Grid>
    );
}
