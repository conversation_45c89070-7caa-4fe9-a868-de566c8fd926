import moment from 'moment';
import { Moment } from 'moment-timezone';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View } from 'react-native';
import { Defs, LinearGradient, Stop } from 'react-native-svg';
import { useTheme } from 'styled-components';
import { VictoryAxis, VictoryChart, VictoryLine, VictoryTooltip, VictoryVoronoiContainer } from 'victory-native';
import { BusinessStats, StaffStat, TotalEarning } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { formatDate } from '@bookr-technologies/core';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { Grid, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { Menu } from '~/components/ui/Menu';
import { MenuItem } from '~/components/ui/MenuItem';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useI18n } from '~/hooks/useTranslation';
import { num } from '~/lib/utils/number';

type EarningsChartProps = {
    businessStats: BusinessStats | undefined;
    earnings: Record<
        string,
        {
            date: Moment;
            totals: {
                [key: string]: number;
            };
        }
    >;
    endDate: string;
    staffStats: StaffStat | undefined;
    startDate: string;
};

const useStyles = makeStyles(({ theme }) => ({
    tooltipStyle: {
        backgroundColor: theme.palette.backgroundPrimary.main,
        fontSize: Number(theme.typography.variants.caption1.fontSize),
        color: theme.palette.typography.textPrimary,
    },
}));

function getDatesInRange(startDate: Date, endDate: Date): Date[] {
    const date = moment(startDate).subtract(1, 'day');
    const finalDate = moment(endDate).add(1, 'day');
    const dates = [];

    while (finalDate.diff(date) >= 0) {
        dates.push(date.toDate());
        date.add(1, 'day');
    }

    return dates;
}

function calculateTotalByCurrency(data?: Record<string, number> | null, currency?: string | null): number {
    return Object.entries(data ?? {}).reduce(
        (acc, [key, value]) => (key.toUpperCase() === currency?.toUpperCase() ? acc + value : acc),
        0,
    );
}

export function EarningsChart({
    staffStats,
    businessStats,
    startDate,
    endDate,
    earnings,
}: EarningsChartProps): JSX.Element | null {
    const t = useI18n();
    const theme = useTheme();
    const styles = useStyles();
    const currencies = Object.keys({
        ...(staffStats?.totalSales ?? {}),
        ...(businessStats?.totalSales ?? {}),
    }).sort((a, b) => a.localeCompare(b));

    const detectCurrency = useEvent(() => {
        // determine the default currency
        const stats = staffStats?.totalSales ?? businessStats?.totalSales ?? {};

        const entries = Object.entries(stats).sort(([, a], [, b]) => b - a);
        return entries?.[0]?.[0] ?? 'Lei';
    });

    const [currency, setCurrency] = useState<string>(detectCurrency);
    const currencyModal = useModal();
    const currencyFieldRef = useRef<View>(null);
    const value = staffStats ? staffStats?.totalSales[currency] : businessStats?.totalSales[currency];

    useEffect(() => {
        setCurrency(detectCurrency);
    }, [staffStats, businessStats, detectCurrency]);

    const series = useMemo<TotalEarning[]>(() => {
        const days = moment(endDate).diff(moment(startDate), 'days');
        return new Array(days + 1).fill(0).map((_, i) => {
            const day = moment(startDate).clone().add(i, 'days');
            return {
                currency: currency,
                date: moment(startDate).clone().add(i, 'days').toISOString(),
                amount: calculateTotalByCurrency(earnings[day.format('YYYY-MM-DD')]?.totals, currency),
            };
        });
    }, [endDate, startDate, currency, earnings]);

    const datesArray = getDatesInRange(new Date(startDate), new Date(endDate));
    const chartData = datesArray.map((date) => {
        const earnings = series
            .filter((it) => it.currency?.toUpperCase() === currency?.toUpperCase())
            .find((it) => new Date(it.date).toDateString() === date.toDateString());

        return { x: date, y: earnings?.amount ?? 0 };
    });

    const tickFormat = useEvent((x): number => new Date(x).getDate());

    const handleCurrencyPress = useEvent((item, value) => {
        setCurrency(value.toLowerCase());
    });

    if (!currency) {
        return null;
    }

    return (
        <Grid mt={2} mx={3}>
            <Paper bgColor={'backgroundPrimary'} px={3} pt={3}>
                <Typography variant={'caption2'} fontWeight={500} color={'textSecondary'}>
                    {t('performanceStats.totalEarnings')}
                </Typography>
                <Typography variant={'title1'} fontWeight={700}>
                    {`${formatNumber(num(value))} ${t(`currencies.${currency.toUpperCase()}`)}`}
                </Typography>
                {currencies.length > 1 && (
                    <Grid mt={1} flexDirection={'row'}>
                        <FormFieldBox
                            onPress={currencyModal.open}
                            px={1}
                            flexDirection={'row'}
                            alignItems={'center'}
                            alignContent={'center'}
                            flexWrap={'nowrap'}
                            ref={currencyFieldRef}
                            minHeight={38}
                            minWidth={120}
                        >
                            <Icon name={'monetization-on'} size={20} color={'contentSecondary'} />
                            <VStack pl={1}>
                                <Typography variant={'subhead'} fontWeight={500}>
                                    {t(`currencies.${currency.toUpperCase()}`)}
                                </Typography>
                            </VStack>
                        </FormFieldBox>
                        <Menu
                            selected={currency}
                            onPress={handleCurrencyPress}
                            anchorEl={currencyFieldRef}
                            {...currencyModal.props}
                        >
                            {currencies.map((currency, index) => (
                                <MenuItem value={currency.toUpperCase()} key={index}>
                                    {t(`currencies.${currency.toUpperCase()}`)}
                                </MenuItem>
                            ))}
                        </Menu>
                    </Grid>
                )}
                <VStack fullWidth alignItems={'center'}>
                    <VictoryChart
                        containerComponent={
                            <VictoryVoronoiContainer
                                voronoiDimension={'x'}
                                mouseFollowTooltips
                                labels={({ datum }): string =>
                                    `${formatNumber(datum.y)} ${t(
                                        `currencies.${currency.toUpperCase()}`,
                                    )}\n${formatDate(datum.x, 'll')}`
                                }
                                labelComponent={
                                    <VictoryTooltip
                                        cornerRadius={4}
                                        centerOffset={{ y: -100 }}
                                        flyoutStyle={{
                                            fill: theme.palette.backgroundPrimary.main,
                                            stroke: theme.palette.divider.main,
                                        }}
                                        pointerWidth={1}
                                        style={styles.tooltipStyle}
                                    />
                                }
                            />
                        }
                    >
                        <Defs>
                            <LinearGradient id={'gradient'} x1={'0%'} y1={'0%'} x2={'0%'} y2={'100%'}>
                                <Stop offset={'0%'} stopColor={'rgba(47, 128, 251, 0.6)'} stopOpacity={0.6} />
                                <Stop offset={'80%'} stopColor={'rgba(47, 128, 251, 0)'} stopOpacity={0} />
                            </LinearGradient>
                        </Defs>
                        <VictoryAxis
                            dependentAxis
                            style={{
                                grid: { stroke: '#eee' },
                                axis: { stroke: 'transparent' },
                                tickLabels: { fill: 'transparent' },
                            }}
                        />
                        <VictoryAxis
                            style={{
                                grid: { stroke: 'transparent' },

                                axis: { stroke: 'transparent' },
                                tickLabels: {
                                    fill: theme.palette.contentTertiary.main,
                                },
                            }}
                            crossAxis={false}
                            tickFormat={tickFormat}
                            tickCount={10}
                        />
                        <VictoryLine
                            data={chartData}
                            animate={true}
                            style={{
                                data: { stroke: theme.palette.accent.main, strokeWidth: 3, fill: 'url(#gradient)' },
                            }}
                        />
                    </VictoryChart>
                </VStack>
            </Paper>
        </Grid>
    );
}
