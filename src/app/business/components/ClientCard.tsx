import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useTheme } from 'styled-components';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { BusinessApplicationNavigationProp } from '~/RoutesParams';
import { Avatar } from '~/components/ui/Avatar';
import { GridProps, HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';

interface ClientCardProps extends GridProps {
    client: UserModel;
    showAvatar?: boolean;
}

export function ClientCard({ client, showAvatar = true, ...rest }: ClientCardProps): ReactElement {
    const theme = useTheme();
    const { t } = useTranslation();
    const { navigate } = useNavigation<BusinessApplicationNavigationProp<'ClientsScreen'>>();

    return (
        <TouchableOpacity onPress={() => navigate('ClientDetailsScreen', { clientId: client.uid })}>
            <HStack {...rest}>
                {showAvatar && <Avatar size={40} source={client.photoURL} name={client.displayName} />}
                <VStack ml={2}>
                    <Typography variant={'callout'} fontWeight={500} fontSize={16}>
                        {client.displayName}
                    </Typography>
                    <Typography
                        variant={'caption1'}
                        fontWeight={500}
                        fontSize={12}
                        color={theme.palette.typography.disabled}
                    >
                        {client.phoneNumber || client.email || t('noContactInformation')}
                    </Typography>
                </VStack>
            </HStack>
        </TouchableOpacity>
    );
}
