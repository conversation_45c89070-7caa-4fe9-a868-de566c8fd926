import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, ImageSourcePropType, TouchableOpacity } from 'react-native';
import { Badge } from '~/components/ui/Badge';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Spacer } from '~/components/ui/Spacer';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';

interface Props {
    caption: string;
    headline: string;
    image: ImageSourcePropType;
    value: string;
    onPress(value: string): void;
}

export function AvailabilitySettingsCard({ caption, headline, image, onPress, value }: Props): ReactElement {
    const { t } = useTranslation();
    const styles = useStyles();

    const handlePress = useEvent(() => onPress(value));

    return (
        <TouchableOpacity onPress={handlePress} activeOpacity={0.8} style={styles.root}>
            <Paper bgColor={'backgroundPrimary'} p={2} justifyContent={'space-between'} direction={'row'}>
                <VStack alignItems={'flex-start'} flex maxWidth={140}>
                    <Typography variant={'caption1'} fontWeight={500} mb={0.5} color={'textSecondary'}>
                        {caption}
                    </Typography>
                    <Typography variant={'body'} fontWeight={700}>
                        {headline}
                    </Typography>
                    <Spacer />
                    <Badge title={t('edit')} />
                </VStack>
                <Image source={image} style={styles.image} />
            </Paper>
        </TouchableOpacity>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        marginBottom: theme.mixins.spacingValue(2),
    },
    image: {
        width: 100,
        height: 100,
    },
}));
