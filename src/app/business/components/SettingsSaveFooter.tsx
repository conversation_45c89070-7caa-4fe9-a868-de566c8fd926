import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, ButtonProps, FormikButton, FormikButtonProps } from '~/components/ui/Button';
import { Container } from '~/components/ui/Grid';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';

type Props =
    | Partial<
          ButtonProps & {
              disableFormik?: true;
          }
      >
    | Partial<
          FormikButtonProps & {
              disableFormik?: false | never;
          }
      >;

export function SettingsSaveFooter({ disableFormik, label, ...rest }: Props): ReactElement {
    const insets = useSafeAreaInsets(true);
    const { t } = useTranslation();

    const Component = disableFormik ? Button : FormikButton;

    return (
        <Container bgColor={'backgroundPrimary'} pt={2} pb={insets.bottom}>
            <Component label={label ?? t('saveChanges')} size={'large'} {...rest} />
        </Container>
    );
}
