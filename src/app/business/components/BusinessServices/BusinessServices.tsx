import { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DraggableFlatList, { DragEndParams } from 'react-native-draggable-flatlist';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { ServiceRow } from '~/app/business/components/BusinessServices/ServiceRow';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useWatchValue } from '~/hooks/useWatch';

interface Props {
    items: ServiceModel[];
    loading?: boolean;

    onReload(): Promise<void>;
    onOrder(records: ServiceModel[]): void;
}

export function BusinessServices({ items, loading, onOrder, onReload }: Props): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const refreshControl = useRefreshControl(onReload);

    const [services, setServices] = useState(items);
    const listItems = useMemo(() => services.sort(ServiceModel.sort), [services]);

    const handleOnDragEnd = useEvent(({ data }: DragEndParams<ServiceModel>) => {
        setServices(
            data.map((item, index) => {
                item.serviceRank = index;
                return item;
            }),
        );

        onOrder(data);
    });

    useWatchValue(items, setServices);

    if (loading) {
        return (
            <VStack alignItems={'center'} p={4}>
                <CircularProgress />
            </VStack>
        );
    }

    if (!listItems?.length) {
        return (
            <VStack scrollable flex scrollViewProps={{ refreshControl }} py={3}>
                <Typography
                    variant={'caption2'}
                    color={'textSecondary'}
                    fontWeight={600}
                    textAlign={'center'}
                    fullWidth
                >
                    {t('weCouldNotFindAnyServices')}
                </Typography>
            </VStack>
        );
    }

    return (
        <DraggableFlatList
            refreshControl={refreshControl}
            data={listItems}
            containerStyle={styles.container}
            keyExtractor={(item): string => `draggable-item-${item.id}`}
            onDragEnd={handleOnDragEnd}
            renderItem={({ item, getIndex, isActive, drag }): ReactElement => (
                <ServiceRow item={item} index={getIndex()} active={isActive} drag={drag} />
            )}
        />
    );
}

const useStyles = makeStyles(() => ({
    container: {
        flex: 1,
    },
}));
