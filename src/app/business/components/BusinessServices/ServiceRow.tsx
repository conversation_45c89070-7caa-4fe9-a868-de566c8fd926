import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable } from 'react-native';
import { useTheme } from 'styled-components/native';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { BusinessApplicationSettingsStackNavigationProp } from '~/RoutesParams';
import { Divider } from '~/components/ui/Divider';
import { HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { usePressable } from '~/hooks/usePressable';

interface ServiceRowParams {
    active: boolean;
    drag: () => void;
    index: number | undefined;
    item: ServiceModel;
}

export function ServiceRow({ drag, active, item }: ServiceRowParams): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const theme = useTheme();
    const { navigate } = useNavigation<BusinessApplicationSettingsStackNavigationProp<'SettingsServicesScreen'>>();
    const [isPressed, pressableProps] = usePressable();

    const handlePress = useEvent(() =>
        navigate('SettingsServiceScreen', {
            service: item,
        }),
    );

    return (
        <>
            <Pressable onLongPress={drag} onPress={handlePress} {...pressableProps}>
                <HStack
                    style={sx(styles.root, isPressed && styles.pressed, active && styles.active)}
                    alignItems={'center'}
                    py={1.5}
                    px={3}
                >
                    <MaterialIcons name={'drag-indicator'} size={20} color={theme.palette.primary.shade30} />

                    <VStack pl={2} flex>
                        <Typography variant={'callout'} fontWeight={500} mb={0.5}>
                            {item.name}
                        </Typography>
                        <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                            {t('serviceDurationAndPrice', { item })}
                        </Typography>
                    </VStack>

                    <MaterialIcons name="keyboard-arrow-right" size={24} color={theme.palette.accent.main} />
                </HStack>
            </Pressable>
            <Divider my={0} color={isPressed || active ? 'transparent' : 'divider'} />
        </>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {},
    pressed: {
        backgroundColor: theme.palette.backgroundSecondary.main,
    },
    active: {
        backgroundColor: theme.palette.backgroundTertiary.main,
    },
}));
