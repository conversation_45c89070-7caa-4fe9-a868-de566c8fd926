import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { HStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';

interface ContactDetailsProps {
    email?: string;
    phoneNumber?: string;
}

export function ContactDetails({ phoneNumber, email }: ContactDetailsProps): ReactElement | null {
    const { t } = useTranslation();
    if (!phoneNumber && !email) {
        return null;
    }

    return (
        <Paper bgColor={'backgroundPrimary'} p={2} label={t('contactDetails')} width={'100%'} mb={1}>
            {phoneNumber ? (
                <HStack width={'100%'} alignItems={'center'}>
                    <Typography variant={'footnote'} fontWeight={700}>
                        {t('phoneNumber') as string}:
                    </Typography>
                    <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} pl={0.5}>
                        {phoneNumber}
                    </Typography>
                </HStack>
            ) : null}
            {email ? (
                <HStack width={'100%'} alignItems={'center'} mt={phoneNumber ? 1 : 0}>
                    <Typography variant={'footnote'} fontWeight={700}>
                        {t('email')}:
                    </Typography>
                    <Typography variant={'footnote'} color={'textSecondary'} fontWeight={500} pl={0.5}>
                        {email}
                    </Typography>
                </HStack>
            ) : null}
        </Paper>
    );
}
