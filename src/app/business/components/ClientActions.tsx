import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import Button from '~/components/ui/Button';
import { HStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { getNearestDatetime } from '~/components/utils/time';
import { useEvent } from '~/hooks/useEvent';

export function ClientActions({
    client,
    onShowMorePress,
}: {
    client: UserModel;
    onShowMorePress: () => void;
}): ReactElement {
    const { t } = useTranslation();
    const { navigate } = useNavigation();

    const handleProgramPress = useEvent(() => {
        navigate('CreateAppointmentScreen', {
            appointment: { client },
            date: getNearestDatetime().toISOString(),
        });
    });

    const handleCallPress = useEvent(() => {
        Linking.openURL(`tel:${client.phoneNumber}`);
    });

    return (
        <HStack pt={1.65} mx={-0.5}>
            <Button
                color={'accent'}
                size={'small'}
                variant={'contained'}
                label={t('makeAppointment')}
                onPress={handleProgramPress}
                mx={0.5}
                mt={1}
                style={{ flexGrow: 1 }}
                startIcon={<Icon name={'event-note'} />}
            />
            <Button
                color={'accent'}
                size={'small'}
                variant={'subtle'}
                label={t('call')}
                onPress={handleCallPress}
                mx={0.5}
                mt={1}
                style={{ flexGrow: 1 }}
                startIcon={<Icon name={'phone-in-talk'} />}
            />
            <IconButton color={'accent'} size={'small'} variant={'subtle'} onPress={onShowMorePress} mt={1} mx={0.5}>
                <Icon name={'more-horiz'} />
            </IconButton>
        </HStack>
    );
}
