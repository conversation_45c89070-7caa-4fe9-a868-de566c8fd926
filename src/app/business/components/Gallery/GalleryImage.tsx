import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useEffect } from 'react';
import { ImageBackground, ImageStyle, LayoutRectangle, Pressable, PressableProps } from 'react-native';
import { CacheManager } from 'react-native-expo-image-cache';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { IconButton } from '~/components/ui/IconButton';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useLayout } from '~/hooks/useLayout';
import { useLoading } from '~/hooks/useLoading';
import { usePressable } from '~/hooks/usePressable';
import { useSafeState } from '~/hooks/useSafeState';

interface Props extends Omit<PressableProps, 'onPress'> {
    image: string;
    loading: boolean;
    onDelete: (image: string) => void;
    onPress: (image: string) => void;
}

export function GalleryImage({ image, loading, onDelete, onPress, ...rest }: Props): ReactElement {
    const layout = useLayout();
    const loader = useLoading();
    const [isPressed, pressableProps] = usePressable(rest);
    const [uri, setUri] = useSafeState<string | undefined>();
    const styles = useStyles({ isPressed, layout, loading: loading || loader.any() });

    const handleDelete = useEvent(() => onDelete(image));
    const handlePress = useEvent(() => onPress(image));

    useEffect(() => {
        loader.from(async () => {
            const path = await CacheManager.get(image, {}).getPath();
            if (path) {
                setUri(path);
            }
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [image]);

    return (
        <Pressable style={styles.root} onLayout={layout.handler} onPress={handlePress} {...rest} {...pressableProps}>
            <ImageBackground source={{ uri }} style={styles.image as ImageStyle}>
                <IconButton
                    variant={'contained'}
                    size={'xsmall'}
                    m={1}
                    color={'secondary'}
                    iconColor={'contentSecondary'}
                    activeOpacity={0.9}
                    onPress={handleDelete}
                >
                    {loading || loader.any() ? (
                        <CircularProgress size={20} thickness={2} />
                    ) : (
                        <MaterialIcons name={'delete'} />
                    )}
                </IconButton>
            </ImageBackground>
        </Pressable>
    );
}

type GalleryImageStyles = Styles<'root' | 'image'>;

// noinspection JSSuspiciousNameCombination
const useStyles = makeStyles<
    {
        isPressed: boolean;
        layout: LayoutRectangle;
        loading: boolean;
    },
    GalleryImageStyles
>(({ isPressed, layout, loading, theme }) => ({
    root: {
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderRadius: 8,
        height: layout.width,
        overflow: 'hidden',
        opacity: isPressed || loading ? 0.7 : 1,
    },
    image: {
        width: layout.width,
        height: layout.height,
        alignItems: 'flex-end',
    },
}));
