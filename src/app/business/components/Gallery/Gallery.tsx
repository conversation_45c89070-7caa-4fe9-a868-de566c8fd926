import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation } from 'react-native';
import { GalleryImage } from '~/app/business/components/Gallery/GalleryImage';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { LightboxModal } from '~/components/LightboxModal';
import { Grid, HStack } from '~/components/ui/Grid';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';
import { useModal } from '~/hooks/useModal';

interface Props {
    images: string[];
    onDelete: (image: string) => void | Promise<void>;
}

export function Gallery({ images, onDelete }: Props): ReactElement {
    const { t } = useTranslation();
    const loading = useLoading<string>();
    const showAlert = useModal();
    const showPreview = useModal();

    const handleDelete = useEvent(async () => {
        const image = showAlert.context;
        await loading.from(image, onDelete(image));
        LayoutAnimation.easeInEaseOut();
    });

    return (
        <>
            <HStack>
                {images.map((image) => (
                    <Grid key={image} xs={4} p={0.75}>
                        <GalleryImage
                            image={image}
                            loading={loading.isLoading(image)}
                            onDelete={showAlert.openWithContext}
                            onPress={showPreview.openWithContext}
                        />
                    </Grid>
                ))}
            </HStack>

            <LightboxModal
                images={images.map((source) => ({ source }))}
                initialIndex={images.indexOf(showPreview.context)}
                {...showPreview.props}
            />

            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                headline={t('doYouWantToDeleteThisImage')}
                primaryText={t('delete')}
                secondaryText={t('iHaveChangedMyMind')}
                onCancel={showAlert.close}
                onAction={handleDelete}
                icon={<MaterialIcons name={'delete-forever'} />}
                snapPoints={[400]}
                {...showAlert.props}
            />
        </>
    );
}
