import { VStack } from '~/components/ui/Grid';
import { Paper, PaperProps } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';

interface StatsCardProps extends PaperProps {
    title: string;
    value: number;
}

export function StatsCard({ value, title, ...rest }: StatsCardProps) {
    return (
        <Paper direction={'row'} bgColor={'backgroundPrimary'} py={2} px={1} flexGrow {...rest}>
            <VStack alignItems={'center'} justifyContent={'center'} flexGrow>
                <Typography variant={'title3'} fontWeight={700} fontSize={17} lineHeight={25.2}>
                    {value}
                </Typography>
                <Typography
                    variant={'caption1'}
                    color={'textSecondary'}
                    fontWeight={500}
                    fontSize={12}
                    lineHeight={15.12}
                    textAlign={'center'}
                >
                    {title}
                </Typography>
            </VStack>
        </Paper>
    );
}
