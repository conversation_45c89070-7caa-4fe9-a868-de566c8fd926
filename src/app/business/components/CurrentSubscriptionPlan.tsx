import moment from 'moment';
import { ReactElement, useMemo } from 'react';
import { Image } from 'react-native';
import { SubscriptionModel } from '@bookr-technologies/api/models/SubscriptionModel';
import { Badge } from '~/components/ui/Badge';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useI18n } from '~/hooks/useTranslation';

interface Props {
    subscription: SubscriptionModel;
}

export function CurrentSubscriptionPlan({ subscription }: Props): ReactElement {
    const t = useI18n();
    const styles = useStyles();

    const isTrialActive = useMemo(() => moment().isBefore(moment(subscription.trialEnd)), [subscription.trialEnd]);

    return (
        <Paper
            bgColor={'backgroundPrimary'}
            direction={'row'}
            justifyContent={'space-between'}
            flexWrap={'nowrap'}
            overflow={'hidden'}
        >
            <VStack justifyContent={'space-between'} alignItems={'flex-start'} p={2} flex>
                <VStack>
                    <Typography variant={'title3'} fontWeight={700} mb={0.5}>
                        {t(`subscriptionPlans.${subscription.subscriptionPlan.toLowerCase()}.name`)}
                    </Typography>
                    <Typography variant={'caption1'} fontWeight={500} color={'disabled'} mb={2}>
                        {t(`subscriptionPlans.${subscription.subscriptionPlan.toLowerCase()}.description`)}
                    </Typography>
                </VStack>
                {isTrialActive && <Badge title={t('trialPeriod')} />}
            </VStack>
            <Image source={require('~/assets/screenBackgrounds/currentSubscriptionPlan.png')} style={styles.image} />
        </Paper>
    );
}

const useStyles = makeStyles(() => ({
    root: {},
    image: {
        width: 150,
        height: 150,
    },
}));
