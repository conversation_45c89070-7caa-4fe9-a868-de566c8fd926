import { Contact } from 'expo-contacts';
import React, { ReactElement } from 'react';
import { Pressable } from 'react-native';
import { Checkbox } from '~/components/ui/Checkbox';
import { HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { ImportContact } from '../screens/ContactsListPage';

export function ImportClientRow({
    contactsToImport,
    contactDetails,
    onPress,
}: {
    contactDetails: Contact;
    onPress(contact: ImportContact, isSelected: boolean): void;
    contactsToImport: ImportContact[];
}): ReactElement {
    const displayName = [contactDetails.lastName, contactDetails.firstName].filter(Boolean).join(' ');
    const phoneNumber = contactDetails.phoneNumbers?.[0].number;
    const contact = { displayName, phoneNumber } as ImportContact;
    const isSelected = !!contactsToImport.find((it) => it.displayName === displayName);

    const handleContactPress = useEvent(() => {
        onPress(contact, isSelected);
    });

    return (
        <Pressable onPress={handleContactPress}>
            <HStack
                py={2}
                pl={2}
                pr={4}
                fullWidth
                alignItems={'center'}
                justifyContent={'space-between'}
                bgColor={isSelected ? 'accent.shade10' : 'transparent'}
                borderRadius={12}
            >
                <VStack>
                    <Typography variant={'callout'} fontWeight={500}>
                        {contactDetails.lastName} {contactDetails.firstName}
                    </Typography>
                    <Typography variant={'caption1'} color={'textSecondary'} fontWeight={500}>
                        {contactDetails.phoneNumbers?.[0].number}
                    </Typography>
                </VStack>
                <Checkbox checked={isSelected} onPress={handleContactPress} noFeedback />
            </HStack>
        </Pressable>
    );
}
