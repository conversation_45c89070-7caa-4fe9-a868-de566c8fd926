import { ReactElement } from 'react';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SubscriptionBillingModel } from '@bookr-technologies/api/models/SubscriptionBillingModel';
import { formatDate } from '@bookr-technologies/core';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { useI18n } from '~/hooks/useTranslation';

export function BillingHistory(): ReactElement {
    const t = useI18n();
    const billings = useQuery('business/subscription/billings', () => businessEndpoint.getSubscriptionBillings());

    return (
        <VStack my={3}>
            <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'} mb={0.75}>
                {t('billingHistory')}
            </Typography>
            <Paper bgColor={'backgroundPrimary'} py={0.5} flexWrap={'nowrap'}>
                {billings.isLoading ? (
                    <VStack alignItems={'center'} justifyContent={'center'} py={2}>
                        <CircularProgress />
                    </VStack>
                ) : null}

                {!billings.isLoading && !billings.data?.items?.length && (
                    <Typography variant="subhead" fontWeight={500} color="textSecondary" p={4} textAlign="center">
                        {t('noBillingHistory')}
                    </Typography>
                )}

                {billings.data?.items?.map?.((item) => (
                    <HStack key={item.id} py={1.5} px={2} justifyContent={'space-between'} alignItems={'center'}>
                        <VStack>
                            <Typography variant={'callout'} fontWeight={500}>
                                {item.subscriptionPlan
                                    ? t(`subscriptionPlans.${item.subscriptionPlan?.toLowerCase()}.name`)
                                    : t('unknown')}
                            </Typography>
                            <Typography variant={'caption1'} fontWeight={500} color={'disabled'}>
                                {formatDate(item.startsAt, 'LL [•] LT')}
                            </Typography>
                        </VStack>

                        <Typography variant={'callout'} fontWeight={500}>
                            {SubscriptionBillingModel.getPrice(item)}
                        </Typography>
                    </HStack>
                ))}
            </Paper>
        </VStack>
    );
}
