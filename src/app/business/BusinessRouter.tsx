import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components/native';
import { SettingsScreen } from '~/app/business/screens/SettingsScreen';
import { BottomSheet } from '~/components/BottomSheet';
import { Badge } from '~/components/ui/Badge';
import { Button } from '~/components/ui/Button';
import { Container, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Typography } from '~/components/ui/Typography';
import { useModal } from '~/hooks/useModal';
import { tabBarOptions } from '~/lib/utils/navigation';
import { AppointmentsScreen } from './screens/AppointmentsScreen';
import { ClientsScreen } from './screens/ClientsScreen';
import { PerformanceScreen } from './screens/PerformanceScreen';

const TabNavigator = createBottomTabNavigator();
const { Navigator, Screen } = TabNavigator;

function CustomTabBarButton() {
    const { navigate } = useNavigation();
    const { t } = useTranslation();
    const modal = useModal();
    const theme = useTheme();

    const handleOnPress = (screenName: string) => {
        modal.close();
        navigate(screenName as any);
    };

    return (
        <VStack justifyContent={'center'} alignItems={'center'}>
            <IconButton size={'small'} variant={'contained'} color={'accent'} borderRadius={999} onPress={modal.open}>
                <MaterialCommunityIcons name={'plus'} size={24} />
            </IconButton>
            <BottomSheet snapPoints={['35%']} {...modal.props}>
                <Container flex safeBottom>
                    <Typography
                        textAlign={'center'}
                        color={'contentTertiary'}
                        variant={'footnote'}
                        fontWeight={500}
                        mb={2}
                    >
                        {t('shortcuts')}
                    </Typography>
                    <Button
                        size={'large'}
                        label={t('createAppointmentScreen.headline')}
                        onPress={() => handleOnPress('CreateAppointmentScreen')}
                        variant={'subtle'}
                        position={'relative'}
                        align={'flex-start'}
                        style={{
                            backgroundColor: theme.palette.backgroundSecondary.main,
                        }}
                        startIcon={
                            <MaterialCommunityIcons
                                name={'calendar-plus'}
                                size={20}
                                color={theme.palette.accent.main}
                            />
                        }
                    />
                    <Button
                        size={'large'}
                        my={1}
                        label={t('createBreakScreen.headline')}
                        onPress={() => handleOnPress('CreateBreakScreen')}
                        variant={'subtle'}
                        position={'relative'}
                        align={'flex-start'}
                        style={{
                            backgroundColor: theme.palette.backgroundSecondary.main,
                        }}
                        startIcon={
                            <MaterialCommunityIcons
                                name={'coffee-outline'}
                                size={20}
                                color={theme.palette.accent.main}
                            />
                        }
                    />
                    <Button
                        size={'large'}
                        label={t('sendANotification')}
                        onPress={() => handleOnPress('SendNotificationSelectClientsScreen')}
                        variant={'subtle'}
                        position={'relative'}
                        align={'flex-start'}
                        style={{
                            backgroundColor: theme.palette.backgroundSecondary.main,
                        }}
                        startIcon={
                            <MaterialCommunityIcons name={'bell-outline'} size={20} color={theme.palette.accent.main} />
                        }
                        endIcon={
                            <Badge
                                title={'BETA'}
                                color={'accent'}
                                style={{
                                    backgroundColor: theme.palette.accent.main,
                                    color: theme.palette.secondary.main,
                                }}
                            />
                        }
                    />
                </Container>
            </BottomSheet>
        </VStack>
    );
}

function NullComponent() {
    return null;
}

export function BusinessRouter(): ReactElement {
    const { t } = useTranslation();

    return (
        <Navigator>
            <Screen
                name={'AppointmentsScreen'}
                component={AppointmentsScreen}
                options={tabBarOptions(t('appointments'), 'event-note')}
            />
            <Screen
                name={'PerformanceScreen'}
                component={PerformanceScreen}
                options={tabBarOptions(t('performance'), 'insert-chart')}
            />
            <Screen
                name="ActionScreen"
                component={NullComponent}
                options={{
                    tabBarButton: CustomTabBarButton,
                }}
            />
            <Screen name={'ClientsScreen'} component={ClientsScreen} options={tabBarOptions(t('clients'), 'people')} />
            <Screen
                name={'SettingsScreen'}
                component={SettingsScreen}
                options={tabBarOptions(t('settings'), 'settings')}
            />
        </Navigator>
    );
}
