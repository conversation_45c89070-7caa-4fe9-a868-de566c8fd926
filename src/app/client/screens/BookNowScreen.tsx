import { useNavigation, useRoute } from '@react-navigation/native';
import { ReactElement } from 'react';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { BookNowComponent } from '~/app/client/components/BookNowComponent';

interface BookNowScreenParams {
    business: BusinessModel;
    service: ServiceModel;
    staff: UserModel;
}

export function BookNowScreen(): ReactElement {
    const route = useRoute();
    const { navigate } = useNavigation();
    const { service, staff, business } = route.params as BookNowScreenParams;

    return <BookNowComponent navigate={navigate} business={business} service={service} staff={staff} />;
}
