import { useNavigation } from '@react-navigation/native';
import React, { ReactElement, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ListRenderItem, SectionList } from 'react-native';
import { useQuery } from 'react-query';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { Loader } from '~/app/common/components/Loader';
import { SignInScreen } from '~/app/common/screens/SignInScreen';
import emptyAppointments from '~/assets/emptyBackgrounds/emptyAppointments.png';
import { AppointmentCard } from '~/components/AppointmentCard';
import { EmptyStateCard } from '~/components/EmptyStateCard';
import { Badge } from '~/components/ui/Badge';
import { Button } from '~/components/ui/Button';
import { Container, Grid, HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useUser } from '~/hooks/useUser';
import { reactQueryClient } from '~/lib/clients/reactQuery';
import { getAppointments } from '~/lib/utils/appointments';
import { navigate } from '~/lib/utils/navigation';
import { useAuthStore } from '~/store/useAuthStore';

const emptyAppointmentsList = getAppointments([]);

export function MyAppointmentsScreen(): ReactElement {
    const { t } = useTranslation();
    const user = useUser();
    const containerPadding = useContainerPadding();
    const { addListener, removeListener } = useNavigation();
    const isGuest = useAuthStore((state) => state.isGuest);
    const {
        data,
        isLoading: isAppointmentsLoading,
        refetch,
    } = useQuery(
        ['appointments', user?.uid],
        async () => {
            if (!user?.uid) {
                return emptyAppointmentsList;
            }

            const { appointmentsAsClient } = await usersEndpoint.show(user.uid, {
                params: {
                    includeAppointmentsAsClient: true,
                },
            });

            return appointmentsAsClient ? getAppointments(appointmentsAsClient) : emptyAppointmentsList;
        },
        {
            enabled: !!user?.uid && !isGuest,
            initialData: () => reactQueryClient.getQueryData(['appointments', user?.uid]),
        },
    );

    const appointments = data ?? emptyAppointmentsList;
    const sections = useMemo(
        () => [
            { data: appointments.FUTURE, key: 'FUTURE', title: t('active') },
            ...(appointments.PAST.length > 0 ? [{ data: appointments.PAST, key: 'PAST', title: t('history') }] : []),
        ],
        [appointments.FUTURE, appointments.PAST, t],
    );

    const isLoading = isAppointmentsLoading || !user?.uid;
    const isEmpty = useMemo(() => appointments.PAST.length === 0 && appointments.FUTURE.length === 0, [appointments]);
    const refreshControl = useRefreshControl(refetch);
    const handleExploreBusinesses = useEvent(() => navigate('SearchMap'));
    const handleFocus = useEvent(() => refetch());

    const renderItem: ListRenderItem<AppointmentModel> = ({ item }) => {
        const client = item.appointmentClients.find((ac) => ac.client.uid === user?.uid)?.client;
        return <AppointmentCard mb={1} mx={containerPadding} appointment={item} client={client} />;
    };

    useEffect(() => {
        addListener('focus', handleFocus);

        return () => removeListener('focus', () => console.log('removed'));
    }, [addListener, handleFocus, removeListener]);

    if (isGuest) {
        return <SignInScreen showGuestOption={false} />;
    }

    return (
        <Screen bgColor={'backgroundSecondary'} disableScroll disablePadding>
            <ScreenHeader
                px={containerPadding}
                headline={t('myAppointments')}
                bgColor={'transparent'}
                headlineTypographyProps={{ variant: 'title2' }}
            />
            <VStack flex>
                {isLoading && <Loader />}
                {!isLoading && isEmpty && (
                    <Grid scrollable scrollViewProps={{ refreshControl }} px={containerPadding}>
                        <EmptyStateCard
                            title={t('noAppointmentsYet')}
                            actionLabel={t('exploreNewBusinesses')}
                            onPress={handleExploreBusinesses}
                            source={emptyAppointments}
                        />
                    </Grid>
                )}
                {!isLoading && !isEmpty && (
                    <SectionList
                        refreshControl={refreshControl}
                        sections={sections}
                        renderItem={renderItem}
                        contentContainerStyle={{ paddingBottom: 16 }}
                        keyExtractor={(item): string => `${item.id}`}
                        maxToRenderPerBatch={10}
                        renderSectionHeader={({ section: { title, data, key } }): ReactElement => (
                            <SectionHeader title={title} data={data} key={key} />
                        )}
                    />
                )}
            </VStack>
        </Screen>
    );
}

function SectionHeader({ title, data }: { data: AppointmentModel[]; title: string }): ReactElement {
    const { t } = useTranslation();

    return (
        <Container>
            <HStack alignItems={'center'} py={2} bgColor={'backgroundSecondary'}>
                <Typography variant={'title3'} fontWeight={700} textTransform={'capitalize'} mr={1}>
                    {title}
                </Typography>
                <Badge title={String(data.length)} color={'primary'} />
            </HStack>
            {data.length === 0 && (
                <>
                    <Typography variant={'subhead'} fontWeight={500} color={'textSecondary'}>
                        {t('noAppointmentsCreateNewOne')}
                    </Typography>
                    <Grid alignItems={'flex-start'} mt={2}>
                        <Button
                            variant={'contained'}
                            color={'primary'}
                            size={'small'}
                            label={t('exploreNewBusinesses')}
                            onPress={(): void => navigate('SearchMap')}
                        />
                    </Grid>
                </>
            )}
        </Container>
    );
}
