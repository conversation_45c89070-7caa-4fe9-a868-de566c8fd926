import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { HomeBusinessesSection } from '~/app/client/components/HomeBusinessesSection/HomeBusinessesSection';
import { UntoldFestivalCard } from '~/app/client/components/HomeBusinessesSection/UntoldFestivalCard';
import { HomeCategories } from '~/app/client/components/HomeCategories';
import { NextAppointmentCard } from '~/app/client/components/NextAppointmentCard';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { Grid } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useDeviceLocation } from '~/hooks/useDeviceLocation';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { useUser } from '~/hooks/useUser';
import { Features } from '~/lib/Features';
import { isRemoteConfigParameterEnabled, RemoteConfigParameters } from '~/lib/firebase/remoteConfig';
import { useApplicationStore } from '~/store/useApplicationStore';
import { useAuthStore } from '~/store/useAuthStore';
import { TheWomanCard } from '../components/HomeBusinessesSection/TheWomanCard';

interface HomeClientScreenProps {
    actionAfterLoad?: () => void;
}

const radius = 3 * 1000; // 3km

export function HomeClientScreen() {
    const { t } = useTranslation();
    const user = useUser();
    const route = useRoute();
    const { navigate } = useNavigation();
    const location = useDeviceLocation();
    const isGuest = useAuthStore((state) => state.isGuest);
    const latestBusinessesVisited = useApplicationStore((state) => state.latestBusinessesVisited);
    const [isInitialized, setIsInitialized] = useState(false);

    const actionAfterLoad = (route.params as HomeClientScreenProps)?.actionAfterLoad;

    const { data: nextAppointment, refetch: refetchNextAppointment } = useQuery({
        queryKey: ['nextAppointment', user?.uid],
        queryFn: usersEndpoint.getNextAppointment,
        enabled: !!user?.uid && !isGuest,
    });

    const {
        data: bookedBusinesses,
        isLoading: isLoadingBookedBusinesses,
        refetch: bookedBusinessesRefetch,
    } = useQuery('bookedBusinesses', businessEndpoint.getMyBookedBusinesses, {
        enabled: !!user?.uid && !isGuest,
    });

    const {
        data: bestBusinesses,
        isLoading: isLoadingBestBusinesses,
        refetch: refetchBestBusinesses,
    } = useQuery('bestBusinesses', () => {
        if (location.coords && location.coords.latitude && location.coords.longitude) {
            const latLng = location.coords.latitude + ',' + location.coords.longitude;
            return businessEndpoint.getBestBusinesses({ latLng, radius: radius * 2 });
        }
        return null;
    });

    const {
        data: nearbyBusinesses,
        isLoading: isLoadingNearbyBusinesses,
        refetch: refetchNearbyBusinesses,
    } = useQuery(
        'nearbyBusinesses',
        () => {
            if (location.coords && location.coords.latitude && location.coords.longitude) {
                const latLng = location.coords.latitude + ',' + location.coords.longitude;
                return businessEndpoint.getNearbyBusinesses({ latLng, radius });
            }
            return null;
        },
        { retry: false },
    );

    const {
        data: recentBusinesses,
        isLoading: isLoadingRecentBusinesses,
        refetch: refetchRecentBusinesses,
    } = useQuery(
        'recentBusinesses',
        () => {
            if (location.coords && location.coords.latitude && location.coords.longitude) {
                const latLng = location.coords.latitude + ',' + location.coords.longitude;
                return businessEndpoint.getRecentBusinesses({ latLng, radius, days: 7 });
            }
            return null;
        },
        { retry: false },
    );

    const refreshControl = useRefreshControl(() => Promise.all([refetchNextAppointment(), bookedBusinessesRefetch()]));

    useEffect(() => actionAfterLoad?.(), [actionAfterLoad]);

    useEffect(() => {
        if (isInitialized) {
            return;
        }
        if (location.coords && location.coords.latitude && location.coords.longitude) {
            refetchBestBusinesses();
            refetchNearbyBusinesses();
            refetchRecentBusinesses();
            setIsInitialized(true);
        }
    }, [isInitialized, location, refetchBestBusinesses, refetchNearbyBusinesses, refetchRecentBusinesses]);

    return (
        <Screen disableScroll disablePadding bgColor={'backgroundSecondary'}>
            <ScreenHeader
                px={3}
                headline={
                    isGuest ? t('hello') : t('helloWithName', { displayName: user?.displayName?.split(' ')?.[0] || '' })
                }
                bgColor={'backgroundPrimary'}
                headlineTypographyProps={{
                    variant: 'title2',
                }}
                headlineActions={
                    Features.ClientNotifications && (
                        <IconButton
                            size={'medium'}
                            variant={'text'}
                            disablePadding
                            onPress={(): void => navigate('NotificationsScreen')}
                        >
                            <MaterialIcons name={'notifications'} />
                        </IconButton>
                    )
                }
            />

            <Grid bgColor={'backgroundPrimary'} pb={2} px={3}>
                <FormFieldBox
                    onPress={(): void => navigate('SearchMap')}
                    alignItems={'center'}
                    flexWrap={'nowrap'}
                    flexDirection={'row'}
                    minHeight={48}
                    px={2}
                >
                    <Icon name={'search'} />
                    <Typography variant="subhead" fontWeight={500} color="textSecondary" pl={1}>
                        {t('searchServiceOrLocation')}
                    </Typography>
                </FormFieldBox>
            </Grid>
            <Grid scrollable scrollViewProps={{ showsVerticalScrollIndicator: false, refreshControl }} pb={3}>
                {isRemoteConfigParameterEnabled(RemoteConfigParameters.TheWomanEnabled) && <TheWomanCard />}
                {isRemoteConfigParameterEnabled(RemoteConfigParameters.UntoldFestivalEnabled) && <UntoldFestivalCard />}
                <Grid px={3}>{nextAppointment && <NextAppointmentCard appointment={nextAppointment} />}</Grid>
                <HomeCategories />
                {!isLoadingBestBusinesses && bestBusinesses && bestBusinesses.length > 0 && (
                    <HomeBusinessesSection title={t('bestBusinesses')} businesses={bestBusinesses} />
                )}
                {!isLoadingNearbyBusinesses && nearbyBusinesses && nearbyBusinesses.length > 0 && (
                    <HomeBusinessesSection title={t('nearbyBusinesses')} businesses={nearbyBusinesses} />
                )}
                {!isLoadingRecentBusinesses && recentBusinesses && recentBusinesses.length > 0 && (
                    <HomeBusinessesSection title={t('recentBusinesses')} businesses={recentBusinesses} />
                )}
                {!isLoadingBookedBusinesses && bookedBusinesses && bookedBusinesses.length > 0 && (
                    <HomeBusinessesSection title={t('bookAgain')} businesses={bookedBusinesses} />
                )}
                <HomeBusinessesSection title={t('latestSearches')} businesses={latestBusinessesVisited} />
            </Grid>
        </Screen>
    );
}
