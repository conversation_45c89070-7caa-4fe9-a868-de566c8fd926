import { ReactElement, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, LayoutAnimation } from 'react-native';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { SignInScreen } from '~/app/common/screens/SignInScreen';
import emptyFavorites from '~/assets/emptyBackgrounds/emptyFavorites.png';
import { EmptyStateCard } from '~/components/EmptyStateCard';
import { SearchBusinessCard } from '~/components/SearchBusinessCard/SearchBusinessCard';
import { Grid } from '~/components/ui/Grid';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { navigate } from '~/lib/utils/navigation';
import { useAuthStore } from '~/store/useAuthStore';
import { useFavouritesStore } from '~/store/useFavouritesStore';

export function FavouritesScreen(): ReactElement {
    const { t } = useTranslation();
    const favouriteBusinesses = useFavouritesStore((state) => state.favourites);
    const isGuest = useAuthStore((state) => state.isGuest);
    const resolveFavourites = useFavouritesStore((state) => state.resolveFavourites);
    const refreshControl = useRefreshControl(() => resolveFavourites());

    useEffect(() => {
        LayoutAnimation.easeInEaseOut();
    }, [favouriteBusinesses]);

    const handleExploreBusinesses = () => {
        navigate('SearchMap');
    };

    const mapBusinessToSearchBusiness = (business: BusinessModel): SearchBusinessModel => ({
        categories: business.categories.map((category) => category.name),
        description: business.description,
        formattedAddress: business.formattedAddress,
        id: business.id,
        name: business.name,
        photos: business.photos,
        profilePicture: business.profilePicture,
        businessServices: [],
        services: [],
        averageRating: business?.reviewInfo?.averageRating ?? 0,
        virtualTourURL: business.virtualTourURL,
        _geoloc: {
            lat: business.latitude,
            lng: business.longitude,
        },
    });

    if (isGuest) {
        return <SignInScreen showGuestOption={false} />;
    }

    return (
        <Screen bgColor="backgroundSecondary" disableScroll>
            <ScreenHeader
                bgColor="backgroundSecondary"
                headline={t('yourFavourites')}
                headlineTypographyProps={{ variant: 'title2' }}
            />
            <Grid flex={1}>
                {favouriteBusinesses.length === 0 ? (
                    <EmptyStateCard
                        title={t('noFavouritesYet')}
                        actionLabel={t('exploreNewBusinesses')}
                        onPress={handleExploreBusinesses}
                        source={emptyFavorites}
                    />
                ) : (
                    <FlatList
                        refreshControl={refreshControl}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{ alignSelf: 'center', paddingTop: 10, width: '100%' }}
                        data={favouriteBusinesses.map(mapBusinessToSearchBusiness)}
                        initialNumToRender={5}
                        maxToRenderPerBatch={3}
                        renderItem={({ item }): ReactElement => (
                            <SearchBusinessCard key={item.id} business={item} mb={4} />
                        )}
                        showsHorizontalScrollIndicator={false}
                        keyExtractor={(item): string => item.id}
                    />
                )}
            </Grid>
        </Screen>
    );
}
