import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { AxiosError } from 'axios';
import moment from 'moment';
import { ReactElement, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { formatDate } from '@bookr-technologies/core/datetime/formatDate';
import { BookNowComponent } from '~/app/client/components/BookNowComponent';
import { BottomSheet } from '~/components/BottomSheet';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { Button } from '~/components/ui/Button';
import { Grid, HStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLocalPushNotificationScheduler } from '~/hooks/useLocalPushNotificationScheduler';
import { useLogger } from '~/hooks/useLogger';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';
import { TranslationKeys } from '~/lib/translations/keys';
import { addToCalendar, deleteCalendarEvent } from '~/lib/utils/calendar';
import { str } from '~/lib/utils/string';

interface BookNowScreenParams {
    business: BusinessModel;
    service: ServiceModel;
    staff: UserModel;
}

export function BookNowSessionsScreen(): ReactElement {
    const logger = useLogger('BookNowSessionsScreen');
    const route = useRoute();
    const { t } = useTranslation();
    const { navigate, goBack } = useNavigation<any>();
    const { service, staff, business } = route.params as BookNowScreenParams;
    const modal = useModal();
    const notifications = useNotifications();
    const notificationScheduler = useLocalPushNotificationScheduler();
    const [appointments, setAppointments] = useState<AppointmentModel[]>([]);
    const [isCancelling, setIsCancelling] = useState(false);
    const [isCancellingAll, setIsCancellingAll] = useState(false);
    const [appointmentCancelling, setAppointmentCancelling] = useState<AppointmentModel>();

    const onBookingSuccess = useEvent((appointment: AppointmentModel) => {
        modal.close();
        setAppointments([...appointments, appointment]);
    });

    const handleCancelIntent = useEvent((appointment: AppointmentModel) => {
        setIsCancelling(true);
        setAppointmentCancelling(appointment);
    });

    const handleCancelAppointment = useEvent(async () => {
        if (!appointmentCancelling) {
            return;
        }
        cancelAppointment(appointmentCancelling);
        setAppointments(appointments.filter((a) => a.id !== appointmentCancelling.id));
    });

    const handleCancelAllAppointments = useEvent(async () => {
        for (const appointment of appointments) {
            await cancelAppointment(appointment);
        }
        goBack();
    });

    const cancelAppointment = async (appointment: AppointmentModel) => {
        if (appointment?.id) {
            try {
                await appointmentsEndpoint.cancelAppointment(appointment.id);
                await Promise.all([
                    deleteCalendarEvent(String(appointment.id)),
                    notificationScheduler.cancelAppointmentReviewPushNotification(appointment),
                ]);
            } catch (error) {
                logger.error('error cancelling appointment', {
                    appointmentId: appointment.id,
                    error,
                });
                if ((error as AxiosError).response?.status === 403) {
                    notifications.error(t('cannotCancelAppointmentAnymore'));
                }
            }
        }
        return;
    };

    const addAppointmentsToCalendar = useEvent(async () => {
        for (const appointment of appointments) {
            await addAppointmentToCalendar(appointment);
        }
    });

    const addAppointmentToCalendar = useCallback(
        async (appointment: AppointmentModel) => {
            const title = `BOOKR ${t('appointment')} - ${service.name} ${t('with')} ${staff.displayName} | ${
                business.name
            }`;
            const res = await addToCalendar(
                title,
                moment(appointment.dateTime).valueOf() / 1000,
                service,
                staff,
                business,
                String(appointment.id),
            );
            if (res.status === 'success') {
                notifications.success(t('addedToCalendar'));
            } else {
                notifications.error(t(res.code as TranslationKeys));
            }
            goBack();
            navigate('ClientApplication', { screen: 'Home' });
        },
        [business, goBack, navigate, notifications, service, staff, t],
    );

    const handleFinish = () => {
        navigate('AppointmentConfirmationScreen', {
            appointments,
            showAddToCalendar: true,
            addToCalendar: addAppointmentsToCalendar,
            onGoToAppointment: () => navigate('ClientApplication', { screen: 'MyAppointments' }),
            onClose: () => {
                goBack();
                navigate('ClientApplication', { screen: 'Home' });
            },
        });
    };

    return (
        <Screen
            bounces={true}
            bgColor={'backgroundPrimary'}
            stickyHeaderIndices={[0]}
            showsVerticalScrollIndicator={false}
        >
            <ScreenHeader
                leftSlot={
                    <IconButton size={'large'} disablePadding onPress={() => setIsCancellingAll(true)}>
                        <MaterialIcons name={'arrow-back'} />
                    </IconButton>
                }
                caption={t('subscription')}
                captionTypographyProps={{
                    fontSize: 12,
                    color: 'primary',
                    pt: 0.5,
                }}
            />

            <HStack justifyContent={'space-between'} alignItems={'flex-end'} mb={3}>
                <Typography variant={'title2'} fontWeight={700}>
                    {t('addBookingToSubscription')}
                </Typography>
                <Button
                    color={'accent'}
                    size={'xsmall'}
                    label={t('xOutOfY', { x: appointments.length, y: service.numberOfSessions }) as string}
                />
            </HStack>

            {appointments.map((appointment) => (
                <HStack
                    key={appointment.id}
                    mt={1}
                    px={3}
                    py={2}
                    borderRadius={12}
                    bgColor={'backgroundSecondary'}
                    justifyContent={'space-between'}
                    alignItems={'center'}
                >
                    <Typography
                        variant={'subhead'}
                        fontSize={15}
                        fontWeight={500}
                        lineHeight={22.5}
                        color={'textSecondary'}
                    >
                        {moment(appointment.dateTime).format('LLL')}
                    </Typography>
                    <Grid
                        p={0.5}
                        alignItems={'center'}
                        justifyContent={'center'}
                        bgColor={'backgroundSecondary'}
                        borderRadius={8}
                    >
                        <MaterialIcons
                            name={'close'}
                            size={24}
                            color={'#AFAFAF'}
                            onPress={() => handleCancelIntent(appointment)}
                        />
                    </Grid>
                </HStack>
            ))}

            {appointments.length < service.numberOfSessions ? (
                <Button
                    mt={3}
                    color={'accent'}
                    size={'large'}
                    variant={'subtle'}
                    startIcon={<MaterialIcons name={'add'} />}
                    label={t('addNewBooking') as string}
                    onPress={modal.open}
                />
            ) : (
                <Button
                    mt={3}
                    color={'accent'}
                    size={'large'}
                    label={t('finishSubscriptionBooking') as string}
                    onPress={handleFinish}
                />
            )}

            <BottomSheet snapPoints={['90%']} {...modal.props}>
                <BookNowComponent
                    hideHeader
                    navigate={navigate}
                    business={business}
                    service={service}
                    staff={staff}
                    onBookingSuccess={onBookingSuccess}
                />
            </BottomSheet>

            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                open={isCancelling}
                onClose={() => setIsCancelling(false)}
                headline={t('cancelAppointmentTitle')}
                subHeadline={`${str(appointmentCancelling?.staff?.business?.name)}, ${str(
                    appointmentCancelling?.service?.name,
                )}, ${formatDate(appointmentCancelling?.dateTime ?? '', 'DD MMM, HH:mm')}`}
                onCancel={() => setIsCancelling(false)}
                onAction={handleCancelAppointment}
            />

            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Error}
                open={isCancellingAll}
                onClose={() => setIsCancellingAll(false)}
                headline={t('cancelAllAppointmentsTitle')}
                subHeadline={t('cancelAllAppointmentsSubtitle')}
                onCancel={() => setIsCancellingAll(false)}
                onAction={handleCancelAllAppointments}
            />
        </Screen>
    );
}
