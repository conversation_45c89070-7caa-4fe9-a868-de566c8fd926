import { MaterialCommunityIcons } from '@expo/vector-icons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import BottomSheet from '@gorhom/bottom-sheet';
import { debounce } from 'lodash';
import LottieView from 'lottie-react-native';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation, TouchableOpacity, View } from 'react-native';
import MapView, { Circle, Marker, Region } from 'react-native-maps';
import { LatLng } from 'react-native-maps/lib/sharedTypes';
import { useQuery } from 'react-query';
import { useTheme } from 'styled-components';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { formatDate } from '@bookr-technologies/core';
import { SearchAdvancedFiltersBottomSheet } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/SearchAdvancedFiltersBottomSheet';
import { SearchForm } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/SearchForm.types';
import { SearchBottomSheet } from '~/app/client/components/Search/SearchBottomSheet/SearchBottomSheet';
import { SearchMapSelectedBusinessCard } from '~/app/client/components/Search/SearchMapSelectedBusinessCard/SearchMapSelectedBusinessCard';
import { SearchResultsBottomSheet } from '~/app/client/components/SearchResultsBottomSheet/SearchResultsBottomSheet';
import { BottomSheet as BottomSheetModal } from '~/components/BottomSheet/BottomSheet';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { kMToLongitudes } from '~/components/utils/radius';
import { localHoursToUtcHours } from '~/components/utils/time';
import { useDeviceLocation } from '~/hooks/useDeviceLocation';
import { useModal } from '~/hooks/useModal';
import { useNotifications } from '~/hooks/useNotifications';

type RegionSearch = LatLng & { radius: number };
const debouncedCall = debounce((func: () => void) => func(), 500);

const advancedFiltersInitialValues: SearchForm = {
    sortBy: 'recommended',
    minPrice: '0',
    maxPrice: '1000',
    instantBooking: false,
};

const filtersEnabled = true;

export function SearchMapScreen() {
    const theme = useTheme();
    const { t } = useTranslation();
    const [selectedBusiness, setSelectedBusiness] = useState<SearchBusinessModel | null>(null);
    const location = useDeviceLocation();
    const mapRef = useRef<MapView>(null);
    const loadingAnimationRef = useRef<LottieView>(null);
    const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);
    const [regionToSearch, setRegionToSearch] = useState<RegionSearch | null>(null);
    const [category, setCategory] = useState('');
    const [addressLocation, setAddressLocation] = useState('');
    const [date, setDate] = useState('');
    const [fromTime, setFromTime] = useState('');
    const [toTime, setToTime] = useState('');
    const advancedFiltersModal = useModal();
    const searchModal = useModal();
    const resultsRef = useRef<BottomSheet>(null);
    const styles = useStyles({ showLoadingAnimation });
    const [filters, setFilters] = useState<SearchForm>(advancedFiltersInitialValues);
    const notifications = useNotifications();

    const { data: searchData, isFetching: isSearchFetching } = useQuery({
        enabled: !!regionToSearch,
        queryKey: [
            'search',
            `${regionToSearch?.latitude},${regionToSearch?.longitude}`,
            regionToSearch?.radius,
            category,
            filters.minPrice,
            filters.maxPrice,
            filters.sortBy,
            filters.instantBooking,
            date,
            fromTime,
            toTime,
        ],
        queryFn: async ({ queryKey }) => {
            const [, latLng, radius, category] = queryKey;
            setShowLoadingAnimation(true);
            const advancedFilters = {
                minPrice:
                    parseInt(advancedFiltersInitialValues.minPrice) !== parseInt(filters.minPrice)
                        ? parseInt(filters.minPrice)
                        : undefined,
                maxPrice:
                    parseInt(advancedFiltersInitialValues.maxPrice) !== parseInt(filters.maxPrice)
                        ? parseInt(filters.maxPrice)
                        : undefined,
                sort: filters.sortBy,
                instantBooking: filters.instantBooking,
            };
            const res = await businessEndpoint.search({
                category: (category as string) || '',
                text: '',
                latLng: latLng as string,
                radius: parseInt(String(radius || 0)),
                size: 100,
                page: 0,
                date: date || undefined,
                fromTime: localHoursToUtcHours(fromTime) || undefined,
                toTime: localHoursToUtcHours(toTime) || undefined,
                ...advancedFilters,
            });
            loadingAnimationRef.current?.reset();
            setShowLoadingAnimation(false);
            return res;
        },
        onError: (error) => {
            console.error('error fetching search data ->', error);
            notifications.error(t('errorSearchingTryAgain'));
            setShowLoadingAnimation(false);
        },
    });

    const updateSearchAndRefreshResults = (
        category: string,
        latLng: string,
        addressLocation: string,
        radius: number,
        date: string,
        fromTime: string,
        toTime: string,
    ) => {
        if (category) {
            setCategory(category);
        }
        if (addressLocation) {
            setAddressLocation(addressLocation);
        }
        setDate(date);
        setFromTime(fromTime);
        setToTime(toTime);
        if (latLng) {
            const [latitudeStr, longitudeStr] = latLng.split(',');
            if (!latitudeStr || !longitudeStr) {
                return;
            }
            const latitude = parseFloat(latitudeStr);
            const longitude = parseFloat(longitudeStr);
            if (latitude !== regionToSearch?.latitude || longitude !== regionToSearch?.longitude) {
                mapRef.current?.animateToRegion(
                    {
                        latitude: latitude,
                        longitude: longitude,
                        latitudeDelta: 0.00001,
                        longitudeDelta: kMToLongitudes((radius || regionToSearch?.radius || 0) / 1000, latitude),
                    },
                    1000,
                );
                if (radius) {
                    setRegionToSearch({
                        latitude: latitude,
                        longitude: longitude,
                        radius,
                    });
                }
            }
        }
    };

    useEffect(() => {
        if (!location.fetching && location.coords) {
            mapRef.current?.animateToRegion(
                {
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude,
                    latitudeDelta: location.coords.latitude / (300 * 10),
                    longitudeDelta: location.coords.longitude / (300 * 10),
                },
                1000,
            );
            setRegionToSearch({
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                radius: 500,
            });
        }
    }, [location.coords, location.fetching]);

    const onRegionChangeComplete = (newRegion: Region) => {
        loadingAnimationRef.current?.play();
        setSelectedBusiness(null);

        const EARTH_RADIUS = 6371000; // in meters
        // Convert latitude delta to radians
        const latDeltaRad = newRegion.latitudeDelta * (Math.PI / 180);
        // Approximate the radius as the distance between two points
        // using latitudeDelta for the radius in the north-south direction
        // and longitudeDelta for the east-west direction
        const radiusNorthSouth = (EARTH_RADIUS * latDeltaRad) / 4;
        const radiusEastWest =
            (EARTH_RADIUS *
                (newRegion.longitudeDelta * (Math.PI / 180)) *
                Math.cos(newRegion.latitude * (Math.PI / 180))) /
            2;
        // Take the average of both distances to approximate the radius
        // divider be any number, 2 or 3
        const radius = (radiusNorthSouth + radiusEastWest) / 2;

        setRegionToSearch({
            latitude: newRegion.latitude,
            longitude: newRegion.longitude,
            radius,
        });
    };

    const handleOnSearchbarPress = () => {
        searchModal.open();
    };

    const handleAdvancedFiltersSave = (values: SearchForm) => {
        advancedFiltersModal.close();
        setFilters((prev) => ({ ...prev, ...values }));
    };

    return (
        <Screen bgColor={theme.palette.backgroundPrimary.main} safeArea disableScroll disablePadding>
            <HStack
                bgColor={theme.palette.backgroundPrimary.main}
                width={'100%'}
                px={2}
                alignItems={'center'}
                justifyContent={'center'}
                mb={2}
            >
                <TouchableOpacity style={{ width: '100%', flex: 1 }} onPress={handleOnSearchbarPress}>
                    <HStack
                        alignItems={'center'}
                        bgColor={theme.palette.backgroundSecondary.main}
                        width={'100%'}
                        borderRadius={999}
                        borderWidth={1}
                        borderColor={'#e2e2e2'}
                        p={1}
                    >
                        <IconButton>
                            <MaterialIcons name={'search'} size={24} color={`primary`} />
                        </IconButton>
                        <VStack flex={1}>
                            <Typography variant={'subhead'} color={'textPrimary'} fontWeight={500}>
                                {!!category ? t(`categories.${category.toLowerCase()}` as any) : t('anyService')}
                            </Typography>
                            <Typography
                                pt={0.5}
                                variant={'caption1'}
                                color={'contentTertiary'}
                                fontWeight={500}
                                numberOfLines={1}
                            >
                                {!!addressLocation ? addressLocation : t('currentLocation')}

                                {date ? ` ∙ ${formatDate(date, 'DD MMM')}` : ''}
                                {fromTime ? ` ${fromTime}` : ''}
                                {toTime ? ` - ${toTime}` : ''}
                            </Typography>
                        </VStack>
                    </HStack>
                </TouchableOpacity>
                {filtersEnabled && (
                    <IconButton
                        height={48}
                        width={48}
                        ml={1}
                        style={styles.advancedFiltersIconButton}
                        borderWidth={1}
                        borderRadius={999}
                        borderColor={theme.palette.contentTertiary.main}
                        onPress={advancedFiltersModal.open}
                    >
                        <MaterialCommunityIcons name="tune-variant" size={20} color={`primary`} />
                    </IconButton>
                )}
            </HStack>
            <MapView
                // provider={PROVIDER_GOOGLE}
                ref={mapRef}
                style={styles.mapView}
                onRegionChangeComplete={(newRegion) => debouncedCall(() => onRegionChangeComplete(newRegion))}
                showsUserLocation={true}
                userInterfaceStyle={theme.mode}
                moveOnMarkerPress={false}
            >
                {regionToSearch && (
                    <Circle
                        center={{
                            latitude: regionToSearch.latitude,
                            longitude: regionToSearch.longitude,
                        }}
                        radius={regionToSearch.radius}
                        strokeWidth={1}
                        strokeColor="rgba(0, 150, 255, 0.5)"
                        fillColor="rgba(0, 150, 255, 0.1)"
                    />
                )}
                {(searchData?.hits || []).map((business) => (
                    <Marker
                        isPreselected={selectedBusiness?.id === business.id}
                        key={business.id}
                        coordinate={{
                            latitude: business._geoloc.lat,
                            longitude: business._geoloc.lng,
                        }}
                        onPress={() => {
                            LayoutAnimation.easeInEaseOut();
                            setSelectedBusiness(business);
                        }}
                    />
                ))}
            </MapView>
            <View style={styles.lottieWrapper}>
                <LottieView
                    ref={loadingAnimationRef}
                    autoPlay
                    cacheComposition
                    hardwareAccelerationAndroid
                    loop={false}
                    duration={2000}
                    source={require('~/assets/lottie/loading-dots.json')}
                    style={styles.lottie}
                />
            </View>

            {!!selectedBusiness && <SearchMapSelectedBusinessCard selectedBusiness={selectedBusiness} />}

            <SearchResultsBottomSheet
                resultsRef={resultsRef}
                isSearchFetching={isSearchFetching}
                searchData={searchData}
                wasTimeFilterApplied={!!fromTime || !!toTime}
            />

            <BottomSheetModal snapPoints={['90%']} {...searchModal.props}>
                <SearchBottomSheet
                    initialValues={{
                        addressLocation,
                        category,
                        latLng: `${regionToSearch?.latitude},${regionToSearch?.longitude}`,
                        radius: regionToSearch?.radius || 5000,
                        date,
                        fromTime,
                        toTime,
                    }}
                    regionToSearchRadius={regionToSearch?.radius || 5000}
                    close={searchModal.close}
                    updateSearchAndRefreshResults={updateSearchAndRefreshResults}
                />
            </BottomSheetModal>

            <BottomSheetModal
                snapPoints={['90%']}
                {...advancedFiltersModal.props}
                enablePanDownToClose={false}
                enableDismissOnClose={false}
            >
                <SearchAdvancedFiltersBottomSheet
                    close={advancedFiltersModal.close}
                    initialValues={filters}
                    handleSubmit={handleAdvancedFiltersSave}
                    resetForm={() => setFilters(advancedFiltersInitialValues)}
                />
            </BottomSheetModal>
        </Screen>
    );
}

const useStyles = makeStyles<
    { showLoadingAnimation: boolean },
    Styles<'mapView' | 'lottieWrapper' | 'lottie' | 'advancedFiltersIconButton'>
>(({ theme, showLoadingAnimation }) => ({
    root: {},
    mapView: { height: '100%', width: '100%' },
    lottieWrapper: {
        position: 'absolute',
        top: '20%',
        left: 0,
        width: showLoadingAnimation ? '100%' : 0,
        height: 20,
    },
    lottie: {
        height: 32,
        width: showLoadingAnimation ? 80 : 0,
        alignSelf: 'center',
        backgroundColor: showLoadingAnimation ? theme.palette.backgroundPrimary.main : 'transparent',
        borderRadius: 20,
        borderWidth: 1,
        borderColor: '#cbcbcb',
    },
    advancedFiltersIconButton: { backgroundColor: theme.palette.backgroundSecondary.main },
}));
