import { useNavigation, useRoute } from '@react-navigation/native';
import moment from 'moment';
import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { BookNowComponent } from '~/app/client/components/BookNowComponent';
import { useBusiness } from '~/hooks/useBusiness';
import { useEvent } from '~/hooks/useEvent';
import { useNotifications } from '~/hooks/useNotifications';
import { useUser } from '~/hooks/useUser';

interface EditAppointmentScreenParams {
    appointment: AppointmentModel;
}

export function EditAppointmentScreen(): ReactElement {
    const route = useRoute();
    const { t } = useTranslation();
    const user = useUser();
    const notifications = useNotifications();
    const { navigate } = useNavigation<any>();
    const { appointment } = route.params as EditAppointmentScreenParams;
    const onBookingSuccess = useEvent(async (a: AppointmentModel) => {
        notifications.success(t('appointmentUpdated', { dateTime: moment(a.dateTime).format('LLL') }));
        const client = a.appointmentClients.find((ac) => ac.client.uid === user?.uid)?.client;
        navigate('AppointmentDetailsScreen', { appointment: a, client });
    });

    const { data: business, isLoading } = useBusiness(appointment.staff.business.id, {
        enabled: !!appointment.staff?.business?.id,
    });

    if (isLoading) {
        return <></>;
    }

    return (
        <BookNowComponent
            isEditAppointment
            appointmentToEdit={appointment}
            navigate={navigate}
            business={business as BusinessModel}
            service={appointment.service}
            staff={appointment.staff}
            onBookingSuccess={onBookingSuccess}
        />
    );
}
