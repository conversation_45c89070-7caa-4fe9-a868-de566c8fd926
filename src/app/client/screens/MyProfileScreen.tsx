import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { ProfileAvatar } from '~/app/business/components/ProfileAvatar';
import { SettingsAboutUs } from '~/app/common/components/SettingsAboutUs';
import { SettingsActions } from '~/app/common/components/SettingsActions';
import { SignInScreen } from '~/app/common/screens/SignInScreen';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { List } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { Paper } from '~/components/ui/Paper';
import { Screen, ScreenHeader } from '~/components/ui/Screen';
import { useEvent } from '~/hooks/useEvent';
import { useUser } from '~/hooks/useUser';
import { Features } from '~/lib/Features';
import { isStaffMember } from '~/lib/utils/appointments';
import { str } from '~/lib/utils/string';
import { useApplicationStore } from '~/store/useApplicationStore';
import { useAuthStore } from '~/store/useAuthStore';

export function MyProfileScreen(): ReactElement {
    const user = useUser();
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const setVisitedIntro = useApplicationStore((state) => state.setVisitedIntro);
    const isGuest = useAuthStore((state) => state.isGuest);

    const handleSelectAccount = useEvent(async () => {
        setVisitedIntro(false);
        navigate('IntroScreen');
    });

    if (isGuest) {
        return <SignInScreen showGuestOption={false} />;
    }

    return (
        <Screen bgColor={'backgroundSecondary'} stickyHeaderIndices={[0]}>
            <ScreenHeader
                bgColor={'backgroundSecondary'}
                headline={t('myProfile')}
                caption={str(user?.displayName)}
                headlineActions={<ProfileAvatar />}
            >
                {user?.accountType === AccountType.Client || isStaffMember(user?.accountType) ? (
                    <HStack my={2}>
                        {user?.accountType === AccountType.Client && (
                            <Button
                                color={'accent'}
                                label={'Become Business'}
                                onPress={handleSelectAccount}
                                startIcon={<MaterialIcons name={'store'} />}
                            />
                        )}

                        {isStaffMember(user?.accountType) && (
                            <Button
                                variant={'subtle'}
                                color={'accent'}
                                label={t('switchToBusiness')}
                                to={'/BusinessApplication/AppointmentsScreen'}
                                startIcon={<MaterialIcons name={'store'} />}
                            />
                        )}
                    </HStack>
                ) : null}
            </ScreenHeader>

            <VStack flexGrow={0} fullWidth>
                <Paper bgColor={'backgroundPrimary'} label={t('general')} fullWidth mb={2}>
                    <List>
                        <ListItemButton to={'/EditProfileScreen'}>
                            <ListItemIcon>
                                <MaterialIcons name={'person'} />
                            </ListItemIcon>
                            <ListItemText primary={t('personalDetails')} primaryTypographyProps={{ fontWeight: 500 }} />
                        </ListItemButton>

                        <ListItemButton to={'/ChangePasswordScreen'}>
                            <ListItemIcon>
                                <MaterialIcons name={'lock'} />
                            </ListItemIcon>
                            <ListItemText primary={t('changePass')} primaryTypographyProps={{ fontWeight: 500 }} />
                        </ListItemButton>

                        {Features.ClientNotifications && (
                            <ListItem>
                                <ListItemIcon>
                                    <MaterialIcons name={'notifications'} />
                                </ListItemIcon>
                                <ListItemText
                                    primary={t('notifications')}
                                    primaryTypographyProps={{ fontWeight: 500 }}
                                />
                            </ListItem>
                        )}
                    </List>
                </Paper>
                {Features.SettingsHelpSection && (
                    <Paper bgColor={'backgroundPrimary'} fullWidth mb={2}>
                        <List>
                            <ListItem>
                                <ListItemIcon>
                                    <MaterialIcons name={'help'} />
                                </ListItemIcon>
                                <ListItemText primary={t('help')} primaryTypographyProps={{ fontWeight: 500 }} />
                            </ListItem>
                        </List>
                    </Paper>
                )}
                <SettingsAboutUs />
                <SettingsActions />
            </VStack>
        </Screen>
    );
}
