/* eslint-disable jsx-a11y/no-autofocus,@typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Keyboard, LayoutAnimation, Pressable } from 'react-native';
import { useTheme } from 'styled-components';
import { businessEndpoint } from '@bookr-technologies/api';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { SearchResult } from '@bookr-technologies/api/types/SearchResult';
import { RootStackParamList } from '~/RoutesParams';
import { SearchBusinessCard } from '~/components/SearchBusinessCard/SearchBusinessCard';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { Screen } from '~/components/ui/Screen';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useKeyboard } from '~/hooks/useKeyboard';
import { navigate, navigateBack } from '~/lib/utils/navigation';

export function SearchBusinessesCategoryScreen() {
    const route = useRoute<RouteProp<RootStackParamList, 'SearchBusinessesCategoryScreen'>>();
    const theme = useTheme();
    const { t } = useTranslation();
    const keyboard = useKeyboard();
    const [isLoading, setIsLoading] = useState(false);
    const [results, setResults] = useState<SearchResult<SearchBusinessModel>>();
    const { category, city, latLng } = route.params;
    const [page, setPage] = useState(0);

    const search = async (city = '', latLng = '', page = 0, size = 10) => {
        setIsLoading(true);
        const result = await businessEndpoint.search({
            category: category || '',
            latLng: city ? latLng : '',
            page,
            radius: city ? 10000 : 10000,
            // meters,
            size,
        });
        LayoutAnimation.easeInEaseOut();
        setResults({ ...results, ...result, hits: [...(results?.hits || []), ...result.hits] });
        setIsLoading(false);
    };

    useEffect(() => {
        search(city, latLng, 0);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleOnBack = () => {
        if (keyboard.active) {
            Keyboard.dismiss();
        } else {
            navigateBack();
        }
    };

    const handleOnFocus = () => {
        navigateBack();
        navigate('SearchMap');
    };

    return (
        <Screen safeArea bgColor={theme.palette.backgroundPrimary.main} stickyHeaderIndices={[0]} disableScroll>
            <HStack mb={1}>
                <TextField
                    onFocus={handleOnFocus}
                    label={`${category ? t(`categories.${category.toLowerCase()}` as any) : t('allCategories')}${
                        city ? `, ${city}` : ''
                    }`}
                    startAdornment={
                        <Pressable onPress={handleOnBack}>
                            <TextFieldAdornment variant={'start'}>
                                <MaterialIcons name={'arrow-back'} size={20} />
                            </TextFieldAdornment>
                        </Pressable>
                    }
                />
            </HStack>

            <FlatList
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ alignSelf: 'center', paddingTop: 10, width: '100%' }}
                data={results?.hits || []}
                initialNumToRender={5}
                ListHeaderComponent={
                    <>
                        {results && (
                            <Typography variant={'title3'} fontWeight={700} fontSize={20} mb={2}>
                                {t('totalResults', { totalResults: results.nbHits })}
                            </Typography>
                        )}
                    </>
                }
                maxToRenderPerBatch={3}
                renderItem={({ item }) => <SearchBusinessCard key={item.id} business={item} mb={4} />}
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item) => item.id}
                onEndReachedThreshold={0.8}
                onEndReached={() => {
                    const totalPages = results?.nbPages || 0;
                    if (!isLoading && totalPages > page + 1) {
                        setPage(page + 1);
                        search(city, latLng, page + 1);
                    }
                }}
            />
            {isLoading && (
                <VStack alignItems={'center'} justifyContent={'center'} pb={3}>
                    <CircularProgress spinDuration={1000} />
                </VStack>
            )}
        </Screen>
    );
}
