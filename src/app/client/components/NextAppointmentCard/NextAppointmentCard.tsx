/* eslint-disable @typescript-eslint/no-explicit-any */
import { Fontisto } from '@expo/vector-icons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { upperFirst } from 'lodash';
import moment from 'moment';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking, TouchableOpacity } from 'react-native';
import { useTheme } from 'styled-components';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { Avatar } from '~/components/ui/Avatar';
import { Button } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { Paper, PaperProps } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useUser } from '~/hooks/useUser';
import { Features } from '~/lib/Features';

interface NextAppointmentCardProps extends PaperProps {
    appointment: AppointmentModel;
}

export function NextAppointmentCard({ appointment, ...rest }: NextAppointmentCardProps): ReactElement | null {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const theme = useTheme();
    const user = useUser();

    const isBusinessDeleted = !appointment?.staff?.business?.name;

    const handlePressAppointment = useEvent(() => {
        if (isBusinessDeleted) {
            return;
        }

        const appointmentClient = appointment.appointmentClients.find((ac) => ac.client.uid === user?.uid);
        navigate('AppointmentDetailsScreen', { appointment, client: appointmentClient?.client });
    });

    const handlePressUber = useEvent(() => {
        if (isBusinessDeleted) {
            return;
        }

        const appointmentClient = appointment.appointmentClients.find((ac) => ac.client.uid === user?.uid);
        navigate('AppointmentDetailsScreen', { appointment, client: appointmentClient?.client });
    });

    const handlePressMeetingLink = useEvent(() => {
        Linking.openURL(appointment?.onlineEvent?.joinUrl);
    });

    if (!appointment?.staff) {
        return null;
    }

    return (
        <VStack>
            <Typography variant={'footnote'} fontWeight={500} pt={3} pb={0.5}>
                {t('nextAppointment')}
            </Typography>
            <TouchableOpacity onPress={handlePressAppointment} activeOpacity={0.7}>
                <Paper bgColor={'backgroundPrimary'} flexWrap={'nowrap'} p={3} {...rest}>
                    <HStack alignItems={'center'}>
                        <VStack flex>
                            <Typography variant={'body'} fontSize={17} fontWeight={700} lineHeight={25.2}>
                                {appointment.staff.business?.name ?? ''}
                            </Typography>
                            <Typography
                                variant={'footnote'}
                                lineHeight={19.5}
                                color={theme.palette.typography.disabled}
                            >
                                {appointment.service.name}
                            </Typography>
                        </VStack>
                        <Avatar
                            source={appointment.staff.business?.profilePicture ?? ''}
                            name={appointment.staff.business?.name ?? ''}
                            size={51}
                        />
                    </HStack>
                    <HStack justifyContent={'space-between'} pt={3}>
                        <Typography variant={'subhead'} fontWeight={700} lineHeight={22.5}>
                            {upperFirst(moment(appointment.dateTime).format('LLLL'))}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={700} lineHeight={22.5}>
                            {appointment.service.price} {appointment.service.currency}
                        </Typography>
                    </HStack>
                    {Features.UberIntegration && (
                        <Button
                            mt={2}
                            size={'medium'}
                            color={'primary'}
                            label={t('getThereWithUber')}
                            startIcon={<Fontisto name={'uber'} color={'secondary'} />}
                            onPress={handlePressUber}
                        />
                    )}
                    {appointment?.onlineEvent?.joinUrl ? (
                        <Button
                            mt={2}
                            size={'medium'}
                            color={'primary'}
                            label={t('joinTheMeeting')}
                            startIcon={<MaterialIcons name={'videocam'} />}
                            onPress={handlePressMeetingLink}
                        />
                    ) : null}
                </Paper>
            </TouchableOpacity>
        </VStack>
    );
}
