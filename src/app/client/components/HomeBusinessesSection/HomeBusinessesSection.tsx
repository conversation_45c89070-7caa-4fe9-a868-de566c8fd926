import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { BlurView } from 'expo-blur';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, TouchableOpacity } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import { useTheme } from 'styled-components';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { HomeTitleSection } from '~/app/client/components/HomeTitleSection';
import { RatingIcon } from '~/components/icons/RatingIcon';
import { Badge } from '~/components/ui/Badge';
import { Grid, HStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { getBusinessPhotos } from '~/components/utils/business';
import { navigate } from '~/lib/utils/navigation';

const useStyles = makeStyles(({ theme }) => ({
    card: {
        width: 144,
        height: 167,
        marginRight: 8,
    },
    businessCard: {
        width: 212,
        height: 160,
        backgroundColor: theme.palette.backgroundPrimary.main,
        borderRadius: 12,
        overflow: 'hidden',
    },
}));

export interface HomeBusinessesSectionProps {
    businesses: Array<SearchBusinessModel | BusinessModel>;
    title: string;
}

export function HomeBusinessesSection({ title, businesses }: HomeBusinessesSectionProps) {
    const { t } = useTranslation();
    const styles = useStyles();
    const [errors, setErrors] = useState<Record<string, boolean>>({});
    const theme = useTheme();

    const handleOnPress = (businessId: string) => {
        navigate('BusinessProfileScreen', { businessId });
    };

    const getReview = (item: SearchBusinessModel | BusinessModel) => {
        let rating = 0;
        if (item instanceof BusinessModel) {
            if (item.reviewInfo?.averageRating > 0) {
                rating = item.reviewInfo?.averageRating;
            }
            rating = 0;
        } else if (item.averageRating > 0) {
            rating = item.averageRating;
        }

        if (rating === 0) {
            return '0';
        }
        return (Math.round(rating * 100) / 100).toFixed(1);
    };

    const getCategory = (item: SearchBusinessModel | BusinessModel, position: number) => {
        if (position > item.categories.length) {
            return '';
        }
        const category = item.categories[position];
        if (typeof category === 'string') {
            return category;
        }
        return category.name;
    };

    return (
        <Grid mt={3}>
            <HomeTitleSection title={title} />
            <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                data={businesses.filter((b) => !!b)}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                renderItem={({ item, index }) => {
                    const photos = getBusinessPhotos(item);

                    const hasServices = 'services' in item && item.services.length > 0;

                    return (
                        <Grid flex borderRadius={10} mr={2} ml={index === 0 ? 3 : 0}>
                            <TouchableOpacity
                                style={styles.businessCard}
                                activeOpacity={0.9}
                                onPress={() => handleOnPress(item.id)}
                            >
                                {!errors[item.id] && photos.length > 0 ? (
                                    <Image
                                        uri={photos[0]}
                                        style={styles.businessCard}
                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                        // @ts-ignore
                                        resizeMode={'cover'}
                                        onError={(): void => setErrors((errors) => ({ ...errors, [item.id]: true }))}
                                    />
                                ) : (
                                    <Grid
                                        flex
                                        justifyContent={'center'}
                                        alignItems={'center'}
                                        px={3}
                                        style={styles.businessCard}
                                    >
                                        <Typography
                                            variant={'caption2'}
                                            color="textSecondary"
                                            textAlign="center"
                                            fontWeight={500}
                                        >
                                            {t('noPreviewAvailable')}
                                        </Typography>
                                    </Grid>
                                )}
                                {hasServices && (
                                    <HStack
                                        position={'absolute'}
                                        borderRadius={8}
                                        bottom={12}
                                        left={8}
                                        overflow={'hidden'}
                                    >
                                        <BlurView
                                            intensity={50}
                                            tint="dark"
                                            style={{
                                                borderRadius: 8,
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                padding: 8,
                                            }}
                                        >
                                            <MaterialIcons
                                                name={'event-available'}
                                                size={20}
                                                color={theme.palette.backgroundPrimary.main}
                                            />
                                            <Typography
                                                pl={0.5}
                                                variant={'caption2'}
                                                fontWeight={500}
                                                color={'secondary'}
                                            >
                                                {t('instantBooking')}
                                            </Typography>
                                        </BlurView>
                                    </HStack>
                                )}
                            </TouchableOpacity>
                            <HStack mt={0.5} alignItems={'center'} justifyContent={'space-between'}>
                                <Typography
                                    variant={'footnote'}
                                    fontSize={15}
                                    fontWeight={700}
                                    lineHeight={19.5}
                                    color={'primary'}
                                    ellipsizeMode={'tail'}
                                    numberOfLines={1}
                                    width={170}
                                >
                                    {item.name}
                                </Typography>
                                {getReview(item) !== '0' && (
                                    <HStack justifyContent={'center'} alignItems={'center'}>
                                        <RatingIcon />
                                        <Typography
                                            variant={'footnote'}
                                            fontSize={12}
                                            fontWeight={getReview(item) !== '0' ? 700 : 500}
                                            lineHeight={19.5}
                                            color={getReview(item) !== '0' ? 'primary' : 'contentSecondary'}
                                        >
                                            {getReview(item)}
                                        </Typography>
                                    </HStack>
                                )}
                            </HStack>
                            <HStack width={'100%'} mt={0.2}>
                                <Badge
                                    styles={{
                                        root: {
                                            marginRight: 4,
                                        },
                                    }}
                                    title={t(`categories.${getCategory(item, 0).toLowerCase()}` as any)}
                                    color={'contentSecondary'}
                                />
                                {item.categories.length > 1 && (
                                    <Badge
                                        styles={{
                                            root: {
                                                marginRight: 4,
                                                borderRadius: 99,
                                            },
                                        }}
                                        title={t('andNMore', { count: item.categories.length - 1 })}
                                        color={'contentSecondary'}
                                    />
                                )}
                            </HStack>
                        </Grid>
                    );
                }}
                keyExtractor={(item, index) => item.id + index}
            />
        </Grid>
    );
}
