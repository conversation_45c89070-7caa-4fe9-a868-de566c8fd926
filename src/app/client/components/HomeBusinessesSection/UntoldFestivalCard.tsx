import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ImageBackground, Linking } from 'react-native';
import { HomeTitleSection } from '~/app/client/components/HomeTitleSection';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { StripeUrlModal } from '~/components/StripeUrlModal/StripeUrlModal';
import { Button } from '~/components/ui/Button';
import { Grid } from '~/components/ui/Grid';
import { makeStyles } from '~/components/ui/makeStyles';

const useStyles = makeStyles(() => ({
    card: {
        // alignSelf: 'center',
        width: '100%',
        height: 180,
        borderRadius: 10,
        backgroundColor: '#000',
        justifyContent: 'flex-end',
    },
}));

export function UntoldFestivalCard() {
    const { t } = useTranslation();
    const styles = useStyles();
    const [openPaymentLink, setOpenPaymentLink] = useState(false);
    const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

    const handleOnPress = () => {
        Linking.openURL('https://www.instagram.com/p/CuqsY89tnV2/');
    };

    const handleSuccessPayment = () => {
        setOpenPaymentLink(false);
        setShowPaymentSuccess(true);
    };

    return (
        <Grid mt={3}>
            <HomeTitleSection
                title={t('nextEvent')}
                mr={1}
                typographyProps={{
                    variant: 'footnote',
                    fontWeight: 500,
                }}
            />

            <Grid mx={2} borderRadius={10} bgColor={'#000'}>
                <ImageBackground
                    source={require('~/assets/bg.png')}
                    style={styles.card}
                    borderRadius={10}
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    resizeMode={'cover'}
                />
                <Button
                    mb={2}
                    mt={-3}
                    mx={2}
                    size={'small'}
                    color={'secondary'}
                    label={t('viewDetails')}
                    onPress={handleOnPress}
                />
            </Grid>
            {openPaymentLink ? (
                <StripeUrlModal
                    uri={'https://buy.stripe.com/bIYdRRecn0eM6OY8ww?locale=ro'}
                    onClose={(): void => setOpenPaymentLink(false)}
                    onSuccess={handleSuccessPayment}
                />
            ) : null}
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Success}
                marginInset={3}
                icon={<MaterialIcons name={'check-circle-outline'} size={53} color={'accent'} />}
                open={showPaymentSuccess}
                headline={t('congratulations')}
                primaryColor={'accent'}
                subHeadline={t('receiveTicketSoon')}
                primaryText={t('done')}
                onAction={() => setShowPaymentSuccess(false)}
                onClose={() => setShowPaymentSuccess(false)}
            />
        </Grid>
    );
}
