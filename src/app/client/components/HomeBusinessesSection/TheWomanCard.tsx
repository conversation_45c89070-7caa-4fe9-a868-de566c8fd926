import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'react-native-expo-image-cache';
import { HomeTitleSection } from '~/app/client/components/HomeTitleSection';
import { BottomSheetAlert, BottomSheetAlertVariant } from '~/components/BottomSheetAlert';
import { StripeUrlModal } from '~/components/StripeUrlModal/StripeUrlModal';
import { Button } from '~/components/ui/Button';
import { Grid } from '~/components/ui/Grid';
import { makeStyles } from '~/components/ui/makeStyles';

const useStyles = makeStyles(() => ({
    card: {
        width: '100%',
        height: 200,
        borderRadius: 10,
    },
}));

export function TheWomanCard() {
    const { t } = useTranslation();
    const styles = useStyles();
    const [openPaymentLink, setOpenPaymentLink] = useState(false);
    const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

    const handleOnPress = () => {
        setOpenPaymentLink(true);
    };

    const handleSuccessPayment = () => {
        setOpenPaymentLink(false);
        setShowPaymentSuccess(true);
    };

    return (
        <Grid mt={3}>
            <HomeTitleSection title={t('nextEvent')} mr={1} />
            <Grid px={2} borderRadius={10}>
                <Image
                    uri={'https://d230pchl7el2vc.cloudfront.net/thewoman.jpeg'}
                    style={styles.card}
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    resizeMode={'cover'}
                />
                <Button
                    mt={1}
                    size={'large'}
                    color={'accent'}
                    label={t('bookNow')}
                    startIcon={<MaterialCommunityIcons name={'ticket-percent'} />}
                    onPress={handleOnPress}
                />
            </Grid>
            {openPaymentLink ? (
                <StripeUrlModal
                    uri={'https://buy.stripe.com/bIYdRRecn0eM6OY8ww?locale=ro'}
                    onClose={(): void => setOpenPaymentLink(false)}
                    onSuccess={handleSuccessPayment}
                />
            ) : null}
            <BottomSheetAlert
                variant={BottomSheetAlertVariant.Success}
                marginInset={3}
                icon={<MaterialIcons name={'check-circle-outline'} size={53} color={'accent'} />}
                open={showPaymentSuccess}
                headline={t('congratulations')}
                primaryColor={'accent'}
                subHeadline={t('receiveTicketSoon')}
                primaryText={t('done')}
                onAction={() => setShowPaymentSuccess(false)}
                onClose={() => setShowPaymentSuccess(false)}
            />
        </Grid>
    );
}
