import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import moment from 'moment';
import { transparentize } from 'polished';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation, Pressable } from 'react-native';
import { DateData } from 'react-native-calendars/src/types';
import SegmentedPicker from 'react-native-segmented-picker';
import { Selections } from 'react-native-segmented-picker/dist/config/interfaces';
import { useTheme } from 'styled-components/native';
import { BottomSheetProps } from '~/components/BottomSheet';
import { Calendar } from '~/components/Calendar';
import { Box } from '~/components/ui/Box';
import { Button } from '~/components/ui/Button';
import { Chip } from '~/components/ui/Chip';
import { Divider } from '~/components/ui/Divider';
import { Container, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { TextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { generateHours } from '~/components/utils/time';
import { useLayout } from '~/hooks/useLayout';
import { useModal } from '~/hooks/useModal';

interface Props {
    close: BottomSheetProps['onClose'];
    initialValues: {
        date: string;
        fromTime: string;
        toTime: string;
    };
    updateDateTime: (date: string, fromTime: string, toTime: string) => void;
}

const today = moment().format('YYYY-MM-DD');

const getPartOfDay = (fromTime: string, toTime: string) => {
    if (fromTime === '06:00' && toTime === '12:00') {
        return 'morning';
    } else if (fromTime === '12:00' && toTime === '18:00') {
        return 'afternoon';
    } else if (fromTime === '18:00' && toTime === '23:59') {
        return 'evening';
    }
    return '';
};

export const SearchDateBottomSheet = ({ close, initialValues, updateDateTime }: Props) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const layout = useLayout();

    const [current, setCurrent] = useState(initialValues.date || '');
    const [fromTime, setFromTime] = useState(initialValues.fromTime || '');
    const [toTime, setToTime] = useState(initialValues.toTime || '');
    const [partOfDay, setPartOfDay] = useState(getPartOfDay(fromTime, toTime));
    const timeModal = useModal();

    const handleDatePress = useCallback(
        async ({ dateString }: DateData) => {
            LayoutAnimation.easeInEaseOut();
            setCurrent(dateString);
        },
        [setCurrent],
    );

    const handlePartOfDayPress = useCallback(
        async (value: string) => {
            LayoutAnimation.easeInEaseOut();
            setPartOfDay(value);
            switch (value) {
                case 'morning':
                    setFromTime('06:00');
                    setToTime('12:00');
                    break;
                case 'afternoon':
                    setFromTime('12:00');
                    setToTime('18:00');
                    break;
                case 'evening':
                    setFromTime('18:00');
                    setToTime('23:59');
                    break;
                default:
                    setFromTime('');
                    setToTime('');
                    break;
            }
        },
        [setFromTime, setToTime, setPartOfDay],
    );

    const onSubmit = useCallback(() => {
        updateDateTime(current, fromTime, toTime);
        close?.();
    }, [current, fromTime, toTime, updateDateTime, close]);

    const onReset = useCallback(() => {
        LayoutAnimation.easeInEaseOut();
        setCurrent('');
        setFromTime('');
        setToTime('');
        setPartOfDay('');
    }, [setCurrent]);

    const dateChips = [
        {
            label: t('anyDate'),
            value: '',
        },
        {
            label: t('today'),
            value: today,
        },
        {
            label: t('tomorrow'),
            value: moment().add(1, 'day').format('YYYY-MM-DD'),
        },
    ];

    const partsOfDayChips = [
        {
            label: t('anyTime'),
            value: '',
        },
        {
            label: t('morning'),
            value: 'morning',
        },
        {
            label: t('afternoon'),
            value: 'afternoon',
        },
        {
            label: t('evening'),
            value: 'evening',
        },
    ];

    const getChipStyles = (checked: boolean) => ({
        chip: {
            borderColor: checked ? theme.palette.primary.main : '#cbcbcb',
            backgroundColor: checked ? theme.palette.primary.main : theme.palette.backgroundSecondary.main,
        },
        pressed: {
            backgroundColor: theme.palette.primary.main,
        },
        pressedText: {
            color: theme.palette.typography.secondary,
        },
    });

    const markedDates: any = {
        [today]: {
            selected: true,
            customTextStyle: {
                color: theme.palette.typography.primary,
            },
            customContainerStyle: {
                backgroundColor: theme.palette.backgroundTertiary.main,
                height: 34,
                width: 34,
                borderRadius: 20,
            },
        },
    };
    if (current) {
        markedDates[current] = {
            selected: true,
            customContainerStyle: {
                backgroundColor: theme.palette.typography.primary,
                height: 34,
                width: 34,
                borderRadius: 20,
            },
            customTextStyle: {
                color: theme.palette.typography.secondary,
            },
        };
    }

    const handleTimeConfirm = useCallback(
        (selections: Selections) => {
            if (timeModal.context.field === 'fromTime') {
                setFromTime(selections.hour);
            } else {
                setToTime(selections.hour);
            }
            setPartOfDay('');
            timeModal.close();
        },
        [setFromTime, setToTime, timeModal],
    );

    const openTimeModal = useCallback(
        (field: string) => () =>
            timeModal.openWithContext({
                field,
            }),
        [timeModal],
    );

    return (
        <Screen disableScroll disablePadding keyboardShouldPersistTaps={'never'}>
            <Container flex>
                <HStack justifyContent={'space-between'} alignItems={'center'}>
                    <Typography variant={'title2'} fontWeight={700}>
                        {t('date')}
                    </Typography>
                    <IconButton
                        width={30}
                        height={30}
                        onPress={close}
                        borderRadius={15}
                        variant={'contained'}
                        color={'backgroundTertiary'}
                    >
                        <MaterialCommunityIcons name={'close'} size={20} />
                    </IconButton>
                </HStack>
                <VStack
                    scrollable
                    scrollViewProps={{
                        style: {
                            flex: 1,
                            height: '100%',
                        },
                        showsVerticalScrollIndicator: false,
                    }}
                    bgColor="backgroundPrimary"
                    mt={3}
                    pb={10}
                    height={'100%'}
                    flex
                >
                    <HStack>
                        {dateChips.map((chip, index) => {
                            const checked = chip.value === current;
                            return (
                                <Chip
                                    key={index}
                                    label={chip.label as string}
                                    value={chip.value}
                                    checked={checked}
                                    onChecked={(value) => {
                                        LayoutAnimation.easeInEaseOut();
                                        setCurrent(value || '');
                                        if (value === '') {
                                            setFromTime('');
                                            setToTime('');
                                            setPartOfDay('');
                                        }
                                    }}
                                    styles={getChipStyles(checked)}
                                />
                            );
                        })}
                    </HStack>

                    <VStack flex={1} height={'100%'} onLayout={layout.handler}>
                        {layout.initialized && (
                            <Calendar
                                calendarWidth={layout.width}
                                current={current}
                                markingType={'period'}
                                markedDates={markedDates}
                                onDayPress={handleDatePress}
                                minDate={moment().format('YYYY-MM-DD')}
                                maxDate={moment().add(6, 'months').format('YYYY-MM-DD')}
                            />
                        )}
                        {current && (
                            <VStack mt={3}>
                                <Typography variant={'title3'} fontWeight={700}>
                                    {t('selectTime')}
                                </Typography>
                                <HStack mt={2}>
                                    {partsOfDayChips.map((chip, index) => {
                                        const checked = chip.value === partOfDay;
                                        return (
                                            <Chip
                                                key={index}
                                                label={chip.label as string}
                                                value={chip.value}
                                                checked={checked}
                                                onChecked={(value) => handlePartOfDayPress(value || '')}
                                                styles={getChipStyles(checked)}
                                            />
                                        );
                                    })}
                                </HStack>
                                <HStack mt={2}>
                                    <Pressable style={{ flex: 1, marginRight: 8 }} onPress={openTimeModal('fromTime')}>
                                        <TextField
                                            editable={false}
                                            onPress={openTimeModal('fromTime')}
                                            label={t('from')}
                                            value={fromTime}
                                        />
                                    </Pressable>
                                    <Pressable style={{ marginLeft: 8, flex: 1 }} onPress={openTimeModal('toTime')}>
                                        <TextField
                                            editable={false}
                                            onPress={openTimeModal('toTime')}
                                            label={t('to')}
                                            value={toTime}
                                        />
                                    </Pressable>
                                </HStack>

                                <SegmentedPicker
                                    visible={timeModal.isOpen}
                                    onCancel={timeModal.close}
                                    onConfirm={handleTimeConfirm}
                                    backgroundColor={theme.palette.backgroundPrimary.main}
                                    confirmTextColor={theme.palette.accent.main}
                                    pickerItemTextColor={theme.palette.typography.textPrimary}
                                    toolbarBackgroundColor={theme.palette.backgroundSecondary.main}
                                    toolbarBorderColor={theme.palette.borderOpaque.main}
                                    selectionBorderColor={theme.palette.accent.main}
                                    selectionBackgroundColor={transparentize(0.9, theme.palette.accent.main)}
                                    defaultSelections={{
                                        hour: timeModal.context
                                            ? timeModal.context.field === 'fromTime'
                                                ? fromTime
                                                : toTime
                                            : '',
                                    }}
                                    options={[
                                        {
                                            items: generateHours(30),
                                            key: 'hour',
                                        },
                                    ]}
                                />
                            </VStack>
                        )}
                    </VStack>
                </VStack>
            </Container>
            <Divider style={{ marginTop: 0 }} />
            <Container width={'100%'} justifyContent={'flex-end'} pb={4}>
                <HStack alignItems={'center'} width={'100%'} justifyContent={'space-between'}>
                    <Box width={'50%'}>
                        <Button label={t('delete')} variant={'subtle'} onPress={onReset} mr={0.5} />
                    </Box>
                    <Box width={'50%'}>
                        <Button size={'medium'} label={t('done')} onPress={onSubmit} ml={0.5} />
                    </Box>
                </HStack>
            </Container>
        </Screen>
    );
};
