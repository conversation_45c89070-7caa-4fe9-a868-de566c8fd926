/* eslint-disable jsx-a11y/no-autofocus */
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import moment from 'moment';
import React, { ReactElement, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, TouchableOpacity } from 'react-native';
import { useTheme } from 'styled-components/native';
import { formatDate } from '@bookr-technologies/core';
import { InitialSearchContent } from '~/app/client/components/Search/InitialSearchContent/InitialSearchContent';
import { NowBottomSheet } from '~/app/client/components/Search/NowBottomSheet/NowBottomSheet';
import { SearchDateBottomSheet } from '~/app/client/components/Search/SearchDateBottomSheet/SearchDateBottomSheet';
import { SearchSelectLocationBottomSheet } from '~/app/client/components/Search/SearchSelectLocationBottomSheet/SearchSelectLocationBottomSheet';
import { CategoryList } from '~/app/common/components/CategoryList/CategoryList';
import { BottomSheet as BottomSheetModal } from '~/components/BottomSheet';
import { Box } from '~/components/ui/Box';
import { Button } from '~/components/ui/Button';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { Switch } from '~/components/ui/Switch';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useModal } from '~/hooks/useModal';
import { isRemoteConfigParameterEnabled, RemoteConfigParameters } from '~/lib/firebase/remoteConfig';
import { SearchServiceLocationBottomSheet } from '../SearchServiceLocationBottomSheet/SearchServiceLocationBottomSheet';
import { SearchTimeBottomSheet } from '../SearchTimeBottomSheet/SearchTimeBottomSheet';

interface Props {
    close: () => void;
    initialValues: {
        addressLocation: string;
        category: string;
        date: string;
        fromTime: string;
        latLng: string;
        radius: number;
        toTime: string;
    };
    regionToSearchRadius: number;
    updateSearchAndRefreshResults: (
        category: string,
        latLng: string,
        addressLocation: string,
        radius: number,
        date: string,
        fromTime: string,
        toTime: string,
    ) => void;
}

export function SearchBottomSheet({ initialValues, close, updateSearchAndRefreshResults }: Props): ReactElement {
    const theme = useTheme();
    const { t } = useTranslation();

    const [addressLocation, setAddressLocation] = useState(initialValues.addressLocation || '');
    const [latLng, setLatLng] = useState('');
    const [radius, setRadius] = useState(initialValues.radius || 0);
    const [category, setCategory] = useState(initialValues.category || '');
    const [date, setDate] = useState(initialValues.date || '');
    const [fromTime, setFromTime] = useState(initialValues.fromTime || '');
    const [toTime, setToTime] = useState(initialValues.toTime || '');
    const [nowEnabled, setNowEnabled] = useState(false);
    const searchServiceLocationModal = useModal();
    const searchSelectLocationModal = useModal();
    const selectDateModal = useModal();
    const selectTimeModal = useModal();
    const nowModal = useModal();

    const handleOnCategoryPress = useCallback((category: string) => {
        setCategory(category);
    }, []);

    const resetForm = (): void => {
        setAddressLocation('');
        setLatLng('');
        setCategory('');
        setRadius(initialValues.radius || 0);
        setDate('');
        setFromTime('');
        setToTime('');
    };

    const search = () => {
        close();
        updateSearchAndRefreshResults(
            category,
            latLng || initialValues.latLng,
            addressLocation,
            Math.round(radius),
            date,
            fromTime,
            toTime,
        );
    };

    const closeAll = () => {
        searchServiceLocationModal.close();
        close();
    };

    const handleOnNowChange = (value: boolean) => {
        if (value) {
            setDate(moment().format('YYYY-MM-DD'));
            setFromTime(moment().format('HH:mm'));
            setToTime(
                moment().add(4, 'hours').isBefore(moment().endOf('day'))
                    ? moment().add(4, 'hours').format('HH:mm')
                    : moment().endOf('day').format('HH:mm'),
            );
        } else {
            setDate('');
            setFromTime('');
            setToTime('');
        }
        setNowEnabled(value);
    };

    const handleUpdateForm = (addressLocation: string, latLng: string, radius: number) => {
        searchSelectLocationModal.close();
        setAddressLocation(addressLocation);
        setLatLng(latLng);
        setRadius(radius);
    };

    const handleUpdateDateTime = (date: string, fromTime: string, toTime: string) => {
        setDate(date);
        setFromTime(fromTime || '');
        setToTime(toTime || '');
    };

    const handleUpdateTime = (fromTime: string, toTime: string) => {
        if ((fromTime || toTime) && !date) {
            setDate(moment().format('YYYY-MM-DD'));
        }
        setFromTime(fromTime || '');
        setToTime(toTime || '');
    };

    return (
        <Screen disableScroll safeBottom>
            <BottomSheetScrollView contentContainerStyle={{ flexGrow: 1 }} showsVerticalScrollIndicator={false}>
                <VStack scrollable width={'100%'} height={'100%'}>
                    <HStack justifyContent={'space-between'} alignItems={'center'}>
                        <Typography variant={'title2'} fontWeight={700}>
                            {t('whatAreYouLookingFor')}
                        </Typography>
                        <IconButton
                            width={30}
                            height={30}
                            onPress={close}
                            borderRadius={15}
                            variant={'contained'}
                            color={'backgroundTertiary'}
                        >
                            <MaterialCommunityIcons name={'close'} size={20} />
                        </IconButton>
                    </HStack>
                    <VStack bgColor="backgroundPrimary" mt={3}>
                        <HStack mb={1.5}>
                            <Pressable style={{ flex: 1 }} onPress={searchServiceLocationModal.open}>
                                <TextField
                                    editable={false}
                                    value={category ? t(`categories.${category.toLowerCase()}` as any) : ''}
                                    onPress={searchServiceLocationModal.open}
                                    label={t('searchServiceOrLocation')}
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <MaterialIcons
                                                name={'search'}
                                                size={20}
                                                color={theme.palette.contentSecondary.main}
                                            />
                                        </TextFieldAdornment>
                                    }
                                    endAdornment={
                                        !!category ? (
                                            <IconButton onPress={() => setCategory('')}>
                                                <MaterialIcons
                                                    name={'close'}
                                                    size={20}
                                                    color={theme.palette.contentSecondary.main}
                                                />
                                            </IconButton>
                                        ) : null
                                    }
                                />
                            </Pressable>
                        </HStack>
                        <HStack mb={1.5}>
                            <Pressable style={{ flex: 1 }} onPress={searchSelectLocationModal.open}>
                                <TextField
                                    editable={false}
                                    onPress={searchSelectLocationModal.open}
                                    label={t('streetLocationEtc')}
                                    value={addressLocation}
                                    startAdornment={
                                        <TextFieldAdornment variant={'start'}>
                                            <MaterialIcons
                                                name={'location-on'}
                                                size={20}
                                                color={theme.palette.contentSecondary.main}
                                            />
                                        </TextFieldAdornment>
                                    }
                                />
                            </Pressable>
                        </HStack>
                        {isRemoteConfigParameterEnabled(RemoteConfigParameters.DateTimeSearchFilterEnabled) && (
                            <VStack>
                                <HStack>
                                    <Pressable
                                        disabled={nowEnabled}
                                        style={{ flex: 1, marginRight: 8 }}
                                        onPress={() => !nowEnabled && selectDateModal.open()}
                                    >
                                        <TextField
                                            disabled={nowEnabled}
                                            editable={false}
                                            onPress={() => !nowEnabled && selectDateModal.open()}
                                            label={date ? t('date') : t('anyDate')}
                                            value={date ? formatDate(date, 'DD MMM') : ''}
                                            startAdornment={
                                                <TextFieldAdornment variant={'start'}>
                                                    <MaterialIcons
                                                        name={'event-note'}
                                                        size={20}
                                                        color={theme.palette.contentSecondary.main}
                                                    />
                                                </TextFieldAdornment>
                                            }
                                        />
                                    </Pressable>
                                    <Pressable
                                        disabled={nowEnabled}
                                        style={{ marginLeft: 8, flex: 1 }}
                                        onPress={() => !nowEnabled && selectTimeModal.open()}
                                    >
                                        <TextField
                                            disabled={nowEnabled}
                                            style={{
                                                width: '100%',
                                                paddingRight: 0,
                                            }}
                                            editable={false}
                                            onPress={() => !nowEnabled && selectTimeModal.open()}
                                            label={fromTime ? t('time') : t('anyTime')}
                                            value={fromTime ? `${fromTime}-${toTime ? toTime : '23:59'}` : ''}
                                            startAdornment={
                                                <TextFieldAdornment variant={'start'}>
                                                    <MaterialIcons
                                                        name={'alarm'}
                                                        size={20}
                                                        color={theme.palette.contentSecondary.main}
                                                    />
                                                </TextFieldAdornment>
                                            }
                                        />
                                    </Pressable>
                                </HStack>
                                <HStack
                                    alignItems={'center'}
                                    justifyContent={'space-between'}
                                    borderRadius={8}
                                    mt={1.5}
                                    px={2}
                                    py={1}
                                    borderColor={'borderOpaque'}
                                >
                                    <TouchableOpacity onPress={nowModal.open}>
                                        <HStack alignItems={'center'}>
                                            <Typography variant={'footnote'} color={'primary'} fontWeight={500} mr={1}>
                                                {t('now')}
                                            </Typography>
                                            <MaterialIcons
                                                name={'info-outline'}
                                                size={13}
                                                color={theme.palette.primary.main}
                                            />
                                        </HStack>
                                    </TouchableOpacity>
                                    <Switch value={nowEnabled} onValueChange={handleOnNowChange} />
                                </HStack>
                            </VStack>
                        )}
                    </VStack>
                    <Grid pt={2} scrollViewProps={{ showsVerticalScrollIndicator: false }}>
                        <InitialSearchContent onPress={closeAll} />
                        <Typography variant={'title3'} fontWeight={700} mb={1.5}>
                            {t('categoriesLabel')}
                        </Typography>
                        <CategoryList onCategoryPress={handleOnCategoryPress} />
                    </Grid>
                </VStack>
            </BottomSheetScrollView>
            <VStack width={'100%'} justifyContent={'flex-end'} pb={4} pt={1}>
                <HStack alignItems={'center'} width={'100%'} justifyContent={'space-between'}>
                    <Box width={'50%'}>
                        <Button label={t('reset')} variant={'subtle'} onPress={resetForm} mr={0.5} />
                    </Box>
                    <Box width={'50%'}>
                        <Button size={'medium'} label={t('search')} onPress={search} ml={0.5} />
                    </Box>
                </HStack>
            </VStack>
            <BottomSheetModal snapPoints={['90%']} {...searchServiceLocationModal.props}>
                <SearchServiceLocationBottomSheet closeCurrent={searchServiceLocationModal.close} closeAll={closeAll} />
            </BottomSheetModal>
            <BottomSheetModal snapPoints={['90%']} {...searchSelectLocationModal.props}>
                <SearchSelectLocationBottomSheet
                    close={searchSelectLocationModal.close}
                    updateForm={handleUpdateForm}
                />
            </BottomSheetModal>
            <BottomSheetModal snapPoints={['90%']} {...selectDateModal.props}>
                <SearchDateBottomSheet
                    initialValues={{
                        date,
                        fromTime,
                        toTime,
                    }}
                    updateDateTime={handleUpdateDateTime}
                    close={selectDateModal.close}
                />
            </BottomSheetModal>
            <BottomSheetModal snapPoints={['90%']} {...selectTimeModal.props}>
                <SearchTimeBottomSheet
                    initialValues={{
                        fromTime,
                        toTime,
                    }}
                    updateTime={handleUpdateTime}
                    close={selectTimeModal.close}
                />
            </BottomSheetModal>
            <BottomSheetModal snapPoints={['90%']} {...nowModal.props}>
                <NowBottomSheet close={nowModal.close} />
            </BottomSheetModal>
        </Screen>
    );
}
