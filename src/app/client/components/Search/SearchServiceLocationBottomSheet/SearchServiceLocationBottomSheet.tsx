import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { debounce } from 'lodash';
import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation, Pressable } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useTheme } from 'styled-components/native';
import { businessEndpoint } from '@bookr-technologies/api';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { SearchResult } from '@bookr-technologies/api/types/SearchResult';
import { Loader } from '~/app/common/components/Loader';
import { BottomSheetProps } from '~/components/BottomSheet';
import { SearchBusinessCard } from '~/components/SearchBusinessCard/SearchBusinessCard';
import { Button } from '~/components/ui/Button';
import { Container, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';

interface Props {
    closeAll: BottomSheetProps['onClose'];
    closeCurrent: BottomSheetProps['onClose'];
}

const debouncedCall = debounce((func: () => void) => func(), 500);

export const SearchServiceLocationBottomSheet = ({ closeCurrent, closeAll }: Props) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const [text, setText] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [results, setResults] = useState<SearchResult<SearchBusinessModel>>();
    const [ableToDisplayNoResults, setAbleToDisplayNoResults] = useState(false);

    const search = async (text = ''): Promise<void> => {
        try {
            setIsLoading(true);
            const results = await businessEndpoint.search({
                text,
                radius: 6371000, // earth radius in meters
                size: 100,
                page: 0,
            });
            LayoutAnimation.easeInEaseOut();
            setResults(results);
        } catch (error) {
            console.log('error while searching', { error });
        } finally {
            setIsLoading(false);
            setAbleToDisplayNoResults(true);
        }
    };

    return (
        <Screen disableScroll disablePadding keyboardShouldPersistTaps={'never'}>
            <Container flex>
                <HStack justifyContent={'space-between'} alignItems={'center'}>
                    <Typography variant={'title2'} fontWeight={700}>
                        {t('search')}
                    </Typography>
                    <IconButton
                        width={30}
                        height={30}
                        onPress={closeCurrent}
                        borderRadius={15}
                        variant={'contained'}
                        color={'backgroundTertiary'}
                    >
                        <MaterialCommunityIcons name={'close'} size={20} />
                    </IconButton>
                </HStack>
                <TextField
                    // eslint-disable-next-line jsx-a11y/no-autofocus
                    autoFocus
                    mt={2}
                    label={t('searchServiceOrLocation')}
                    value={text}
                    onChangeText={(text: string): void => {
                        setText(text);
                        debouncedCall(() => search(text));
                    }}
                    startAdornment={
                        <TextFieldAdornment variant={'start'}>
                            <MaterialIcons name={'search'} size={20} color={theme.palette.contentSecondary.main} />
                        </TextFieldAdornment>
                    }
                    endAdornment={
                        text !== '' ? (
                            <Pressable onPress={(): void => setText('')}>
                                <TextFieldAdornment variant={'end'}>
                                    <MaterialIcons name={'cancel'} color={'#afafaf'} size={20} />
                                </TextFieldAdornment>
                            </Pressable>
                        ) : null
                    }
                />

                {isLoading && <Loader p={3} />}

                {text !== '' && (results?.hits?.length || 0) > 0 && (
                    <FlatList
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{ paddingTop: 10, width: '100%', alignSelf: 'center' }}
                        data={results?.hits || []}
                        initialNumToRender={5}
                        ListHeaderComponent={
                            <Typography variant={'title3'} fontWeight={700} fontSize={20} mb={2}>
                                {t('totalResults', { totalResults: results?.nbHits || 0 })}
                            </Typography>
                        }
                        maxToRenderPerBatch={3}
                        renderItem={({ item }): ReactElement => (
                            <SearchBusinessCard minimalist key={item.id} business={item} mb={4} onPress={closeAll} />
                        )}
                        showsHorizontalScrollIndicator={false}
                        keyExtractor={(item): string => item.id}
                    />
                )}

                {ableToDisplayNoResults && text !== '' && !isLoading && (!results || results?.hits?.length === 0) && (
                    <VStack mt={10} justifyContent={'center'} alignItems={'center'}>
                        <MaterialIcons name={'search'} size={24} color={theme.palette.contentSecondary.main} />
                        <Typography variant={'callout'} fontWeight={700} mb={0.5} mt={1}>
                            {t('noResultsForText', { text })}
                        </Typography>
                        <Typography variant={'footnote'} color={'contentTertiary'}>
                            {t('tryDifferentSearch')}
                        </Typography>

                        <Button
                            label={t('reset')}
                            variant={'subtle'}
                            size={'small'}
                            onPress={() => setText('')}
                            mt={2}
                        />
                    </VStack>
                )}
            </Container>
        </Screen>
    );
};
