import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import React, { ReactElement } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Button } from '~/components/ui/Button';
import { Container, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Typography } from '~/components/ui/Typography';

export function NowBottomSheet({ close }: { close: () => void }): ReactElement {
    const { t } = useTranslation();

    return (
        <Container flex safeBottom>
            <IconButton
                width={30}
                height={30}
                onPress={close}
                borderRadius={15}
                variant={'contained'}
                color={'backgroundTertiary'}
                my={1}
            >
                <MaterialCommunityIcons name={'arrow-left'} size={20} />
            </IconButton>
            <Typography variant={'title2'} fontWeight={700}>
                {t('howDoesNowFeatureWork')}
            </Typography>

            <Typography mt={2.5} variant={'subhead'} fontWeight={500} color={'contentTertiary'}>
                {t('howDoesNowFeatureWorkDescription1')}
            </Typography>

            <Typography mt={2} variant={'callout'} fontWeight={700}>
                {t('whyToUseNowFeature')}
            </Typography>

            <Trans
                i18nKey={'whyToUseNowFeatureDescription1'}
                components={{
                    Text: <Typography variant={'subhead'} color={'contentTertiary'} mt={1} />,
                    Bold: <Typography variant={'subhead'} fontWeight={500} color={'primary'} mt={1} />,
                }}
            />
            <Trans
                i18nKey={'whyToUseNowFeatureDescription2'}
                components={{
                    Text: <Typography variant={'subhead'} color={'contentTertiary'} mt={1} />,
                    Bold: <Typography variant={'subhead'} fontWeight={500} color={'primary'} mt={1} />,
                }}
            />
            <Trans
                i18nKey={'whyToUseNowFeatureDescription3'}
                components={{
                    Text: <Typography variant={'subhead'} color={'contentTertiary'} mt={1} />,
                    Bold: <Typography variant={'subhead'} fontWeight={500} color={'primary'} mt={1} />,
                }}
            />

            <VStack flexGrow={1} justifyContent={'flex-end'}>
                <Button size={'medium'} label={t('gotIt')} onPress={close} />
            </VStack>
        </Container>
    );
}
