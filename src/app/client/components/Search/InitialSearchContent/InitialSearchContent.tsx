import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { SearchBusinessCard } from '~/components/SearchBusinessCard/SearchBusinessCard';
import { Grid } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { useApplicationStore } from '~/store/useApplicationStore';

export function InitialSearchContent({ onPress }: { onPress: () => unknown }): ReactElement {
    const { t } = useTranslation();
    const latestBusinessesVisited = useApplicationStore((state) => state.latestBusinessesVisited);

    return (
        <Grid pt={0}>
            {latestBusinessesVisited && latestBusinessesVisited.length > 0 && (
                <Typography variant={'title3'} fontWeight={700} fontSize={20} mb={2}>
                    {t('latestSearches')}
                </Typography>
            )}
            {latestBusinessesVisited &&
                latestBusinessesVisited
                    .slice(0, 3)
                    .map((business) => (
                        <SearchBusinessCard onPress={onPress} minimalist key={business.id} business={business} mb={4} />
                    ))}
        </Grid>
    );
}
