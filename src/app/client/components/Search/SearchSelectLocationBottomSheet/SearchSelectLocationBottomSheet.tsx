import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GooglePlaceData, GooglePlaceDetail, GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { useTheme } from 'styled-components/native';
import { BottomSheetProps } from '~/components/BottomSheet';
import { Container, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { getPlaceSize } from '~/components/utils/radius';
import { GoogleMapsKey } from '~/lib/map';

interface Props {
    close: BottomSheetProps['onClose'];
    updateForm: (addressLocation: string, latLng: string, radius: number) => void;
}

const useStyles = makeStyles(({ theme }) => ({
    container: {
        overflow: 'visible',
    },
    listView: {
        backgroundColor: theme.palette.backgroundPrimary.main,
        borderRadius: theme.mixins.spacingValue(1),
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: theme.palette.divider.main,
        ...theme.shadows.medium,
    },
    predefinedPlacesDescription: {
        color: theme.palette.accent.main,
    },
    row: {
        backgroundColor: theme.palette.backgroundPrimary.main,
        maxWidth: '100%',
        fontSize: 16,
    },
    description: {
        maxWidth: '100%',
        ...theme.typography.variants.subhead,
        fontWeight: 500,
    },
    separator: {
        display: 'none',
    },
    textInput: {
        backgroundColor: theme.palette.backgroundTertiary.main,
        borderRadius: 12,
        height: 48,
        marginBottom: 0,
        marginTop: 0,
        color: theme.palette.contentTertiary.main,
    },
    textInputContainer: {
        alignContent: 'center',
        alignItems: 'center',
        borderColor: theme.palette.borderOpaque.main,
        borderStyle: 'solid',
        height: theme.mixins.spacingValue(7),
        marginBottom: theme.mixins.spacingValue(2),
    },
}));

export const SearchSelectLocationBottomSheet = ({ close, updateForm }: Props) => {
    const styles = useStyles();
    const { t } = useTranslation();
    const theme = useTheme();
    const [city, setSearchCity] = useState('');

    const handleAutocompletePress = useCallback(
        async (data: GooglePlaceData, detail: GooglePlaceDetail | null) => {
            if (!detail) {
                return;
            }

            updateForm(
                detail.formatted_address,
                `${detail.geometry.location.lat},${detail.geometry.location.lng}`,
                getPlaceSize(detail),
            );

            close?.();
        },
        [close, updateForm],
    );

    return (
        <Screen disableScroll disablePadding keyboardShouldPersistTaps={'never'}>
            <Container flex>
                <HStack justifyContent={'space-between'} alignItems={'center'}>
                    <Typography variant={'title2'} fontWeight={700}>
                        {t('searchForAddress')}
                    </Typography>
                    <IconButton
                        width={30}
                        height={30}
                        onPress={close}
                        borderRadius={15}
                        variant={'contained'}
                        color={'backgroundTertiary'}
                    >
                        <MaterialCommunityIcons name={'close'} size={20} />
                    </IconButton>
                </HStack>
                <VStack bgColor="backgroundPrimary" mt={3}>
                    <HStack>
                        <GooglePlacesAutocomplete
                            textInputProps={{
                                label: t('location'),
                                InputComp: TextField,
                                value: city,
                                onChangeText: setSearchCity,
                                clearButtonMode: 'never',
                                startAdornment: (
                                    <TextFieldAdornment variant={'start'}>
                                        <MaterialIcons
                                            name={'location-on'}
                                            size={20}
                                            color={theme.palette.contentSecondary.main}
                                        />
                                    </TextFieldAdornment>
                                ),
                            }}
                            fetchDetails
                            enablePoweredByContainer={false}
                            styles={styles}
                            placeholder={''}
                            onPress={handleAutocompletePress}
                            filterReverseGeocodingByTypes={['locality']}
                            query={{ key: GoogleMapsKey }}
                        />
                    </HStack>
                </VStack>
            </Container>
        </Screen>
    );
};
