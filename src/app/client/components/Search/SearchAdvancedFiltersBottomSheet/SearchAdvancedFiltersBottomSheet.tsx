import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Formik } from 'formik';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Footer } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/Footer';
import { InstantBookingFilter } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/InstantBookingFilter';
import { PriceRangeFilter } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/PriceRangeFilter';
import { SearchForm } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/SearchForm.types';
import { SortByFilter } from '~/app/client/components/Search/SearchAdvancedFiltersBottomSheet/SortByFilter';
import { BottomSheetProps } from '~/components/BottomSheet';
import { Container, HStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Screen } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';

interface Props {
    close: BottomSheetProps['onClose'];
    handleSubmit: (values: SearchForm) => void;
    initialValues: SearchForm;
    resetForm: () => void;
}

const pricingEnabled = true;

export const SearchAdvancedFiltersBottomSheet = ({ initialValues, close, handleSubmit, resetForm }: Props) => {
    const { t } = useTranslation();

    return (
        <Screen disablePadding keyboardShouldPersistTaps={'never'}>
            <Formik validateOnMount enableReinitialize initialValues={initialValues} onSubmit={handleSubmit}>
                <Container flex safeBottom>
                    <HStack justifyContent={'space-between'} alignItems={'center'}>
                        <Typography variant={'title2'} fontWeight={700}>
                            {t('filters')}
                        </Typography>
                        <IconButton
                            width={30}
                            height={30}
                            onPress={close}
                            borderRadius={15}
                            variant={'contained'}
                            color={'backgroundTertiary'}
                        >
                            <MaterialCommunityIcons name={'close'} size={20} />
                        </IconButton>
                    </HStack>
                    <SortByFilter />
                    {pricingEnabled && <PriceRangeFilter />}
                    <InstantBookingFilter />
                    <Footer resetForm={resetForm} />
                </Container>
            </Formik>
        </Screen>
    );
};
