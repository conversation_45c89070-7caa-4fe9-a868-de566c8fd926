import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '~/components/ui/Box';
import { VStack } from '~/components/ui/Grid';
import { FormikRadio } from '~/components/ui/Radio';
import { Typography } from '~/components/ui/Typography';

const sortByOptions = ['recommended', 'distance', 'rating', 'minPrice'];
export const SortByFilter = () => {
    const { t } = useTranslation();
    return (
        <VStack mt={3}>
            <Typography variant={'body'} fontWeight={700}>
                {t('sortBy')}
            </Typography>
            <Box mt={2} />
            {sortByOptions.map((sortByOption) => (
                <FormikRadio
                    style={{ marginHorizontal: -16 }}
                    key={sortByOption}
                    name={'sortBy'}
                    value={sortByOption}
                    label={t(sortByOption as any)}
                />
            ))}
        </VStack>
    );
};
