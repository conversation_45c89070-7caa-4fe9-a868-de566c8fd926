import { useFormikContext } from 'formik';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '~/components/ui/Box';
import { HStack, VStack } from '~/components/ui/Grid';
import { RangeSlider } from '~/components/ui/Slider/RangeSlider';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';

export const PriceRangeFilter = () => {
    const { t } = useTranslation();
    const { setFieldValue, values } = useFormikContext<{ maxPrice: string; minPrice: string }>();
    return (
        <VStack mt={3}>
            <Typography variant={'body'} fontWeight={700}>
                {t('priceRange')}
            </Typography>
            <Box mt={1.5} />
            <RangeSlider
                minimumValue={0}
                maximumValue={1000}
                sliderValue={[Number(values.minPrice), Number(values.maxPrice)]}
                step={50}
                animateTransitions
                animationType="spring"
                onSlidingComplete={([minPrice, maxPrice]) => {
                    // apparently, the form does not display the new values if we don't convert them to strings
                    if (minPrice > maxPrice) {
                        setFieldValue('minPrice', `${maxPrice}`);
                        setFieldValue('maxPrice', `${minPrice}`);
                    } else {
                        setFieldValue('minPrice', `${minPrice}`);
                        setFieldValue('maxPrice', `${maxPrice}`);
                    }
                }}
                key={`${values.minPrice} - ${values.maxPrice}`}
            />
            <HStack mt={1} width={'100%'} alignItems={'center'} justifyContent={'space-between'}>
                <Box flex flexGrow={1}>
                    <FormikTextField name={'minPrice'} label={t('minimum')} type={'number'} />
                </Box>
                <Box width={32} height={2} bgColor={'contentTertiary'} mx={3} />
                <Box flex flexGrow={1}>
                    <FormikTextField name={'maxPrice'} label={t('maximum')} type={'number'} />
                </Box>
            </HStack>
        </VStack>
    );
};
