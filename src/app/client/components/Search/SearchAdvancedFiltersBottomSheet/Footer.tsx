import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '~/components/ui/Box';
import { Button, FormikButton } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';

export const Footer = ({ resetForm }: { resetForm: () => void }) => {
    const { t } = useTranslation();
    return (
        <VStack flexGrow={1} justifyContent={'flex-end'}>
            <HStack alignItems={'center'} width={'100%'} justifyContent={'space-between'}>
                <Box maxWidth={'50%'} pr={1}>
                    <Button label={t('reset')} variant={'subtle'} fullWidth onPress={resetForm} />
                </Box>
                <Box maxWidth={'50%'}>
                    <FormikButton size={'medium'} label={t('search')} fullWidth />
                </Box>
            </HStack>
        </VStack>
    );
};
