import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Pressable } from 'react-native';
import { BottomSheet as BottomSheetModal } from '~/components/BottomSheet';
import { Box } from '~/components/ui/Box';
import { Button } from '~/components/ui/Button';
import { Container, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { FormikRadio } from '~/components/ui/Radio';
import { Typography } from '~/components/ui/Typography';
import { useModal } from '~/hooks/useModal';

export const InstantBookingFilter = () => {
    const { t } = useTranslation();
    return (
        <VStack mt={4}>
            <InstantBookingTitle />
            <Box mt={2} />
            <FormikRadio
                style={{ marginHorizontal: -16 }}
                name={'instantBooking'}
                value={true}
                label={t('yes')}
                typographyProps={{
                    ellipsizeMode: 'tail',
                    numberOfLines: 1,
                }}
            />
            <FormikRadio
                style={{ marginHorizontal: -16 }}
                name={'instantBooking'}
                value={false}
                label={t('no')}
                typographyProps={{
                    ellipsizeMode: 'tail',
                    numberOfLines: 1,
                }}
            />
        </VStack>
    );
};

const InstantBookingTitle = () => {
    const { t } = useTranslation();
    const businessTypeMoreInformationModal = useModal();
    return (
        <>
            <Pressable
                style={{ flexDirection: 'row', alignItems: 'center' }}
                onPress={businessTypeMoreInformationModal.open}
            >
                <Typography mr={0.5} variant={'body'} fontWeight={700}>
                    {t('instantBooking')}
                </Typography>
                <MaterialIcons name="info-outline" size={16} />
            </Pressable>
            <BottomSheetModal snapPoints={['90%']} {...businessTypeMoreInformationModal.props}>
                <Container flex safeBottom>
                    <IconButton
                        width={30}
                        height={30}
                        onPress={businessTypeMoreInformationModal.close}
                        borderRadius={15}
                        variant={'contained'}
                        color={'backgroundTertiary'}
                        my={1}
                    >
                        <MaterialCommunityIcons name={'arrow-left'} size={20} />
                    </IconButton>
                    <Typography variant={'title2'} fontWeight={700}>
                        {t('businessTypeTitle')}
                    </Typography>

                    <Typography mt={2.5} variant={'subhead'} fontWeight={500} color={'contentTertiary'}>
                        {t('businessTypeDescription')}
                    </Typography>

                    <Typography mt={2} variant={'callout'} fontWeight={700}>
                        {t('whyToChooseInstantBooking')}
                    </Typography>

                    <Trans
                        i18nKey={'whyToChooseInstantBookingDescription1'}
                        components={{
                            Text: <Typography variant={'subhead'} color={'contentTertiary'} mt={1} />,
                            Bold: <Typography variant={'subhead'} fontWeight={500} color={'primary'} mt={1} />,
                        }}
                    />
                    <Trans
                        i18nKey={'whyToChooseInstantBookingDescription2'}
                        components={{
                            Text: <Typography variant={'subhead'} color={'contentTertiary'} mt={1} />,
                            Bold: <Typography variant={'subhead'} fontWeight={500} color={'primary'} mt={1} />,
                        }}
                    />

                    <VStack flexGrow={1} justifyContent={'flex-end'}>
                        <Button size={'medium'} label={t('gotIt')} onPress={businessTypeMoreInformationModal.close} />
                    </VStack>
                </Container>
            </BottomSheetModal>
        </>
    );
};
