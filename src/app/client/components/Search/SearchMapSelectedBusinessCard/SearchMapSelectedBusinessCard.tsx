import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { RootStackParamList } from '~/RoutesParams';
import BookrIcon from '~/components/icons/Bookr';
import { HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';

export const SearchMapSelectedBusinessCard = ({
    selectedBusiness,
}: {
    selectedBusiness: SearchBusinessModel | null;
}) => {
    const { t } = useTranslation();
    const styles = useStyles();
    const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();

    return (
        <View style={styles.selectedBusinessWrapper}>
            <TouchableOpacity
                style={styles.selectedBusinessCard}
                onPress={() =>
                    navigate('BusinessProfileScreen', {
                        businessId: selectedBusiness?.id || '',
                    })
                }
            >
                {!!selectedBusiness?.profilePicture || !!selectedBusiness?.photos?.[0] ? (
                    <Image
                        tint={'dark'}
                        uri={selectedBusiness?.profilePicture || selectedBusiness?.photos?.[0]}
                        style={styles.businessImage}
                    />
                ) : (
                    <View style={styles.placeholder}>
                        <BookrIcon color={'secondary'} size={32} />
                    </View>
                )}
                <VStack flex alignItems={'flex-start'} mt={1.5} style={{ gap: 8 }}>
                    <HStack alignItems={'flex-start'} justifyContent={'space-between'} width={'100%'}>
                        <Typography maxWidth={140} variant={'footnote'} fontWeight={700}>
                            {selectedBusiness?.name}
                        </Typography>

                        {(selectedBusiness?.averageRating || 0) > 0 && (
                            <HStack alignItems={'center'} mr={1.5}>
                                <MaterialIcons name={'star'} size={16} color={`textPrimary`} />
                                <Typography ml={0.5} variant={'footnote'} color={'textPrimary'}>
                                    {selectedBusiness?.averageRating.toFixed(2).slice(0, 3)}
                                </Typography>
                            </HStack>
                        )}
                    </HStack>

                    <VStack flex justifyContent={'flex-end'} pb={1.5} mr={1.5}>
                        <Typography
                            variant={'footnote'}
                            color={'contentPrimary'}
                            fontWeight={500}
                            mb={0.5}
                            numberOfLines={2}
                        >
                            {selectedBusiness?.categories
                                .map((c) => t(`categories.${c.toLowerCase()}` as any))
                                .slice(0, 3)
                                .join(', ')}
                        </Typography>
                        <Typography variant={'caption1'} color={'contentTertiary'} fontWeight={500}>
                            {selectedBusiness?.formattedAddress}
                        </Typography>
                    </VStack>
                </VStack>
            </TouchableOpacity>
        </View>
    );
};

const useStyles = makeStyles(({ theme }) => ({
    selectedBusinessWrapper: {
        position: 'absolute',
        bottom: '20%',
        left: 0,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        flex: 1,
        paddingHorizontal: 16,
        borderRadius: 16,
    },
    selectedBusinessCard: {
        flex: 1,
        width: '100%',
        height: 132,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 16,
        backgroundColor: theme.palette.backgroundPrimary.main,
        flexDirection: 'row',
    },
    businessImage: {
        width: 132,
        height: 132,
        borderRadius: 16,
        marginRight: 16,
    },
    placeholder: {
        width: 132,
        height: 132,
        borderRadius: 16,
        marginRight: 16,
        backgroundColor: theme.palette.primary.main,
        justifyContent: 'center',
        alignItems: 'center',
    },
}));
