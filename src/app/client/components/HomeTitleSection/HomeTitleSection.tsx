import { useTranslation } from 'react-i18next';
import { Button } from '~/components/ui/Button';
import { HStack, HStackProps } from '~/components/ui/Grid';
import { Typography, TypographyProps } from '~/components/ui/Typography';

interface HomeTitleSectionProps extends HStackProps {
    onPress?: () => void;
    title: string;
    typographyProps?: TypographyProps;
}

export function HomeTitleSection({ title, onPress, typographyProps, ...rest }: HomeTitleSectionProps) {
    const { t } = useTranslation();
    return (
        <HStack alignItems={'center'} justifyContent={'space-between'} mb={1} {...rest}>
            <Typography variant={'title3'} fontWeight={700} pl={3} {...typographyProps}>
                {title}
            </Typography>
            {onPress && (
                <Button
                    mr={2}
                    size={'xsmall'}
                    variant={'text'}
                    color={'accent'}
                    onPress={onPress}
                    label={t('showMore')}
                />
            )}
        </HStack>
    );
}
