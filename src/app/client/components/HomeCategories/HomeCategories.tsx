import { ReactElement, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, ImageBackground, TouchableOpacity } from 'react-native';
import { HomeTitleSection } from '~/app/client/components/HomeTitleSection';
import BarberCategory from '~/assets/Barber.png';
import DentistCategory from '~/assets/Dentist.png';
import HairstyleCategory from '~/assets/Hairstyle.png';
import MakeupCategory from '~/assets/Makeup.png';
import MassageCategory from '~/assets/Massage.png';
import NailsCategory from '~/assets/Nails.png';
import OphthalmologyCategory from '~/assets/Ophthalmology.png';
import { Grid } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useDeviceLocation } from '~/hooks/useDeviceLocation';
import { useSafeState } from '~/hooks/useSafeState';
import { googleMapsEndpoint } from '~/lib/map';
import { navigate } from '~/lib/utils/navigation';

const CATEGORIES = [
    { source: BarberCategory, name: 'barber', key: 'BARBER' },
    { source: HairstyleCategory, name: 'hairstyling', key: 'HAIRSTYLING' },
    { source: MakeupCategory, name: 'makeup', key: 'MAKEUP' },
    { source: DentistCategory, name: 'dentistry', key: 'DENTISTRY' },
    { source: MassageCategory, name: 'massage', key: 'MASSAGE' },
    { source: NailsCategory, name: 'manicure', key: 'MANICURE' },
    { source: OphthalmologyCategory, name: 'ophthalmology', key: 'OPHTHALMOLOGY' },
];

const useStyles = makeStyles(() => ({
    card: {
        width: 144,
        height: 167,
        marginRight: 8,
    },
}));

export function HomeCategories(): ReactElement {
    const { t } = useTranslation();
    const styles = useStyles();
    const [city, setSearchCity] = useSafeState('');
    const [latLng, setLatLng] = useSafeState<string>('');
    const location = useDeviceLocation();

    const handleOnPressCategory = useCallback(
        (category: string) => {
            handleOnCategoryPress(category, city, latLng);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [city, latLng],
    );

    const handleOnPress = (): void => {
        navigate('SearchMap');
    };

    const handleOnCategoryPress = useCallback(
        (category: string, cityToSearch = city, latLngToSearch = latLng) => {
            navigate('SearchBusinessesCategoryScreen', {
                category: category,
                latLng: latLngToSearch,
                city: cityToSearch,
            });
        },
        [city, latLng],
    );

    useEffect(() => {
        const getLocationSearchCity = async (): Promise<void> => {
            if (!location || !location.coords || location.fetching || latLng) {
                return;
            }

            const { results } = await googleMapsEndpoint.geocode({
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                cached: 'home',
            });

            const resultCity = results[0].address_components.find(({ types }) => types.includes('locality'));
            setSearchCity(resultCity?.long_name || '');
            setLatLng(`${location.coords.latitude},${location.coords.longitude}`);
        };

        getLocationSearchCity();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        location?.fetching,
        location?.coords?.latitude,
        location?.coords?.longitude,
        latLng,
        city,
        setSearchCity,
        setLatLng,
    ]);

    return (
        <Grid mt={3}>
            <HomeTitleSection title={t('bestCategories')} onPress={handleOnPress} />
            <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                data={CATEGORIES}
                initialNumToRender={10}
                maxToRenderPerBatch={10}
                renderItem={({ item, index }): ReactElement => (
                    <TouchableOpacity
                        activeOpacity={0.9}
                        style={[styles.card, { marginLeft: index === 0 ? 24 : 0 }]}
                        onPress={(): void => handleOnPressCategory(item.key)}
                    >
                        <ImageBackground
                            source={item.source}
                            resizeMode={'cover'}
                            style={{ height: '100%' }}
                            borderRadius={10}
                        >
                            <Typography pl={1.5} pt={1.5} variant={'footnote'} fontWeight={500} color={'secondary'}>
                                {t(`categories.${item.name}` as any)}
                            </Typography>
                        </ImageBackground>
                    </TouchableOpacity>
                )}
                keyExtractor={(item): string => item.key}
            />
        </Grid>
    );
}
