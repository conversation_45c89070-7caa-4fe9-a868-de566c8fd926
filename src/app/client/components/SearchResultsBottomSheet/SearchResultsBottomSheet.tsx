import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList } from 'react-native-gesture-handler';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { SearchResult } from '@bookr-technologies/api/types/SearchResult';
import { SearchBusinessCard } from '~/components/SearchBusinessCard/SearchBusinessCard';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';

interface SearchResultsBottomSheetProps {
    isSearchFetching: boolean;
    resultsRef: React.RefObject<BottomSheet>;
    searchData?: SearchResult<SearchBusinessModel>;
    wasTimeFilterApplied: boolean;
}

export const SearchResultsBottomSheet: React.FC<SearchResultsBottomSheetProps> = ({
    resultsRef,
    isSearchFetching,
    searchData,
    wasTimeFilterApplied,
}) => {
    const { t } = useTranslation();

    return (
        <BottomSheet
            snapPoints={[140, '50%', '80%']}
            ref={resultsRef}
            handleComponent={() => (
                <HStack width={'100%'} justifyContent={'center'} alignItems={'center'} pt={2} pb={2}>
                    <VStack bgColor={'#cbcbcb'} width={40} height={4} borderRadius={999} />
                </HStack>
            )}
        >
            <BottomSheetView style={{ flex: 1, width: '100%', flexDirection: 'row' }}>
                {isSearchFetching ? (
                    <VStack alignItems={'center'} justifyContent={'center'} pb={3} width={'100%'}>
                        <CircularProgress spinDuration={1000} />
                    </VStack>
                ) : (
                    <FlatList
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{
                            paddingHorizontal: 16,
                            width: '100%',
                            alignSelf: 'center',
                            paddingBottom: 100,
                        }}
                        data={searchData?.hits || []}
                        initialNumToRender={5}
                        ListHeaderComponent={
                            <>
                                {searchData && (
                                    <Typography
                                        textAlign={'center'}
                                        color={'contentTertiary'}
                                        variant={'footnote'}
                                        fontWeight={500}
                                        mb={2}
                                    >
                                        {searchData.nbHits > 0 && searchData.hits?.length > 0
                                            ? wasTimeFilterApplied
                                                ? t('yourSearchResults')
                                                : t('totalBusinessesNearby', { total: searchData?.nbHits })
                                            : t('noBusinessesNearby')}
                                    </Typography>
                                )}
                            </>
                        }
                        maxToRenderPerBatch={3}
                        renderItem={({ item }) => <SearchBusinessCard key={item.id} business={item} mb={6} />}
                        showsHorizontalScrollIndicator={false}
                        keyExtractor={(item) => item.id}
                    />
                )}
            </BottomSheetView>
        </BottomSheet>
    );
};
