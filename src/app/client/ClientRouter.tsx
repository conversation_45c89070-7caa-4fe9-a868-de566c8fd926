import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { SearchMapScreen } from '~/app/client/screens/SearchMapScreen';
import { tabBarOptions } from '~/lib/utils/navigation';
import { useFavouritesStore } from '~/store/useFavouritesStore';
import { FavouritesScreen } from './screens/FavouritesScreen';
import { HomeClientScreen } from './screens/HomeClientScreen';
import { MyAppointmentsScreen } from './screens/MyAppointmentsScreen';
import { MyProfileScreen } from './screens/MyProfileScreen';

const TabNavigator = createBottomTabNavigator();
const { Navigator, Screen } = TabNavigator;

export function ClientRouter() {
    const { t } = useTranslation();
    const resolveFavourites = useFavouritesStore((state) => state.resolveFavourites);

    useEffect(() => {
        resolveFavourites();
    }, [resolveFavourites]);

    return (
        <Navigator initialRouteName={'Home'}>
            <Screen name={'Home'} component={HomeClientScreen} options={tabBarOptions(t('home'), 'home')} />
            <Screen
                name={'Favourites'}
                component={FavouritesScreen}
                options={tabBarOptions(t('favourites'), 'favorite')}
            />
            <Screen name={'SearchMap'} component={SearchMapScreen} options={tabBarOptions(t('search'), 'search')} />
            <Screen
                name={'MyAppointments'}
                component={MyAppointmentsScreen}
                options={tabBarOptions(t('appointments'), 'date-range')}
            />
            <Screen
                name={'MyProfile'}
                component={MyProfileScreen}
                options={tabBarOptions(t('profile'), 'account-circle')}
            />
        </Navigator>
    );
}
