import { BottomSheetModal, BottomSheetModalProps } from '@gorhom/bottom-sheet';
import React, { forwardRef, ReactNode, useEffect, useMemo } from 'react';
import { ModalProps } from 'react-native';
import { useTheme } from 'styled-components/native';
import { Backdrop } from '~/components/BottomSheet/Backdrop';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useForwardRef } from '~/hooks/useForwardRef';

export interface BottomSheetProps extends Omit<BottomSheetModalProps, 'snapPoints'> {
    children: ReactNode;
    marginInset?: number | boolean;
    modalProps?: ModalProps;
    onClose?: () => void;
    open: boolean;
    snapPoints?: BottomSheetModalProps['snapPoints'];
}

export const BottomSheet = forwardRef<BottomSheetModal, BottomSheetProps>(function BottomSheet(props, ref) {
    const { open, children, onClose, modalProps, marginInset, ...rest } = props;

    const theme = useTheme();
    const marginInsetSize = useMemo(() => {
        if (!marginInset) {
            return 0;
        }

        return theme.mixins.spacingValue(typeof marginInset === 'number' ? marginInset : 3);
    }, [theme, marginInset]);

    const styles = useStyles({ marginInsetSize });

    const [forwardRef, bottomSheetRef] = useForwardRef<BottomSheetModal>(ref);

    useEffect(() => {
        if (open) {
            bottomSheetRef.current?.present();
        } else {
            bottomSheetRef.current?.dismiss();
        }
    }, [bottomSheetRef, open]);

    const handleClose = useEvent(() => {
        onClose?.();
    });

    if (!open) {
        return null;
    }

    return (
        <BottomSheetModal
            enablePanDownToClose
            ref={forwardRef}
            index={0}
            snapPoints={['50%']}
            onDismiss={handleClose}
            backdropComponent={Backdrop}
            detached={marginInsetSize > 0}
            bottomInset={marginInsetSize}
            handleIndicatorStyle={styles.handleIndicatorStyle}
            style={styles.root}
            {...rest}
        >
            {children}
        </BottomSheetModal>
    );
});

const useStyles = makeStyles<{ marginInsetSize: number }, Styles<'root' | 'handleIndicatorStyle'>>(
    ({ theme, marginInsetSize }) => ({
        handleIndicatorStyle: {
            backgroundColor: theme.palette.borderOpaque.main,
        },
        root: {
            marginHorizontal: marginInsetSize,
        },
    }),
);
