import { ReactElement } from 'react';
import { Divider, DividerProps } from '../Divider';
import { Grid, GridProps } from '../Grid';

export type ListItemProps = GridProps & {
    divider?: boolean;
    dividerProps?: DividerProps;
};

export function ListItem(props: ListItemProps): ReactElement {
    const { divider, dividerProps, ...rest } = props;

    return (
        <>
            <Grid
                container
                px={2.5}
                height={48}
                alignItems={'center'}
                alignContent={'center'}
                justifyContent={'flex-start'}
                flexWrap={'nowrap'}
                {...rest}
            />
            {divider ? <Divider {...dividerProps} /> : null}
        </>
    );
}
