import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Children, ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { BottomSheet, BottomSheetProps } from '~/components/BottomSheet';
import { Divider } from '~/components/ui/Divider';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { Container } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { FlatList } from '~/components/ui/List';
import { fromMenuItems } from '~/components/ui/Menu/MenuItemRow';
import { TextField, TextFieldAdornment } from '~/components/ui/TextField';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useWatchValue } from '~/hooks/useWatch';
import { MenuProps } from '../Menu';
import { MenuItemElement, MenuItemProps } from '../MenuItem';
import { Typography } from '../Typography';
import { makeStyles } from '../makeStyles';

export interface SelectProps {
    bottomSheetProps?: Omit<BottomSheetProps, 'open' | 'onClose'>;
    children: MenuItemElement | MenuItemElement[];
    disabled?: boolean;
    label?: string;
    onChange?: MenuProps['onPress'];
    value?: MenuItemProps['value'];
}

export function Select({ children, value, disabled, onChange, label, bottomSheetProps }: SelectProps): ReactElement {
    const styles = useStyles();
    const { t } = useTranslation();
    const modal = useModal();
    const [currentValue, setCurrentValue] = useState(value);
    const [search, setSearch] = useState('');

    const options = useMemo(() => Children.toArray(children) as MenuItemElement[], [children]);
    const selected = useMemo(
        () => options.find((option) => option.props.value === currentValue),
        [options, currentValue],
    );

    const handlePressItem = useEvent((event, item) => {
        const { value, data } = item.props;

        onChange?.(item, value, data);
        setCurrentValue(value);
        modal.close();
    });

    useWatchValue(value, setCurrentValue);

    return (
        <>
            <FormFieldBox onPress={modal.open} px={2} height={56} alignItems={'center'} disabled={disabled}>
                {selected?.props?.avatar ? <View style={styles.avatarHolder}>{selected?.props.avatar}</View> : null}
                <View style={styles.content}>
                    {label ? (
                        <Typography
                            variant={!selected ? 'subhead' : 'footnote'}
                            fontWeight={500}
                            color={!selected ? 'textPrimary' : 'textSecondary'}
                        >
                            {label}
                        </Typography>
                    ) : null}
                    {selected?.props?.children ? (
                        <Typography variant={'subhead'} fontWeight={500}>
                            {selected?.props?.children}
                        </Typography>
                    ) : null}
                </View>
                {!disabled && <MaterialIcons name={'keyboard-arrow-down'} size={24} />}
            </FormFieldBox>
            <BottomSheet snapPoints={['74%']} keyboardBehavior="interactive" {...modal.props} {...bottomSheetProps}>
                <Container>
                    <TextField
                        value={search}
                        onChangeText={setSearch}
                        placeholder={t('typeToSearch')}
                        startAdornment={
                            <TextFieldAdornment variant="start">
                                <Icon name="search" />
                            </TextFieldAdornment>
                        }
                    />
                </Container>

                <Divider mt={2.75} mb={0} />

                <FlatList inBottomSheet>
                    {fromMenuItems({
                        items: Children.toArray(children) as MenuItemElement[],
                        onPress: handlePressItem,
                        selected: currentValue,
                        search,
                    })}
                </FlatList>
            </BottomSheet>
        </>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    avatarHolder: {
        marginRight: theme.mixins.spacingValue(1),
    },
    content: {
        flexGrow: 1,
        paddingLeft: theme.mixins.spacingValue(1),
    },
    root: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: theme.mixins.spacingValue(1.5),
        borderStyle: 'solid',
        borderWidth: 1,
        flexDirection: 'row',
        height: theme.mixins.spacingValue(7),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(2),
    },
}));
