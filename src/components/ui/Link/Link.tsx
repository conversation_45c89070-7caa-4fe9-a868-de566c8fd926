import { useLinkTo } from '@react-navigation/native';
import { To } from '@react-navigation/native/lib/typescript/src/useLinkTo';
import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';
import { ReactElement, useMemo } from 'react';
import { GestureResponderEvent, Pressable, PressableProps } from 'react-native';
import { sx } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { usePressable } from '~/hooks/usePressable';

export type LinkProps<ParamList extends ReactNavigation.RootParamList = ReactNavigation.RootParamList> = {
    activeOpacity?: number;
    disableFeedback?: boolean;
    external?: boolean;
    href?: string;
    to?: To<ParamList>;
} & PressableProps;

export function Link({
    onPress,
    to,
    href,
    external,
    style,
    onPressIn,
    onPressOut,
    activeOpacity,
    disableFeedback,
    ...rest
}: LinkProps): ReactElement {
    const linkTo = useLinkTo();
    const log = useLogger('Link');
    const [isPressed, pressableProps] = usePressable({
        onPressIn,
        onPressOut,
    });

    const pressedStyle = useMemo(
        () => ({
            opacity: isPressed && !disableFeedback ? activeOpacity ?? 0.6 : 1,
        }),
        [isPressed, activeOpacity, disableFeedback],
    );

    const handleLinkPress = useEvent(async (event: GestureResponderEvent) => {
        try {
            if (onPress) {
                await onPress(event);
            }

            if (to) {
                linkTo(to);
            } else if (href) {
                // Add extra schema if we need them.
                const hasSchema = /^(http|https|mailto|tel):/.test(href);
                const url = hasSchema ? href : `https://${href}`;

                if (!external) {
                    await WebBrowser.openBrowserAsync(url);
                } else {
                    await Linking.openURL(url);
                }
            }
        } catch (e) {
            log.error('Error opening link', { e, to, href, external });
        }
    });

    return <Pressable onPress={handleLinkPress} style={sx(pressedStyle, style)} {...pressableProps} {...rest} />;
}
