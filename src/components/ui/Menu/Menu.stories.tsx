import { storiesOf } from '@storybook/react-native';
import { useCallback, useRef, useState } from 'react';
import { GestureResponderEvent, TouchableOpacity } from 'react-native';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Menu } from '~/components/ui/Menu';
import { MenuItem } from '~/components/ui/MenuItem';
import { Screen } from '~/components/ui/Screen';
import { ThemeProvider } from '~/components/ui/ThemeProvider';
import { Typography } from '~/components/ui/Typography';

function DefaultExample() {
    const anchorEl = useRef<TouchableOpacity>(null);
    const [selected, setSelected] = useState(null);
    const [open, setOpen] = useState(false);

    const handlePressItem = useCallback((event: GestureResponderEvent, value: any) => {
        setSelected(value);
    }, []);

    return (
        <VStack>
            <Button label={'Open Menu'} ref={anchorEl} onPress={() => setOpen(true)} />
            {selected && <Typography>Selected item {selected}</Typography>}

            <Menu open={open} onClose={() => setOpen(false)} anchorEl={anchorEl}>
                <MenuItem onPress={handlePressItem} value={'item 1'}>
                    Item 1
                </MenuItem>
                <MenuItem onPress={handlePressItem} value={'item 2'}>
                    Item 2
                </MenuItem>
                <MenuItem onPress={handlePressItem} value={'item 3'}>
                    Item 3
                </MenuItem>
                <MenuItem onPress={handlePressItem} value={'item 4'}>
                    Lorem Ipsum dolor it amet
                </MenuItem>
            </Menu>
        </VStack>
    );
}

storiesOf('Menu', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    Menu
                </Typography>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={2}>
                    Meu components
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Default', () => <DefaultExample />);
