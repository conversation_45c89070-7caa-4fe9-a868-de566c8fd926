import { ReactElement } from 'react';
import { GestureResponderEvent } from 'react-native';
import { useTheme } from 'styled-components/native';
import { useEvent } from '~/hooks/useEvent';
import { usePressable } from '~/hooks/usePressable';
import { ListItemElement } from '../List/FlatList';
import { ListItem } from '../ListItem';
import { ListItemAvatar } from '../ListItemAvatar';
import { ListItemButton } from '../ListItemButton';
import { ListItemText } from '../ListItemText';
import { MenuItem, MenuItemElement, MenuItemProps } from '../MenuItem';

interface MenuItemRowProps {
    item: MenuItemElement;
    selected: boolean;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onPress(event: GestureResponderEvent, item: MenuItemElement): void;
}

export function MenuItemRow({ item, selected, onPress }: MenuItemRowProps): ReactElement {
    const theme = useTheme();
    const [isPressed, pressableProps] = usePressable();

    const handlePress = useEvent((event) => onPress(event, item));

    const Component = !!item.props.onPress || !!onPress ? ListItemButton : ListItem;

    return (
        <Component
            onPress={handlePress}
            bgColor={
                isPressed ? theme.palette.backgroundSecondary.main : selected ? theme.palette.accent.shade10 : undefined
            }
            {...pressableProps}
        >
            {item.props.avatar ? <ListItemAvatar>{item.props.avatar}</ListItemAvatar> : null}
            <ListItemText
                primary={item.props.children}
                primaryTypographyProps={{
                    color: selected ? theme.palette.accent.main : undefined,
                    fontWeight: 500,
                    variant: 'subhead',
                }}
            />
        </Component>
    );
}

export function fromMenuItems({
    items,
    onPress,
    search,
    searchByValue,
    selected,
}: {
    items: MenuItemElement[];
    onPress: MenuItemRowProps['onPress'];
    search?: string;
    searchByValue?: boolean;
    selected?: MenuItemProps['value'];
}): ListItemElement[] {
    return items
        .filter((item) => {
            if (!search) {
                return true;
            }

            return (
                String(item.props.children)?.toLowerCase()?.includes(search.toLowerCase()) ||
                (searchByValue && String(item.props.value)?.toLowerCase()?.includes(search.toLowerCase()))
            );
        })
        .map((item: ReactElement<MenuItemProps, typeof MenuItem>, index) => {
            const key = item.key || `${index}_${item.props.value}`;

            return <MenuItemRow key={key} onPress={onPress} selected={selected === item.props.value} item={item} />;
        });
}
