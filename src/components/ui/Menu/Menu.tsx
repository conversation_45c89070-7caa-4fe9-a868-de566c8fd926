import { Children, isValidElement, ReactElement } from 'react';
import styled from 'styled-components/native';
import { Box } from '~/components/ui/Box';
import { fromMenuItems } from '~/components/ui/Menu/MenuItemRow';
import { Popover, PopoverProps } from '~/components/ui/Popover';
import { useEvent } from '~/hooks/useEvent';
import { List } from '../List';
import { MenuItem, MenuItemElement, MenuItemProps } from '../MenuItem';
import { shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';
import { backgroundMixin, backgroundMixinHelpers } from '../ThemeProvider/mixins/background';
import { boxMixin, boxMixinHelpers } from '../ThemeProvider/mixins/box';

export interface MenuProps extends Omit<PopoverProps, 'children'> {
    children: MenuItemElement | MenuItemElement[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onPress?: (item: MenuItemElement, value: MenuItemProps['value'], data: MenuItemProps['data']) => void;
    selected?: MenuItemProps['value'];
}

function MenuComponent({ children, style, fullWidth, onPress, selected, ...rest }: MenuProps): ReactElement {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const menuItems = Children.toArray(children).filter(isValidElement) as Array<
        ReactElement<MenuItemProps, typeof MenuItem>
    >;

    const handleListItemPress = useEvent((event, item: ReactElement<MenuItemProps, (props: MenuItemProps) => null>) => {
        const { value, data } = item.props;

        onPress?.(item, value, data);
        item.props.onPress?.(event, value, data);
        rest.onClose?.();
    });

    return (
        <Popover {...rest}>
            <Box style={style} shadow={'small'}>
                <List scrollable fullWidth={fullWidth} overflow={'hidden'}>
                    {fromMenuItems({
                        items: menuItems,
                        selected,
                        onPress: handleListItemPress,
                    })}
                </List>
            </Box>
        </Popover>
    );
}

export const Menu = styled(MenuComponent).withConfig({
    shouldForwardProp: shouldForwardMixinProps([boxMixinHelpers, backgroundMixinHelpers]),
})`
    ${boxMixin()}
    ${backgroundMixin()}
`;

Menu.defaultProps = {
    bgColor: 'backgroundPrimary',
    borderRadius: 12,
    maxHeight: 400,
};
