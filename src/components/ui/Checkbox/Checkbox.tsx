/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useCallback, useEffect, useMemo } from 'react';
import { Animated, Pressable, StyleProp, ViewStyle } from 'react-native';
import { useTheme } from 'styled-components/native';
import { Typography, TypographyProps } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { usePressable } from '~/hooks/usePressable';

export interface CheckboxProps {
    checked?: boolean;
    disablePadding?: boolean;
    label?: string;
    labelProps?: TypographyProps;
    noFeedback?: boolean;
    onPress?: (value: boolean) => void;
}

const useStyles = makeStyles(({ theme }) => ({
    checkbox: {},
    icon: {
        color: theme.palette.accent.contrast,
        left: -1,
        position: 'absolute',
        top: -1,
    },
    iconHolder: {
        borderRadius: theme.mixins.spacingValue(0.5),
        height: theme.mixins.spacingValue(3),
        width: theme.mixins.spacingValue(3),
    },
    pressedRoot: {
        backgroundColor: theme.palette.backgroundSecondary.main,
    },
    root: {
        alignContent: 'center',
        alignItems: 'center',
        borderRadius: theme.mixins.spacingValue(1),
        flexDirection: 'row',
    },
    rootPadding: {
        alignContent: 'center',
        alignItems: 'center',
        borderRadius: theme.mixins.spacingValue(1),
        flexDirection: 'row',
        height: theme.mixins.spacingValue(6),
        paddingHorizontal: theme.mixins.spacingValue(2),
        paddingVertical: theme.mixins.spacingValue(1),
    },
}));

export function Checkbox({
    noFeedback,
    label,
    checked,
    disablePadding,
    labelProps,
    onPress,
}: CheckboxProps): ReactElement {
    const theme = useTheme();
    const styles = useStyles();
    const [pressable, pressableProps] = usePressable();

    const [opacity, opacityHelpers] = useAnimatedValue(checked ? 1 : 0);
    const [background, backgroundHelpers] = useAnimatedValue(checked ? 1 : 0);

    const iconHolderStyle = useMemo(
        () => [
            {
                opacity: opacity.current,
            },
            styles.iconHolder,
        ],
        [opacity, styles.iconHolder],
    );

    const checkboxStyle = useMemo<StyleProp<ViewStyle>>(() => {
        const backgroundColor: any = background.current.interpolate({
            inputRange: [0, 1],
            outputRange: [theme.palette.backgroundTertiary.main, theme.palette.accent.main],
        });

        const borderColor: any = background.current.interpolate({
            inputRange: [0, 1],
            outputRange: [theme.palette.borderOpaque.main, theme.palette.accent.main],
        });

        return [
            styles.iconHolder,
            {
                backgroundColor,
                borderColor,
                borderStyle: 'solid',
                borderWidth: 1,
            },
        ];
    }, [
        background,
        styles.iconHolder,
        theme.palette.accent.main,
        theme.palette.backgroundTertiary.main,
        theme.palette.borderOpaque.main,
    ]);

    const handlePress = useCallback(() => {
        if (onPress) {
            onPress(!checked);
        }
    }, [checked, onPress]);

    useEffect(() => {
        Animated.parallel([
            opacityHelpers.timing(checked ? 1 : 0, {
                duration: 100,
            }),
            backgroundHelpers.timing(checked ? 1 : 0, {
                duration: 100,
                useNativeDriver: false,
            }),
        ]).start();
    }, [checked, backgroundHelpers, opacityHelpers]);

    return (
        <Pressable
            onPress={handlePress}
            style={[styles.root, !disablePadding && styles.rootPadding, pressable && !noFeedback && styles.pressedRoot]}
            {...pressableProps}
        >
            <Animated.View style={checkboxStyle}>
                <Animated.View style={iconHolderStyle}>
                    <MaterialIcons name={'check'} size={24} style={styles.icon} />
                </Animated.View>
            </Animated.View>
            {label ? (
                <Typography variant={'body'} fontWeight={500} ml={3} {...labelProps}>
                    {label}
                </Typography>
            ) : null}
        </Pressable>
    );
}
