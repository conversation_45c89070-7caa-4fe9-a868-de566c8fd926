/* eslint-disable @typescript-eslint/no-explicit-any */
import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { Checkbox, CheckboxProps } from './Checkbox';

interface FormikCheckboxProps extends CheckboxProps {
    name: string;
    value?: any;
}

export function FormikCheckbox({ name, value, ...rest }: FormikCheckboxProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<any[] | boolean>(name);
    const helpers = formik.getFieldHelpers<any[] | boolean>(name);

    const [values, isChecked] = useMemo(() => {
        if (Array.isArray(meta.value)) {
            const collection = meta.value || [];

            return [collection, collection.includes(value)];
        } else if (typeof meta.value === 'boolean') {
            return [meta.value, meta.value];
        }

        return [false, false];
    }, [meta.value, value]);

    // Keep it pure function, we don't need to mess up with cached values.
    function handlePress() {
        let newValues;

        if (Array.isArray(values)) {
            newValues = values;
            if (!isChecked) {
                newValues = [...newValues, value];
            } else {
                newValues = values.filter((v) => v !== value);
            }
        } else {
            newValues = !isChecked;
        }

        helpers.setTouched(true);
        helpers.setValue(newValues, true);
    }

    return <Checkbox checked={isChecked} onPress={handlePress} {...rest} />;
}
