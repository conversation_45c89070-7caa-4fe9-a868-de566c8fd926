/* eslint-disable @typescript-eslint/no-explicit-any */
import { castArray } from 'lodash';
import { ReactElement, ReactNode, RefObject, useContext, useMemo } from 'react';
import { Animated, ScrollView, ScrollViewProps, View, ViewProps } from 'react-native';
import { Edge, NativeSafeAreaViewProps, SafeAreaInsetsContext, SafeAreaView } from 'react-native-safe-area-context';
import styled, { useTheme } from 'styled-components/native';
import { shadow, ShadowMixinProps } from '~/components/ui/ThemeProvider/mixins/shadow';
import { sx } from '~/components/ui/makeStyles';
import { shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';
import { backgroundMixin, backgroundMixinHelpers, BackgroundMixinProps } from '../ThemeProvider/mixins/background';
import { boxMixin, boxMixinHelpers, BoxMixinProps } from '../ThemeProvider/mixins/box';
import { colorMixin, colorMixinHelpers } from '../ThemeProvider/mixins/color';
import { flexMixin, flexMixinHelpers, FlexMixinProps } from '../ThemeProvider/mixins/flex';
import { marginMixin, marginMixinHelpers, MarginMixinProps } from '../ThemeProvider/mixins/margin';
import { paddingMixin, paddingMixinHelpers, PaddingMixinProps } from '../ThemeProvider/mixins/padding';

export interface BoxProps
    extends Omit<ViewProps, 'children'>,
        BoxMixinProps,
        FlexMixinProps,
        MarginMixinProps,
        PaddingMixinProps,
        BackgroundMixinProps,
        ShadowMixinProps {
    animated?: boolean;
    children?: ReactNode | ReactNode[];
    disableSafeAreaFlex?: boolean;
    innerRef?: RefObject<View>;
    safeArea?: number | boolean;
    safeAreaEdges?: NativeSafeAreaViewProps['edges'] | Edge;
    safeAreaProps?: NativeSafeAreaViewProps;
    safeBottom?: number | boolean;
    safeTop?: number | boolean;
    scrollViewProps?: ScrollViewProps;
    scrollable?: boolean;
}

const AnimatedSafeAreaView = Animated.createAnimatedComponent(SafeAreaView);

function renderWithSafeArea(
    children: JSX.Element,
    { animated, safeArea, safeTop, safeBottom, insets, theme, disableSafeAreaFlex, safeAreaProps, safeAreaEdges }: any,
): ReactElement {
    const edges = castArray(safeAreaEdges);
    const SafeAreaViewComponent = animated ? AnimatedSafeAreaView : SafeAreaView;

    if (safeTop) {
        edges.push('top');
    }

    if (safeBottom) {
        edges.push('bottom');
    }

    const props: NativeSafeAreaViewProps = {
        edges: edges.filter(Boolean),
        style: sx(
            {
                flex: disableSafeAreaFlex ? 0 : 1,
            },
            !insets && {
                marginTop: theme.mixins.spacingValue(Number(safeTop || safeArea)),
                marginBottom: theme.mixins.spacingValue(Number(safeBottom || safeArea)),
            },
            safeAreaProps?.style,
        ),
    };

    return <SafeAreaViewComponent {...props}>{children}</SafeAreaViewComponent>;
}

function BoxComponent({
    safeBottom,
    safeTop,
    safeArea,
    safeAreaProps = {},
    scrollable,
    scrollViewProps = {},
    safeAreaEdges,
    animated,
    innerRef,
    disableSafeAreaFlex,
    ...rest
}: BoxProps): ReactElement {
    const ViewComponent = useMemo<any>(() => (animated ? Animated.View : View), [animated]);
    const ScrollViewComponent = useMemo<any>(() => (animated ? Animated.ScrollView : ScrollView), [animated]);
    const theme = useTheme();
    const insets = useContext(SafeAreaInsetsContext);

    rest.style = sx(rest.style, shadow({ theme, shadow: rest.shadow }));

    let children = <ViewComponent ref={innerRef} {...rest} />;
    if (scrollable) {
        scrollViewProps.style = [{ flex: 1 }, scrollViewProps?.style];
        children = <ScrollViewComponent {...scrollViewProps}>{children}</ScrollViewComponent>;
    }

    if (safeArea || safeBottom || safeTop) {
        children = renderWithSafeArea(children, {
            theme,
            animated,
            insets,
            safeTop,
            safeBottom,
            safeAreaProps,
            safeAreaEdges,
            disableSafeAreaFlex,
        });
    }

    return children;
}

export const Box = styled(BoxComponent).withConfig({
    displayName: 'Box',
    shouldForwardProp: shouldForwardMixinProps([
        colorMixinHelpers,
        boxMixinHelpers,
        flexMixinHelpers,
        marginMixinHelpers,
        paddingMixinHelpers,
        backgroundMixinHelpers,
    ]),
})`
    ${colorMixin()}
    ${boxMixin()}
    ${flexMixin()}
    ${marginMixin()}
    ${paddingMixin()}
    ${backgroundMixin()}
`;
