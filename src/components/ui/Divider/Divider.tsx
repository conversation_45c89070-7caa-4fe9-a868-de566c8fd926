import { ReactElement } from 'react';
import { View, ViewProps } from 'react-native';
import { makeStyles, Styles, sx } from '~/components/ui/makeStyles';
import { ThemeColorKeys } from '../ThemeProvider/Theme';
import { marginMixinHelpers, MarginMixinProps, margins } from '../ThemeProvider/mixins/margin';

export interface DividerProps extends ViewProps, MarginMixinProps {
    color?: ThemeColorKeys | string;
    grow?: boolean;
    size?: number;
}

export function Divider(props: DividerProps): ReactElement {
    const styles = useStyles(props);
    const { color, grow, style, ...rest } = props;

    marginMixinHelpers.omitProps(rest);

    return <View style={sx(styles.root, style)} {...rest} />;
}

const useStyles = makeStyles<DividerProps, Styles<'root'>>((props) => ({
    root: {
        borderBottomWidth: props.size ?? 1,
        borderBottomColor: props.theme.mixins.getColor(props.color, 'divider'),
        ...(props.grow ? { flexGrow: 1 } : { width: '100%' }),
        ...margins(props, { my: 1 }),
    },
}));
