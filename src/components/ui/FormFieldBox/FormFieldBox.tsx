import { ForwardedRef, forwardRef, ReactElement } from 'react';
import { GestureResponderEvent, Pressable, PressableProps, View } from 'react-native';
import { HStack, HStackProps } from '~/components/ui/Grid';
import { useEvent } from '~/hooks/useEvent';
import { usePressable } from '~/hooks/usePressable';

export type FormFieldBoxProps = HStackProps & {
    disabled?: boolean;
    onPress: PressableProps['onPress'];
};

function FormFieldBoxComponent(props: FormFieldBoxProps, ref: ForwardedRef<View>): ReactElement {
    const { onPress, disabled, ...rest } = props;
    const [isPressed, pressableProps] = usePressable(props);

    const handlePress = useEvent((event: GestureResponderEvent) => {
        if (!disabled) {
            onPress?.(event);
        }
    });

    return (
        <Pressable onPress={handlePress} ref={ref} disabled={disabled} {...pressableProps}>
            <HStack
                bgColor={isPressed ? 'backgroundTertiary' : 'backgroundSecondary'}
                borderRadius={12}
                px={3}
                py={1}
                borderColor={'borderOpaque'}
                minHeight={56}
                alignItems={'center'}
                flexWrap={'nowrap'}
                {...rest}
            />
        </Pressable>
    );
}

export const FormFieldBox = forwardRef<View, FormFieldBoxProps>(FormFieldBoxComponent);
