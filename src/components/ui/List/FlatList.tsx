import { BottomSheetFlatList } from '@gorhom/bottom-sheet';
import { Children, ReactElement, useCallback, useMemo } from 'react';
import { FlatList as RNFlatList, FlatListProps as RNFlatListProps, ListRenderItemInfo } from 'react-native';
import { ListItem, ListItemProps } from '~/components/ui/ListItem';

export type ListItemElement = ReactElement<ListItemProps, typeof ListItem>;

export interface FlatListProps extends Omit<RNFlatListProps<ListItemElement>, 'renderItem' | 'data'> {
    children: ListItemElement | ListItemElement[];
    inBottomSheet?: boolean;
}

export function FlatList({ children, inBottomSheet, ...rest }: FlatListProps): ReactElement {
    const data = useMemo(() => Children.toArray(children) as ListItemElement[], [children]);

    const renderItem = useCallback(({ item }: ListRenderItemInfo<ListItemElement>) => item, []);
    const Component = inBottomSheet ? BottomSheetFlatList : RNFlatList;
    return <Component data={data} renderItem={renderItem} {...rest} />;
}
