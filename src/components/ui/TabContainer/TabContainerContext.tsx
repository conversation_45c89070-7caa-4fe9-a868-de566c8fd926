import { createContext, useContext } from 'react';

export interface TabContainerContextType {
    currentTab: string;

    setCurrentTab(tab: string): void;
}

export const TabContainerContext = createContext<TabContainerContextType | null>(null);

export function useTabs(): TabContainerContextType | null {
    return useContext(TabContainerContext);
}

export const TabContainerContextProvider = TabContainerContext.Provider;
