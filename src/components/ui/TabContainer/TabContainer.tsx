import { ReactElement, ReactNode, useMemo, useState } from 'react';
import { useWatchValue } from '~/hooks/useWatch';
import { TabContainerContextProvider } from './TabContainerContext';

export interface TabContainerProps {
    children: ReactNode;
    value: string;
}

export function TabContainer({ value, children }: TabContainerProps): ReactElement {
    const [currentTab, setCurrentTab] = useState(value);

    const state = useMemo(
        () => ({
            currentTab,
            setCurrentTab,
        }),
        [currentTab],
    );

    useWatchValue(value, setCurrentTab);

    return <TabContainerContextProvider value={state}>{children}</TabContainerContextProvider>;
}
