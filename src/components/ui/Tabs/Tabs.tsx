import { ReactElement } from 'react';
import { ScrollView, ScrollViewProps } from 'react-native';
import { marginMixinHelpers, MarginMixinProps, margins } from '../ThemeProvider/mixins/margin';
import { makeStyles } from '../makeStyles';

export type TabsProps = ScrollViewProps & MarginMixinProps;

export function Tabs(props: TabsProps): ReactElement {
    const styles = useStyles(props);
    const { ...rest } = props;

    marginMixinHelpers.omitProps(rest);

    return (
        <ScrollView
            horizontal
            style={styles.root}
            contentContainerStyle={styles.content}
            showsHorizontalScrollIndicator={false}
            bounces={false}
            {...rest}
        />
    );
}

const useStyles = makeStyles((props) => ({
    root: {
        backgroundColor: props.theme.palette.backgroundTertiary.main,
        borderRadius: 8,
        overflow: 'hidden',
        width: '100%',
        ...margins(props),
    },
    content: {
        alignItems: 'center',
        flexDirection: 'row',
        minWidth: '100%',
        padding: props.theme.mixins.spacingValue(0.25),
    },
}));
