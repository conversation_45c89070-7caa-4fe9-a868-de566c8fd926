/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement } from 'react';
import { GestureResponderEvent, TextProps } from 'react-native';
import { Avatar, AvatarProps } from '../Avatar';

export interface MenuItemProps {
    avatar?: ReactElement<AvatarProps, typeof Avatar>;
    children: TextProps['children'];
    data?: any;
    onPress?: (event: GestureResponderEvent, value: MenuItemProps['value'], data: MenuItemProps['data']) => void;
    value: number | string | null;
}

/**
 * Modifier component, is not used to render anything,
 * but to provide props to the component
 * @param {MenuItemProps} props
 * @returns {null}
 * @constructor
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function MenuItem(props: MenuItemProps) {
    return null;
}

export type MenuItemElement = ReactElement<MenuItemProps, typeof MenuItem>;
