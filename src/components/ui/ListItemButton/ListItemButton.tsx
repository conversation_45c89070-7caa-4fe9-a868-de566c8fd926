import { ReactElement } from 'react';
import { usePressable } from '~/hooks/usePressable';
import { Link, LinkProps } from '../Link/Link';
import { ListItem, ListItemProps } from '../ListItem';

export type ListItemButtonProps = ListItemProps & LinkProps;

export function ListItemButton({ to, href, external, onPress, ...rest }: ListItemButtonProps): ReactElement {
    const [isPressed, pressableProps] = usePressable();

    return (
        <Link disableFeedback to={to} href={href} external={external} onPress={onPress} {...pressableProps}>
            <ListItem bgColor={isPressed ? 'backgroundTertiary' : 'transparent'} {...rest} />
        </Link>
    );
}
