import { ReactElement } from 'react';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { Grid, GridProps } from './Grid';

export interface ContainerProps extends GridProps {
    noPadding?: boolean;
}

export function Container({ children, noPadding, ...rest }: ContainerProps): ReactElement {
    const padding = useContainerPadding(noPadding);

    return (
        <Grid px={padding} {...rest}>
            {children}
        </Grid>
    );
}

Container.displayName = 'Container';
