import { storiesOf } from '@storybook/react-native';
import React, { Fragment } from 'react';
import { Paper } from '../Paper';
import { Screen } from '../Screen';
import { ThemeProvider } from '../ThemeProvider';
import { Typography } from '../Typography';
import { Grid } from './Grid';
import { HStack } from './HStack';
import { VStack } from './VStack';

function GridExample() {
    return (
        <VStack flexGrow={0}>
            <Grid container mt={3}>
                <Grid xs={12} p={0.25}>
                    <Typography color={'accent.contrast'} bgColor={'accent'} p={1}>
                        12
                    </Typography>
                </Grid>
                {new Array(6).fill(0).map((_, index) => (
                    <Fragment key={index}>
                        <Grid xs={index + 1} p={0.25}>
                            <Typography color={'accent.contrast'} bgColor={'accent'} p={1}>
                                {index + 1}
                            </Typography>
                        </Grid>
                        <Grid xs={12 - index - 1} p={0.25}>
                            <Typography color={'accent.contrast'} bgColor={'accent'} p={1}>
                                {12 - index - 1}
                            </Typography>
                        </Grid>
                    </Fragment>
                ))}

                <Grid xs p={0.25}>
                    <Typography color={'accent.contrast'} bgColor={'accent'} p={1}>
                        xs
                    </Typography>
                </Grid>

                <Grid xs p={0.25}>
                    <Typography color={'accent.contrast'} bgColor={'accent'} p={1}>
                        xs
                    </Typography>
                </Grid>

                <Grid xs p={0.25}>
                    <Typography color={'accent.contrast'} bgColor={'accent'} p={1}>
                        xs
                    </Typography>
                </Grid>
            </Grid>
            <VStack mt={4} xs={12}>
                <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} mb={1}>
                    Details
                </Typography>
                <Paper fullWidth p={1.5}>
                    <HStack>
                        <Typography variant={'footnote'} fontWeight={600} mr={0.8}>
                            Key:
                        </Typography>
                        <Typography variant={'footnote'} mr={0.8}>
                            Value
                        </Typography>
                    </HStack>
                </Paper>
            </VStack>
        </VStack>
    );
}

storiesOf('Grid', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    Grid
                </Typography>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500}>
                    Grid is a component that allows you to create nice and organized layouts.
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Grid example', GridExample);
