import { ReactElement } from 'react';
import { css } from 'styled-components';
import styled from 'styled-components/native';
import { Box, BoxProps } from '../Box';
import { mixinKeys, shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';

export interface GridProps extends BoxProps {
    container?: boolean;
    direction?: BoxProps['flexDirection'];
    xs?: number | boolean;
}

function GridComponent({ container, direction, ...rest }: GridProps): ReactElement {
    if (container) {
        if (!rest.flexWrap) {
            rest.flexWrap = 'wrap';
        }
        if (!rest.flexDirection) {
            rest.flexDirection = direction || 'row';
        }
    }

    return <Box {...rest} />;
}

const getWidth = ({ xs }: GridProps) => {
    if (!xs) {
        return '';
    }

    if (xs === true) {
        return css`
            flex-basis: 0;
            flex-grow: 1;
            flex-shrink: 1;
        `;
    }

    const size = xs <= 12 ? (xs * 100) / 12 : 100;

    return css`
        width: ${size}%;
        max-width: ${size}%;
        flex-basis: ${size}%;
    `;
};

GridComponent.displayName = 'GridComponent';

export const Grid = styled(GridComponent).withConfig({
    displayName: 'Grid',
    shouldForwardProp: shouldForwardMixinProps([mixinKeys('xs')]),
})`
    ${getWidth};
`;
