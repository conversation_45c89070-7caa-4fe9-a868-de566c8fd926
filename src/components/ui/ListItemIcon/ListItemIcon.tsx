import { IconElement, ResolveIconProps, useResolveIcon } from '~/hooks/useResolveIcon';
import { Grid, VStackProps } from '../Grid';

export interface ListItemIconProps extends VStackProps {
    children: IconElement;
    color?: ResolveIconProps['color'];
    size?: ResolveIconProps['size'];
}

export function ListItemIcon({ children, color, size, ...rest }: ListItemIconProps) {
    const iconElement = useResolveIcon(children, {
        color,
        size,
    });

    return (
        <Grid pr={2} width={40} {...rest}>
            {iconElement}
        </Grid>
    );
}
