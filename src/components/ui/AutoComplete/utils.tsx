/* eslint-disable @typescript-eslint/no-explicit-any */
import { isValidElement } from 'react';
import { Avatar } from '~/components/ui/Avatar';
import { ListItemAvatar } from '~/components/ui/ListItemAvatar';
import { ListItemText } from '~/components/ui/ListItemText';
import { AutoCompleteOptionType } from './AutoCompleteOptionType';

export function DefaultAutoCompleteItemRenderer(option: AutoCompleteOptionType<any>, item: any, selected: boolean) {
    return (
        <>
            {option.avatar ? (
                <ListItemAvatar>
                    {isValidElement(option.avatar) ? (
                        option.avatar
                    ) : (
                        <Avatar
                            size={32}
                            source={typeof option.avatar !== 'boolean' ? option.avatar : null}
                            name={option.label}
                        />
                    )}
                </ListItemAvatar>
            ) : null}
            <ListItemText
                primary={option.label}
                primaryTypographyProps={{
                    color: selected ? 'accent' : 'textPrimary',
                    fontWeight: 500,
                    variant: 'subhead',
                }}
            />
        </>
    );
}
