import { transparentize } from 'polished';
import { useCallback, useMemo } from 'react';
import { ViewProps } from 'react-native';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { AutoCompleteOptionType } from './AutoCompleteOptionType';

interface Props<T> extends ViewProps {
    disabledItem?: (option: AutoCompleteOptionType<T>, value: T) => boolean;
    onPress: (option: AutoCompleteOptionType<T>, value: T) => void;
    option: AutoCompleteOptionType<T>;
    renderItem: (option: AutoCompleteOptionType<T>, value: T, selected: boolean) => JSX.Element;
    selected: boolean;
}

const useStyles = makeStyles(({ theme }) => ({
    disabled: {
        opacity: 0.5,
    },
    selected: {
        backgroundColor: transparentize(0.95, theme.palette.accent.main),
    },
}));

export function AutoCompleteOption<T>({ option, onPress, renderItem, disabledItem, selected }: Props<T>) {
    const styles = useStyles();

    const disabled = useMemo(() => disabledItem?.(option, option.value), [disabledItem, option]);

    const children = useMemo(() => renderItem(option, option.value, selected), [option, renderItem, selected]);

    const handlePress = useCallback(() => onPress(option, option.value), [onPress, option]);

    return (
        <ListItemButton
            style={sx(disabled && styles.disabled, selected && styles.selected)}
            onPress={handlePress}
            pointerEvents={disabled ? 'none' : 'auto'}
        >
            {children}
        </ListItemButton>
    );
}
