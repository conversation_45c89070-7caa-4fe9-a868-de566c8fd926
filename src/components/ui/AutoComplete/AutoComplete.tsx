/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { debounce } from 'lodash';
import { ReactElement, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutAnimation } from 'react-native';
import { useQuery } from 'react-query';
import { BottomSheet } from '~/components/BottomSheet';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { Divider } from '~/components/ui/Divider';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { Container, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { FlatList, FlatListProps } from '~/components/ui/List';
import { ListItem } from '~/components/ui/ListItem';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { TextField, TextFieldAdornment, TextFieldProps } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useWatchValue } from '~/hooks/useWatch';
import { AutoCompleteOption } from './AutoCompleteOption';
import { AutoCompleteOptionType } from './AutoCompleteOptionType';
import { DefaultAutoCompleteItemRenderer } from './utils';

export interface AutoCompleteProps<T> extends Omit<TextFieldProps, 'value' | 'onChange'> {
    data?: Array<AutoCompleteOptionType<T>>;
    debounceTime?: number;
    disabledItem?: (option: AutoCompleteOptionType<T>, value: T) => boolean;
    id?: string;
    listProps?: Partial<FlatListProps>;
    loading?: boolean;
    loadingText?: string;
    noResultsText?: string;
    onChange?: (option: AutoCompleteOptionType<T> | null, value: T | null) => void;
    onFetch: (text: string) => unknown;
    onSearch?: (value: string) => void | Promise<void>;
    renderAddNewEntry?: (value: string) => JSX.Element;
    renderItem?: (option: AutoCompleteOptionType<T>, value: T, selected: boolean) => JSX.Element;
    value?: AutoCompleteOptionType<T>['id'] | null;
}

export function AutoComplete<T>({
    data,
    id,
    value,
    renderItem,
    disabledItem,
    loadingText,
    debounceTime,
    onSearch,
    onChange,
    onFetch,
    noResultsText,
    renderAddNewEntry,
    loading,
    listProps,
    ...rest
}: AutoCompleteProps<T>): ReactElement {
    const modal = useModal();
    const { t } = useTranslation();

    const [selectedId, setSelectedId] = useState(value);
    const [searchValue, setSearchValue] = useState<string>('');

    const debouncedFunc = useMemo(() => debounce((func: () => void) => func(), debounceTime || 300), [debounceTime]);

    const autoCompleteId = useMemo(() => id || `auto-complete-${Math.random()}-${new Date().getTime()}`, [id]);

    const { isFetching, refetch } = useQuery(autoCompleteId, () => onFetch(searchValue ?? ''));

    const selectedOption = useMemo(() => data?.find(({ id }) => id === selectedId), [data, selectedId]);

    const handleFieldFocus = useEvent(() => {
        refetch();
        modal.open();
    });

    const handleSearch = useCallback(
        (text: string) => {
            setSearchValue(text);
            debouncedFunc(() => refetch());
        },
        [debouncedFunc, refetch],
    );

    const handleClose = useEvent(() => {
        modal.close();
        setSearchValue('');
    });

    const handleOptionPress = useEvent((option: AutoCompleteOptionType<T> | null, value: T | null) => {
        setSelectedId(option?.id);
        handleClose();
        LayoutAnimation.easeInEaseOut();

        // TODO(BKR-335): Find a better way to solve JS blocking issue that occurs when the user clicks
        //                on the list item.
        setImmediate(() => onChange?.(option, value));
    });

    useWatchValue(selectedOption?.fieldLabel ?? selectedOption?.label, setSearchValue);
    useWatchValue(value, setSelectedId);

    const handleClear = useCallback(() => handleOptionPress(null, null), [handleOptionPress]);
    const handleClearSearch = useEvent(() => {
        handleSearch('');
        setSelectedId(null);
    });

    const selectedLabel = selectedOption?.fieldLabel || selectedOption?.label || '';
    const fieldLabel = selectedLabel || rest.label;

    return (
        <>
            <FormFieldBox onPress={handleFieldFocus} pl={2} pr={1.25} height={56} justifyContent={'center'} {...rest}>
                <VStack flexGrow justifyContent={'center'} flexWrap={'nowrap'}>
                    {selectedLabel ? (
                        <Typography variant="footnote" fontWeight={500} color="textSecondary" mb={0.25}>
                            {rest.label}
                        </Typography>
                    ) : null}
                    <Typography variant="subhead" fontWeight={500}>
                        {fieldLabel}
                    </Typography>
                </VStack>
                {!rest.disabled && selectedOption ? (
                    <IconButton onPress={handleClear} size={'small'} color={'typography.textSecondary'}>
                        <MaterialIcons name={'cancel'} />
                    </IconButton>
                ) : null}
            </FormFieldBox>
            <BottomSheet snapPoints={['75%']} {...modal.props}>
                <VStack flex>
                    <Container>
                        <TextField
                            value={searchValue}
                            onChangeText={handleSearch}
                            placeholder={t('typeToSearch')}
                            endAdornment={
                                selectedOption ? (
                                    <TextFieldAdornment variant="end">
                                        <IconButton
                                            disablePadding
                                            onPress={handleClearSearch}
                                            size={'small'}
                                            color={'typography.textSecondary'}
                                        >
                                            <MaterialIcons name={'cancel'} />
                                        </IconButton>
                                    </TextFieldAdornment>
                                ) : null
                            }
                        />
                    </Container>
                    <Divider mt={2.5} mb={0} />
                    <FlatList inBottomSheet {...listProps}>
                        {renderAddNewEntry && renderAddNewEntry(searchValue)}

                        {data && data?.length > 0 ? (
                            (data?.map((option) => (
                                <AutoCompleteOption
                                    key={option.id}
                                    selected={option.id === selectedId}
                                    option={option}
                                    renderItem={renderItem || DefaultAutoCompleteItemRenderer}
                                    onPress={handleOptionPress}
                                />
                            )) as any)
                        ) : (
                            <ListItem>
                                <ListItemText
                                    primary={noResultsText}
                                    primaryTypographyProps={{
                                        color: 'textSecondary',
                                        fontWeight: 500,
                                        variant: 'subhead',
                                    }}
                                />
                            </ListItem>
                        )}
                        {loading || isFetching ? (
                            <ListItem>
                                <ListItemIcon>
                                    <CircularProgress thickness={2} color={'accent'} />
                                </ListItemIcon>
                                {loadingText ? (
                                    <ListItemText
                                        primary={loadingText}
                                        primaryTypographyProps={{
                                            color: 'textSecondary',
                                            fontWeight: 500,
                                            variant: 'subhead',
                                        }}
                                    />
                                ) : null}
                            </ListItem>
                        ) : null}
                    </FlatList>
                </VStack>
            </BottomSheet>
        </>
    );
}
