import { ReactElement } from 'react';
import { Switch as RNSwitch, SwitchProps as RNSwitchProps } from 'react-native';
import { useTheme } from 'styled-components/native';

export type SwitchProps = RNSwitchProps;

export function Switch({ ...rest }: SwitchProps): ReactElement {
    const theme = useTheme();

    return (
        <RNSwitch
            thumbColor={theme.palette.backgroundPrimary.main}
            trackColor={{
                false: theme.palette.borderOpaque.main,
                true: theme.palette.accent.main,
            }}
            {...rest}
        />
    );
}
