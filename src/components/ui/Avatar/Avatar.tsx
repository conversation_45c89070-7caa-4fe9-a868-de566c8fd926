import { readableColor } from 'polished';
import React from 'react';
import { TouchableOpacity, View, ViewProps } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import styled, { useTheme } from 'styled-components/native';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { makeStyles } from '~/components/ui/makeStyles';
import { sx } from '~/components/ui/makeStyles/sx';
import { IconElement, useResolveIcon } from '~/hooks/useResolveIcon';
import { oneOf } from '~/lib/utils/oneOf';
import { VoidFunc } from '~/types/AnyFunc';
import { shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';
import { backgroundMixin, backgroundMixinHelpers, BackgroundMixinProps } from '../ThemeProvider/mixins/background';
import { boxMixin, boxMixinHelpers, BoxMixinProps } from '../ThemeProvider/mixins/box';
import { marginMixin, marginMixinHelpers, MarginMixinProps } from '../ThemeProvider/mixins/margin';
import { Initials } from '../Typography';
import { AvatarVariant, DefaultAvatarSize, getAvatarRadius, getAvatarSize } from './styles';

export interface AvatarProps extends ViewProps, BackgroundMixinProps, MarginMixinProps, BoxMixinProps {
    fontSize?: number;
    icon?: IconElement | null;
    iconColor?: ThemeColorKeys;
    iconSize?: number;
    name?: string;
    onPress?: VoidFunc;
    size?: number;
    source?: string | null;
    variant?: AvatarVariant;
}

function AvatarComponent(props: AvatarProps) {
    const {
        source,
        icon,
        name,
        size,
        fontSize,
        iconSize,
        iconColor,
        variant,
        children,
        style,
        bgColor,
        onPress,
        ...rest
    } = props;

    const styles = useStyles(props);
    const theme = useTheme();

    const icon$ = useResolveIcon(icon, {
        color:
            iconColor ??
            (bgColor
                ? theme.mixins.getContrastColor(
                      bgColor,
                      readableColor(theme.mixins.getColor(bgColor, theme.palette.typography.primary)),
                  )
                : theme.palette.typography.primary),
        size: iconSize || Math.floor((size ?? DefaultAvatarSize) * 0.34),
    });

    const content = (
        <>
            <View style={styles.innerView}>
                {oneOf(
                    source && (
                        <Image
                            tint={'dark'}
                            uri={source}
                            style={{
                                height: size,
                                width: size,
                            }}
                        />
                    ),
                    name && (
                        <Initials
                            fontSize={fontSize || Math.floor((size ?? DefaultAvatarSize) * 0.4)}
                            lineHeight={0}
                            fontWeight={600}
                            text={name}
                            count={2}
                        />
                    ),
                    icon$,
                )}
            </View>
            {children}
        </>
    );

    if (onPress) {
        return (
            <TouchableOpacity activeOpacity={0.7} onPress={onPress} style={sx(styles.root, style)}>
                {content}
            </TouchableOpacity>
        );
    }

    return (
        <View style={sx(styles.root, style)} {...rest}>
            {content}
        </View>
    );
}

const useStyles = makeStyles((props) => ({
    innerView: {
        ...getAvatarSize(props),
        ...getAvatarRadius(props),
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
    },
    root: {
        ...getAvatarSize(props),
        ...getAvatarRadius(props),
    },
}));

export const Avatar = styled(AvatarComponent).withConfig({
    shouldForwardProp: shouldForwardMixinProps(
        [backgroundMixinHelpers, marginMixinHelpers, boxMixinHelpers],
        ['bgColor'],
    ),
})`
    ${backgroundMixin()}
    ${marginMixin()}
    ${boxMixin()}
`;

Avatar.defaultProps = {
    bgColor: 'backgroundTertiary',
    variant: 'circle',
};
