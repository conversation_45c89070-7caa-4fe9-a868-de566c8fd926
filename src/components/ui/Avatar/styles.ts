import { ViewStyle } from 'react-native';
import { WithTheme } from '../ThemeProvider/Theme';

export type AvatarVariant = 'circle' | 'square' | 'rounded';

interface AvatarPartialProps {
    size?: number;
    variant?: AvatarVariant;
}

export const DefaultAvatarSize = 48;

export const getAvatarRadius = ({ size = DefaultAvatarSize, variant }: WithTheme<AvatarPartialProps>): ViewStyle => {
    switch (variant) {
        case 'circle':
            return {
                borderRadius: size / 2,
            };
        case 'rounded':
            return {
                borderRadius: 14,
            };
        default:
            return {};
    }
};

export const getAvatarSize = ({ size = DefaultAvatarSize }: WithTheme<AvatarPartialProps>): ViewStyle => {
    if (!size) {
        return {};
    }

    return {
        height: size,
        width: size,
    };
};
