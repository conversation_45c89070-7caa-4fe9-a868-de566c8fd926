/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement, useCallback, useEffect, useMemo } from 'react';
import { Animated, Pressable, StyleProp, ViewStyle } from 'react-native';
import { useTheme } from 'styled-components/native';
import { Typography, TypographyProps } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { usePressable } from '~/hooks/usePressable';

export interface RadioProps {
    checked?: boolean;
    label?: string | JSX.Element;
    onPress?: (value: boolean) => void;
    style?: ViewStyle;
    typographyProps?: TypographyProps;
}

const useStyles = makeStyles(({ theme }) => ({
    pressedRoot: {
        backgroundColor: theme.palette.backgroundSecondary.main,
    },
    radio: {
        height: 20,
        width: 20,
    },
    radioInner: {
        borderRadius: 5,
        height: 10,
        width: 10,
    },
    root: {
        alignContent: 'center',
        alignItems: 'center',
        borderRadius: theme.mixins.spacingValue(1),
        flexDirection: 'row',
        height: theme.mixins.spacingValue(6),
        paddingVertical: theme.mixins.spacingValue(1),
        paddingHorizontal: theme.mixins.spacingValue(2),
    },
}));

export function Radio({ label, checked, style, onPress, typographyProps }: RadioProps): ReactElement {
    const theme = useTheme();
    const styles = useStyles();
    const [pressable, pressableProps] = usePressable();

    const [background, backgroundHelpers] = useAnimatedValue(checked ? 1 : 0);

    const radioStyle = useMemo<StyleProp<ViewStyle>>(() => {
        const borderColor: any = background.current.interpolate({
            inputRange: [0, 1],
            outputRange: [theme.palette.borderOpaque.main, theme.palette.accent.main],
        });

        return [
            styles.radio,
            {
                alignItems: 'center',
                borderColor,
                borderRadius: theme.mixins.spacingValue(2),
                borderStyle: 'solid',
                borderWidth: 1,
                justifyContent: 'center',
            },
        ];
    }, [background, styles.radio, theme.mixins, theme.palette.accent.main, theme.palette.borderOpaque.main]);

    const radioInnerStyle = useMemo<StyleProp<ViewStyle>>(() => {
        const backgroundColor: any = background.current.interpolate({
            inputRange: [0, 1],
            outputRange: ['transparent', theme.palette.accent.main],
        });

        return [styles.radioInner, { backgroundColor }];
    }, [background, styles.radioInner, theme.palette.accent.main]);

    const handlePress = useCallback(() => {
        if (onPress) {
            onPress(!checked);
        }
    }, [checked, onPress]);

    useEffect(() => {
        Animated.parallel([
            backgroundHelpers.timing(checked ? 1 : 0, {
                duration: 100,
                useNativeDriver: false,
            }),
        ]).start();
    }, [checked, backgroundHelpers]);

    return (
        <Pressable
            onPress={handlePress}
            style={[styles.root, style, pressable && styles.pressedRoot]}
            {...pressableProps}
        >
            <Animated.View style={radioStyle}>
                <Animated.View style={radioInnerStyle} />
            </Animated.View>
            {typeof label === 'string' ? (
                <Typography fontSize={15} variant={'subhead'} fontWeight={500} ml={1.4} {...typographyProps}>
                    {label}
                </Typography>
            ) : (
                label
            )}
        </Pressable>
    );
}
