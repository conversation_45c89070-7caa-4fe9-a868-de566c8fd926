/* eslint-disable @typescript-eslint/no-explicit-any */
import { useFormikContext } from 'formik';
import { Radio, RadioProps } from './Radio';

interface FormikCheckboxProps extends RadioProps {
    name: string;
    value?: any;
}

export function FormikRadio({ name, value, ...rest }: FormikCheckboxProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<boolean>(name);
    const helpers = formik.getFieldHelpers<boolean>(name);

    const isChecked = meta.value === value;

    // Keep it pure function, we don't need to mess up with cached values.
    function handlePress() {
        helpers.setTouched(true);
        helpers.setValue(value, true);
    }

    return <Radio checked={isChecked} onPress={handlePress} {...rest} />;
}
