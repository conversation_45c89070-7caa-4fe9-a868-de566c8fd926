import { Slider, SliderProps } from '@miblanchard/react-native-slider';
import React from 'react';
import { useTheme } from 'styled-components/native';
import { SliderContainer } from '~/components/ui/Slider/SliderContainer';

interface RangeSliderProps extends SliderProps {
    // for left and right thumbs
    sliderValue?: number[];
    // for track marks on the slider, marked as pipes (min | | | | | max), cannot be used together with multiple thumbs
    trackMarks?: number[];
}

export const RangeSlider = ({ sliderValue, trackMarks, ...rest }: RangeSliderProps) => {
    const theme = useTheme();
    return (
        <SliderContainer sliderValue={sliderValue} trackMarks={trackMarks}>
            <Slider
                minimumTrackTintColor={theme.palette.primary.main}
                maximumTrackTintColor={'#e2e2e2'}
                thumbStyle={{
                    width: 28,
                    height: 28,
                    borderRadius: 14,
                    backgroundColor: theme.palette.secondary.main,
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2,
                }}
                {...rest}
            />
        </SliderContainer>
    );
};
