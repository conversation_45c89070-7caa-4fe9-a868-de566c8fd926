import { Slider } from '@miblanchard/react-native-slider';
import React, { Children, cloneElement, ReactElement, useState } from 'react';
import { View } from 'react-native';
import { makeStyles } from '~/components/ui/makeStyles';

const useTrackMarkStyles = makeStyles(({ theme }) => ({
    activeMark: {
        borderColor: theme.palette.primary.main,
        borderWidth: 1,
        height: 8,
        left: -0.5,
        borderRadius: 1,
    },
    inactiveMark: {
        borderColor: theme.palette.borderOpaque.main,
        borderWidth: 1,
        height: 8,
        left: -0.5,
        borderRadius: 1,
    },
}));

export const SliderContainer = (props: {
    children: ReactElement;
    sliderValue?: number[];
    trackMarks?: number[];
    vertical?: boolean;
}) => {
    const { sliderValue, trackMarks } = props;
    const DEFAULT_VALUE = 1;
    const [value, setValue] = useState(sliderValue ? sliderValue : DEFAULT_VALUE);
    let renderTrackMarkComponent: (index: number) => ReactElement;
    const trackMarkStyles = useTrackMarkStyles();

    if (trackMarks?.length && (!Array.isArray(value) || value?.length === 1)) {
        renderTrackMarkComponent = (index: number) => {
            const currentMarkValue = trackMarks[index];
            const currentSliderValue = value || (Array.isArray(value) && value[0]) || 0;
            const style =
                currentMarkValue > Math.max(currentSliderValue)
                    ? trackMarkStyles.activeMark
                    : trackMarkStyles.inactiveMark;
            return <View style={style} />;
        };
    }

    const renderChildren = () =>
        Children.map(props.children, (child: ReactElement) => {
            if (!!child && child.type === Slider) {
                return cloneElement(child, {
                    onValueChange: setValue,
                    renderTrackMarkComponent,
                    trackMarks,
                    value,
                });
            }

            return child;
        });

    return renderChildren();
};
