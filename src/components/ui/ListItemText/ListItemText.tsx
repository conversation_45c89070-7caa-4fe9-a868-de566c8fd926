import { ReactNode } from 'react';
import { Box } from '~/components/ui/Box';
import { GridProps } from '../Grid';
import { Typography, TypographyProps } from '../Typography';

export interface ListItemTextProps extends GridProps {
    primary?: string | ReactNode;
    primaryTypographyProps?: TypographyProps;

    secondary?: string | ReactNode;
    secondaryTypographyProps?: TypographyProps;
}

export function ListItemText({
    children,
    primary,
    secondary,
    primaryTypographyProps,
    secondaryTypographyProps,
    ...rest
}: ListItemTextProps) {
    return (
        <Box flexDirection={'column'} flexShrink={1} {...rest}>
            {primary && typeof primary === 'string' ? (
                <Box flexShrink={1}>
                    <Typography variant={'body'} {...primaryTypographyProps}>
                        {primary}
                    </Typography>
                </Box>
            ) : (
                primary ?? null
            )}
            {secondary && typeof secondary === 'string' ? (
                <Box>
                    <Typography variant={'caption1'} color={'textSecondary'} {...secondaryTypographyProps}>
                        {secondary}
                    </Typography>
                </Box>
            ) : (
                secondary ?? null
            )}
            {children}
        </Box>
    );
}
