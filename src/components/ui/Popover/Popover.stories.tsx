import { storiesOf } from '@storybook/react-native';
import { useRef, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Popover } from '~/components/ui/Popover';
import { Screen } from '~/components/ui/Screen';
import { ThemeProvider } from '~/components/ui/ThemeProvider';
import { Typography } from '~/components/ui/Typography';

function DefaultExample() {
    const anchorEl = useRef<TouchableOpacity>(null);
    const [open, setOpen] = useState(false);

    return (
        <VStack>
            <Button label={'Open Popover'} ref={anchorEl} onPress={() => setOpen(true)} />
            <Popover open={open} onClose={() => setOpen(false)} anchorEl={anchorEl}>
                <Paper>
                    <Typography>Hello from the Popover</Typography>
                    <Typography>Hello from the Popover</Typography>
                    <Typography>Hello from the Popover</Typography>
                    <Typography>Hello from the Popover</Typography>
                    <Typography>Hello from the Popover</Typography>
                </Paper>
            </Popover>
        </VStack>
    );
}

storiesOf('Popover', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    Popover
                </Typography>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={2}>
                    Popover description.
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Default', () => <DefaultExample />);
