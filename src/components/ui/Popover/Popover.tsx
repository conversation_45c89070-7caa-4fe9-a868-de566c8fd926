import { ReactElement, RefObject, useCallback, useEffect, useMemo, useState } from 'react';
import {
    LayoutChangeEvent,
    LayoutRectangle,
    Modal,
    ModalProps,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View,
    ViewStyle,
} from 'react-native';
import { Origin } from '~/components/types/Origin';
import { BackgroundMixinProps } from '~/components/ui/ThemeProvider/mixins/background';
import { BoxMixinProps } from '~/components/ui/ThemeProvider/mixins/box';
import { extendChildren } from '~/components/utils/extendChildren';
import { useAnimStyle } from '~/hooks/useAnimStyle';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useLayout, withMeasurement } from '~/hooks/useLayout';
import { num } from '~/lib/utils/number';
import { PopoverBackdrop } from './PopoverBackdrop';

export interface PopoverProps extends Omit<ModalProps, 'children'>, BoxMixinProps, BackgroundMixinProps {
    anchorEl?: RefObject<TouchableOpacity | View>;
    anchorOrigin?: Partial<Origin>;
    children: ReactElement;
    disableBackdrop?: boolean;
    onClose: () => void;
    open: boolean;
    transformOrigin?: Partial<Origin>;
}

export function Popover({
    open,
    anchorEl,
    anchorOrigin,
    transformOrigin,
    onClose,
    onLayout,
    children,
    disableBackdrop,
    ...rest
}: PopoverProps) {
    const [anchorMeasurement, setAnchorMeasurement] = useState<LayoutRectangle | null>(null);
    const [internalOpen, setInternalOpen] = useState(open);
    const layout = useLayout(onLayout, withMeasurement(anchorEl, setAnchorMeasurement));
    const contentLayout = useLayout();

    const handleClose = useCallback(() => onClose(), [onClose]);
    const [opacity, { timing }] = useAnimatedValue(0);

    const opacityStyle = useAnimStyle(() => ({ opacity }));

    const anchorStyle = useMemo<ViewStyle>(() => {
        const anchorOrigin$ = Origin.normalize(anchorOrigin);
        const minTop = layout.y;
        const minLeft = layout.x;
        const maxTop = num(anchorMeasurement?.y) + num(anchorMeasurement?.height) - num(layout.y);
        const maxLeft = num(anchorMeasurement?.x) + num(anchorMeasurement?.width) - num(layout.x);
        let top = num(anchorMeasurement?.y) - num(layout.y);
        let left = num(anchorMeasurement?.x) - num(layout.x);

        if (anchorOrigin$.isBottom()) {
            top += num(anchorMeasurement?.height);
        } else if (anchorOrigin$.isVerticalCenter()) {
            top += num(anchorMeasurement?.height) / 2;
        }

        if (anchorOrigin$.isRight()) {
            left += num(anchorMeasurement?.width);
        } else if (anchorOrigin$.isHorizontalCenter()) {
            left += num(anchorMeasurement?.width) / 2;
        }

        // TODO: implement offscreen positioning checks

        return {
            left: Math.min(Math.max(minLeft, left), maxLeft),
            position: 'absolute',
            top: Math.min(Math.max(minTop, top), maxTop),
            zIndex: 10,
        };
    }, [
        anchorOrigin,
        anchorMeasurement?.y,
        anchorMeasurement?.height,
        anchorMeasurement?.x,
        anchorMeasurement?.width,
        layout.y,
        layout.x,
    ]);

    const contentStyle = useMemo<ViewStyle>(() => {
        const transformOrigin$ = Origin.normalize(transformOrigin);
        let top = 0;
        let left = 0;

        if (transformOrigin$.isBottom()) {
            top -= contentLayout.height;
        } else if (transformOrigin$.isVerticalCenter()) {
            top -= contentLayout.height / 2;
        }

        if (transformOrigin$.isRight()) {
            left -= contentLayout.width;
        } else if (transformOrigin$.isHorizontalCenter()) {
            left -= contentLayout.width / 2;
        }

        const style: ViewStyle = {
            left,
            position: 'absolute',
            top,
        };

        if (anchorMeasurement?.width) {
            style.width = anchorMeasurement.width;
        }

        if (layout.width) {
            style.maxWidth = layout.width;
        }

        return style;
    }, [anchorMeasurement?.width, contentLayout.height, contentLayout.width, layout.width, transformOrigin]);

    const children$ = extendChildren(children, (rest) => ({
        ...rest,
        onLayout: (event: LayoutChangeEvent) => {
            contentLayout.handler(event);
            rest.onLayout?.(event);
        },
        style: [rest.style, contentStyle],
    }));

    useEffect(() => {
        if (open) {
            setInternalOpen(true);
        }

        setTimeout(() => timing(open ? 1 : 0).start(() => setInternalOpen(open)), open ? 100 : 0);
    }, [open, timing]);

    return (
        <Modal transparent visible={internalOpen} animationType={'none'} {...rest}>
            <PopoverBackdrop onPress={handleClose} style={opacityStyle} disabled={disableBackdrop}>
                <SafeAreaView style={styles.flex}>
                    <View onLayout={layout.handler} style={styles.flex}>
                        <View style={anchorStyle}>{children$}</View>
                    </View>
                </SafeAreaView>
            </PopoverBackdrop>
        </Modal>
    );
}

const styles = StyleSheet.create({
    flex: {
        flex: 1,
    },
});
