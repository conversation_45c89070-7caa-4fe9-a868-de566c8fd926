import { ReactElement } from 'react';
import { Typography, TypographyProps } from '~/components/ui/Typography';
import { Grid, GridProps } from '../Grid';

export type PaperProps = Omit<GridProps, 'xs'> & {
    label?: string;
    labelProps?: TypographyProps;
};

export function Paper({ children, direction, label, labelProps, ...rest }: PaperProps): ReactElement {
    return (
        <>
            {label && (
                <Typography mb={0.5} variant={'footnote'} fontWeight={500} color={'textSecondary'} {...labelProps}>
                    {label}
                </Typography>
            )}
            <Grid
                container
                direction={direction || 'column'}
                borderRadius={12}
                bgColor={'backgroundSecondary'}
                {...rest}
            >
                {children}
            </Grid>
        </>
    );
}
