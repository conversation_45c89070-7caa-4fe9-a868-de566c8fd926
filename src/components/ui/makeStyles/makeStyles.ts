/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { DefaultTheme, useTheme } from 'styled-components/native';
import { value } from '~/lib/value';

export type Styles<T extends string> = StyleSheet.NamedStyles<Record<T, never>>;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function makeStyles<P extends object, T extends StyleSheet.NamedStyles<T> | StyleSheet.NamedStyles<any>>(
    styles: T | ((props: P & { theme: DefaultTheme }) => T),
) {
    return (props?: P, customStyles?: Partial<T>): T => {
        const theme = useTheme();
        return useMemo(() => {
            const rootStyles = StyleSheet.create(value(styles, { ...(props || {}), theme }) as any);

            return Object.keys(rootStyles as T).reduce((acc, key) => {
                if (acc && acc[key as keyof T] && customStyles && customStyles[key as keyof T]) {
                    return { ...acc, [key]: StyleSheet.compose(acc[key as keyof T], customStyles[key as keyof T]) };
                }

                return acc;
            }, rootStyles as T);
        }, [customStyles, props, theme]);
    };
}
