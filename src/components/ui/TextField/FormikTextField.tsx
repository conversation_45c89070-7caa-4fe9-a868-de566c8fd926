import { useFormikContext } from 'formik';
import { ReactElement } from 'react';
import { TextField, TextFieldProps } from './TextField';

export interface FormikTextFieldProps extends Omit<TextFieldProps, 'value' | 'defaultValue' | 'error'> {
    name: string;
}

export function FormikTextField({ name, helperText, disabled, ...rest }: FormikTextFieldProps): ReactElement {
    const { getFieldMeta, isSubmitting, handleBlur, handleChange } = useFormikContext();
    const { value, error, touched } = getFieldMeta<string>(name);
    const hasError = !!error && touched;

    return (
        <TextField
            disabled={disabled || isSubmitting}
            value={value}
            error={hasError}
            helperText={hasError ? error : helperText}
            onChangeText={handleChange(name)}
            onBlur={handleBlur(name)}
            {...rest}
        />
    );
}
