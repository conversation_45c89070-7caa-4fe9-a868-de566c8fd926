import { forwardRef, isValidElement, ReactElement, ReactNode, RefObject, useCallback, useMemo, useState } from 'react';
import { NativeSyntheticEvent, TextInput, TextInputFocusEventData, View } from 'react-native';
import { useTheme } from 'styled-components/native';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { Grid, HStack } from '../Grid';
import { marginMixinHelpers, MarginMixinProps } from '../ThemeProvider/mixins/margin';
import { Typography } from '../Typography';
import { TextFieldAdornment, TextFieldAdornmentProps } from './TextFieldAdornment';
import { TextFieldInput, TextFieldInputProps } from './TextFieldInput';

export interface TextFieldProps extends TextFieldInputProps, MarginMixinProps {
    endAdornment?: ReactElement<TextFieldAdornmentProps, typeof TextFieldAdornment> | null;
    helperText?: ReactNode | string | null;
    innerRef?: RefObject<View>;
    startAdornment?: ReactElement<TextFieldAdornmentProps, typeof TextFieldAdornment> | null;
    type?: 'text' | 'password' | 'email' | 'number' | 'numeric' | 'tel' | 'url';
}

export const TextField = forwardRef<TextInput, TextFieldProps>(function TextField(props, ref): ReactElement {
    const {
        autoFocus,
        startAdornment,
        endAdornment,
        helperText,
        error,
        success,
        style,
        onFocus,
        onBlur,
        innerRef,
        type = 'text',
        ...rest
    } = props;
    const styles = useStyles(props);
    const theme = useTheme();
    const [isFocused, setIsFocused] = useState(false);

    const rootStyle = useMemo(() => {
        let borderColor = isFocused || success ? theme.palette.accent.main : theme.palette.borderOpaque.main;
        if (rest.focused || isFocused || success) {
            borderColor = theme.palette.accent.main;
        }

        if (error) {
            borderColor = theme.palette.error.main;
        }

        return [
            {
                borderColor,
            },
            styles.root,
            style,
        ];
    }, [
        error,
        isFocused,
        rest.focused,
        style,
        styles.root,
        success,
        theme.palette.accent.main,
        theme.palette.borderOpaque.main,
        theme.palette.error.main,
    ]);

    const helperTextColor = useMemo(() => {
        if (error) {
            return 'error';
        }

        if (success) {
            return 'accent';
        }
        return 'textSecondary';
    }, [error, success]);

    const handleInputFocus = useCallback(
        (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            setIsFocused(true);
            onFocus && onFocus(e);
        },
        [onFocus],
    );

    const handleInputBlur = useCallback(
        (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            setIsFocused(false);
            onBlur && onBlur(e);
        },
        [onBlur],
    );

    const margins = marginMixinHelpers.getProps(rest);

    extendFieldProps(type, rest);

    return (
        <Grid direction={'column'} width={'100%'} innerRef={innerRef} {...margins}>
            <HStack style={rootStyle} alignItems={rest.multiline ? 'flex-start' : 'center'} flexWrap={'nowrap'}>
                {startAdornment ?? null}
                <TextFieldInput
                    success={success}
                    error={error}
                    focused={isFocused}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    placeholderTextColor={isFocused ? 'transparent' : theme.palette.contentSecondary.main}
                    // eslint-disable-next-line jsx-a11y/no-autofocus
                    autoFocus={autoFocus}
                    textInputRef={ref}
                    {...rest}
                />
                {endAdornment ?? null}
            </HStack>
            {helperText && typeof helperText === 'string' ? (
                <Typography
                    variant={'footnote'}
                    color={helperTextColor}
                    fontWeight={500}
                    mt={0.5}
                    px={0.5}
                    lineHeight={'24px'}
                >
                    {helperText}
                </Typography>
            ) : null}
            {helperText && isValidElement(helperText) ? helperText : null}
        </Grid>
    );
});

function extendFieldProps(type: TextFieldProps['type'], rest: TextFieldProps): void {
    if (type === 'email') {
        rest.keyboardType = 'email-address';
        rest.autoCapitalize = 'none';
        rest.textContentType = 'emailAddress';
        rest.spellCheck = false;
    }

    if (type === 'password') {
        rest.secureTextEntry = true;
    }

    if (type === 'number') {
        rest.keyboardType = 'number-pad';
    }

    if (type === 'numeric') {
        rest.keyboardType = 'numeric';
    }
}

const useStyles = makeStyles<TextFieldProps, Styles<'root'>>(
    ({ theme, endAdornment, startAdornment, success, error }) => ({
        root: {
            paddingLeft: startAdornment ? 0 : theme.mixins.spacingValue(3),
            paddingRight: endAdornment ? 0 : theme.mixins.spacingValue(3),
            borderRadius: 12,
            backgroundColor: theme.palette.backgroundSecondary.main,
            borderWidth: success || error ? 2 : 1,
        },
    }),
);
