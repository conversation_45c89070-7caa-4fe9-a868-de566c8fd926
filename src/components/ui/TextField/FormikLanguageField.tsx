/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useFormikContext } from 'formik';
import { ReactElement, useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GestureResponderEvent, Pressable, TouchableOpacity, View } from 'react-native';
import { Avatar } from '~/components/ui/Avatar';
import { HStack } from '~/components/ui/Grid';
import { Menu } from '~/components/ui/Menu';
import { MenuItem } from '~/components/ui/MenuItem';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';

export interface FormikLanguageFieldProps {
    name: string;
}

export const AvailableLanguages = [
    { code: 'en-EN', flag: 'https://flagcdn.com/h80/gb.png' },
    { code: 'en-US', flag: 'https://flagcdn.com/h80/us.png' },
    { code: 'ro-RO', flag: 'https://flagcdn.com/h80/ro.png' },
];

export function FormikLanguageField({ name }: FormikLanguageFieldProps): ReactElement {
    const { getFieldMeta, getFieldHelpers } = useFormikContext();
    const { value } = getFieldMeta<string>(name);
    const helpers = getFieldHelpers<string>(name);
    const styles = useStyles();
    const { t } = useTranslation();

    const anchorEl = useRef<TouchableOpacity>(null);
    const [open, setOpen] = useState(false);

    const selectedLang = useMemo(
        () => AvailableLanguages.find((lang) => lang.code === value) || AvailableLanguages[0],
        [value],
    );

    const handlePressItem = useCallback(
        (event: GestureResponderEvent, value: any) => {
            helpers.setValue(value, true);
            helpers.setTouched(true, true);
        },
        [helpers],
    );

    return (
        <>
            <Pressable ref={anchorEl} style={styles.pressable} onPress={(): void => setOpen(true)}>
                <View style={styles.root}>
                    <HStack flexGrow alignItems={'center'}>
                        <Avatar size={34} source={selectedLang.flag} />
                        <Typography variant={'subhead'} fontWeight={500} ml={2}>
                            {t(selectedLang.code as any)}
                        </Typography>
                    </HStack>
                    <MaterialCommunityIcons name={'chevron-down'} size={24} />
                </View>
            </Pressable>
            <Menu
                open={open}
                onClose={(): void => setOpen(false)}
                anchorEl={anchorEl}
                anchorOrigin={{ vertical: 'bottom' }}
            >
                {AvailableLanguages.map(({ code, flag }) => (
                    <MenuItem
                        key={code}
                        onPress={handlePressItem}
                        value={code}
                        avatar={<Avatar size={24} source={flag} />}
                    >
                        {t(code as any)}
                    </MenuItem>
                ))}
            </Menu>
        </>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    pressable: {
        marginBottom: theme.mixins.spacingValue(2),
    },
    root: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: theme.mixins.spacingValue(1.5),
        borderStyle: 'solid',
        borderWidth: 1,
        flexDirection: 'row',
        height: theme.mixins.spacingValue(7),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(1.5),
    },
}));
