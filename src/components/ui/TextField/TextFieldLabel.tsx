/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Animated, LayoutChangeEvent, LayoutRectangle } from 'react-native';
import { withAnchorPoint } from 'react-native-anchor-point';
import styled, { useTheme } from 'styled-components/native';
import { ThemeColorKeys } from '../ThemeProvider/Theme';
import { Typography, TypographyProps } from '../Typography';

export interface TextFieldLabelProps extends Partial<TypographyProps> {
    disabled?: boolean;
    error?: boolean;
    filled?: boolean;
    focused?: boolean;
    success?: boolean;
}

function TextFieldLabelComponent({ focused, filled, error, success, disabled, style, ...rest }: TextFieldLabelProps) {
    const theme = useTheme();
    const [labelLayout, setLabelLayout] = useState<LayoutRectangle | null>(null);

    const labelTranslateY = useMemo(() => {
        const size = filled ? 1 : 2;
        return theme.mixins.spacingValue(size - (!filled && (success || error) ? 0.25 : 0));
    }, [error, filled, success, theme.mixins]);
    const labelScale = useMemo(() => (filled ? 0.75 : 1), [filled]);

    const animatedLabelRef = useRef({
        scale: new Animated.Value(labelScale),
        translateY: new Animated.Value(labelTranslateY),
    });

    const labelStyle = useMemo(
        () =>
            withAnchorPoint(
                {
                    transform: [
                        {
                            translateY: animatedLabelRef.current.translateY as any,
                        },
                        {
                            scale: animatedLabelRef.current.scale as any,
                        },
                    ],
                },
                { x: 0, y: 0 },
                { height: labelLayout?.height ?? 0, width: labelLayout?.width ?? 0 },
            ),
        [labelLayout],
    );

    const labelColor = useMemo<ThemeColorKeys>(() => {
        if (disabled) {
            return 'typography.disabled';
        }
        if (error) {
            return 'error';
        }

        if (focused) {
            return 'accent';
        }

        return 'contentTertiary';
    }, [disabled, error, focused]);

    const handleLabelLayout = useCallback((e: LayoutChangeEvent) => {
        setLabelLayout(e.nativeEvent.layout);
    }, []);

    useEffect(() => {
        if (rest.children) {
            Animated.parallel([
                Animated.timing(animatedLabelRef.current.translateY, {
                    duration: 200,
                    toValue: labelTranslateY,
                    useNativeDriver: true,
                }),
                Animated.timing(animatedLabelRef.current.scale, {
                    duration: 200,
                    toValue: labelScale,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [rest.children, filled, focused, labelTranslateY, labelScale]);

    return (
        <Typography
            animated
            variant={'body'}
            color={labelColor}
            style={[style, labelStyle]}
            onLayout={handleLabelLayout}
            fontWeight={500}
            {...rest}
        />
    );
}

export const TextFieldLabel = styled(TextFieldLabelComponent)`
    position: absolute;
    top: 0;
    left: -0.5px;
    z-index: -1;
`;
