import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import { Ref, useCallback, useMemo, useState } from 'react';
import { TextInput, TextInputProps } from 'react-native';
import { makeStyles, Styles, sx } from '~/components/ui/makeStyles';
import { useWatchValue } from '~/hooks/useWatch';
import { VStack } from '../Grid';
import { ThemeColorKeys } from '../ThemeProvider/Theme';
import { TextFieldLabel } from './TextFieldLabel';

export interface TextFieldInputProps extends TextInputProps {
    bottomSheetField?: boolean;
    disabled?: boolean;
    error?: boolean;
    focused?: boolean;
    label?: string;
    minHeight?: number;
    success?: boolean;
    textInputRef?: Ref<TextInput>;
}

export function TextFieldInput({
    autoFocus,
    focused,
    error,
    success,
    disabled,
    style,
    value,
    defaultValue,
    onChangeText,
    label,
    textInputRef,
    bottomSheetField,
    ...rest
}: TextFieldInputProps) {
    const [internalValue, setInternalValue] = useState(() => value || defaultValue || '');

    const inputColor = useMemo(() => {
        if (disabled) {
            return 'disabled';
        }
        return 'textPrimary';
    }, [disabled]);

    const hasValue = !!internalValue;

    const styles = useStyles({
        color: inputColor,
        cutBorders: success || error ? 0.5 : 0.25,
        focused: hasValue || focused,
        hasLabel: !!label,
    });

    const handleInputChangeText = useCallback(
        (text: string) => {
            onChangeText && onChangeText(text);
            setInternalValue(text);
        },
        [onChangeText],
    );

    useWatchValue(value, setInternalValue);

    const Component = bottomSheetField ? BottomSheetTextInput : TextInput;

    return (
        <VStack alignContent={'center'} style={sx(styles.root, style)}>
            {label ? (
                <TextFieldLabel
                    error={error}
                    success={success}
                    disabled={disabled}
                    focused={focused}
                    filled={hasValue || focused}
                >
                    {label}
                </TextFieldLabel>
            ) : null}
            <Component
                // eslint-disable-next-line jsx-a11y/no-autofocus
                autoFocus={autoFocus}
                onChangeText={handleInputChangeText}
                value={internalValue}
                focusable={!disabled}
                editable={!disabled}
                selectTextOnFocus={!disabled}
                style={styles.input}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                ref={textInputRef as any}
                {...rest}
            />
        </VStack>
    );
}

const useStyles = makeStyles<
    TextInputProps &
        Pick<TextFieldInputProps, 'focused' | 'minHeight'> & {
            color: ThemeColorKeys | string;
            cutBorders: number;
            hasLabel: boolean;
        },
    Styles<'root' | 'input'>
>(({ theme, minHeight, cutBorders, hasLabel, color }) => ({
    input: {
        color: theme.mixins.getColor(`typography.${color || 'textPrimary'}`),
        minHeight: minHeight || theme.mixins.spacingValue(7 - cutBorders),
        paddingBottom: theme.mixins.spacingValue((hasLabel ? 1 : 2) - cutBorders),
        paddingTop: theme.mixins.spacingValue((hasLabel ? 3 : 2) - cutBorders),
        width: '100%',

        ...theme.mixins.typographyObject('subhead', { fontWeight: 500 }),
    },

    root: {
        flexGrow: 1,
        flexShrink: 1,
        zIndex: 3,
    },
}));
