import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { storiesOf } from '@storybook/react-native';
import React from 'react';
import { HStack, VStack } from '../Grid';
import { Screen } from '../Screen';
import { ThemeProvider } from '../ThemeProvider';
import { Typography } from '../Typography';
import { TextField } from './TextField';
import { TextFieldAdornment } from './TextFieldAdornment';

function Default() {
    return (
        <VStack mt={3}>
            <HStack mb={2}>
                <TextField
                    label={'With Start adornment'}
                    startAdornment={
                        <TextFieldAdornment variant={'start'}>
                            <MaterialIcons name={'search'} size={24} />
                        </TextFieldAdornment>
                    }
                />
            </HStack>
            <HStack mb={2}>
                <TextField
                    label={'With End adornment'}
                    endAdornment={
                        <TextFieldAdornment variant={'end'}>
                            <MaterialIcons name={'search'} size={24} />
                        </TextFieldAdornment>
                    }
                />
            </HStack>
            <HStack mb={2}>
                <TextField />
            </HStack>
            <HStack mb={2}>
                <TextField value={'With value but no label'} />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Type your email'} />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Filled Text Field'} value={'Test'} />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Disabled Text Field'} disabled />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Disabled Filled Text Field'} value={'Test'} disabled />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Caption Text Field'} helperText={"Here's a hint that might help you."} />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Success Text Field'} helperText={"Here's a hint that might help you."} success />
            </HStack>
            <HStack mb={2}>
                <TextField label={'Error Text Field'} helperText={"Here's a hint that might help you."} error />
            </HStack>
        </VStack>
    );
}

storiesOf('Forms', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    Forms - Text fields
                </Typography>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500}>
                    Used to collect information from the user, text field is always a must have component for any
                    application.
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Default', Default);
