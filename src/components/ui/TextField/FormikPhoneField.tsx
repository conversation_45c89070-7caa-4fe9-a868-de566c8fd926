import { useFormikContext } from 'formik';
import { PhoneInput, PhoneInputProps } from '~/components/PhoneInput';
import { makeStyles } from '~/components/ui/makeStyles';

export interface FormikPhoneFieldProps extends Omit<PhoneInputProps, 'value' | 'onChangePhoneNumber'> {
    name: string;
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: theme.mixins.spacingValue(1.5),
        borderStyle: 'solid',
        borderWidth: 1,
        height: theme.mixins.spacingValue(7),
        marginBottom: theme.mixins.spacingValue(2),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(1),
    },
    text: {
        color: theme.palette.typography.textPrimary,
        fontFamily: theme.mixins.getTypographyFamily({
            fontWeight: 500,
            variant: 'subhead',
        }),
        fontSize: Number(theme.typography.variants.subhead.fontSize),
    },
}));

export function FormikPhoneField({ name, ...rest }: FormikPhoneFieldProps) {
    const { getFieldMeta, handleChange } = useFormikContext();
    const { value } = getFieldMeta<string>(name);
    const styles = useStyles();

    return (
        <PhoneInput
            initialCountry={'RO'}
            value={value}
            onChangePhoneNumber={handleChange(name)}
            style={styles.root}
            textStyle={styles.text}
            {...rest}
        />
    );
}
