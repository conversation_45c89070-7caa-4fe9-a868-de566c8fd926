/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { DefaultTheme } from 'styled-components';
import { ThemeColor } from './Theme';
import { createMixins } from './createMixins';
import { debug } from './debug';
import { createThemeColor, createThemeColorWithShades } from './utils';

interface CreateThemeOptions {
    mode?: DefaultTheme['mode'];
    palette?: Partial<{
        [K in keyof DefaultTheme['palette']]: DefaultTheme['palette'][K] extends ThemeColor
            ? DefaultTheme['palette'][K] | string
            : DefaultTheme['palette'][K];
    }>;
    shadows?: Partial<DefaultTheme['shadows']>;

    spacingOptions?: Partial<DefaultTheme['spacingOptions']>;
    typography?: Partial<{
        fontFamilyWeightMap: Partial<DefaultTheme['typography']['fontFamilyWeightMap']>;
        variants: Partial<DefaultTheme['typography']['variants']>;
    }>;
}

export function createTheme(options?: CreateThemeOptions): DefaultTheme {
    const palette = options?.palette ?? {};
    const typographyVariants = options?.typography?.variants ?? {};
    const typographyWeightMap = options?.typography?.fontFamilyWeightMap ?? {};

    const theme: Partial<DefaultTheme> = {
        mode: options?.mode || 'light',
        palette: {
            accent: createThemeColorWithShades(palette.accent ?? '#2f80fb', '#fff'),
            backgroundPrimary: createThemeColor(palette.backgroundPrimary ?? '#fff'),
            backgroundSecondary: createThemeColor(palette.backgroundSecondary ?? '#f6f6f6'),
            backgroundTertiary: createThemeColor(palette.backgroundTertiary ?? '#eee'),
            borderOpaque: createThemeColor(palette.borderOpaque ?? '#ccc'),
            borderSelected: createThemeColor(palette.borderSelected ?? '#111'),
            contentPrimary: createThemeColor(palette.contentPrimary ?? '#111'),
            contentSecondary: createThemeColor(palette.contentSecondary ?? '#545454'),
            contentTertiary: createThemeColor(palette.contentTertiary ?? '#757575'),
            divider: createThemeColor(palette.divider ?? '#eee'),
            error: createThemeColorWithShades(palette.error ?? '#f05253', '#fff'),
            primary: createThemeColorWithShades(palette.primary ?? '#111'),
            secondary: createThemeColorWithShades(palette.secondary ?? '#fff'),
            success: createThemeColorWithShades(palette.success ?? '#05944f', '#fff'),
            typography: {
                accent: '#2f80fb',
                disabled: '#AFAFAF',
                error: '#f05253',
                primary: '#111',
                secondary: '#fff',
                success: '#05944f',
                textPrimary: '#111',
                textSecondary: '#757575',
                warning: '#ffc043',
                ...(palette.typography ?? {}),
            },
            warning: createThemeColorWithShades(palette.warning ?? '#ffc043', '#fff'),
        },
        shadows: {
            ...(options?.shadows ?? {}),
            medium: {
                elevation: 12,
                shadowColor: '#000000',
                shadowOffset: {
                    height: 11,
                    width: 0,
                },
                shadowOpacity: 0.1,
                shadowRadius: 20,
            },
            small: {
                elevation: 4,
                shadowColor: '#051037',
                shadowOffset: {
                    height: 2,
                    width: 0,
                },
                shadowOpacity: 0.1,
                shadowRadius: 6,
            },
        },
        spacingOptions: {
            size: 8,
            unit: 'px',
            ...(options?.spacingOptions ?? {}),
        },
        typography: {
            fontFamilyWeightMap: {
                '400': 'PlusJakartaSans-Regular',
                '500': 'PlusJakartaSans-Medium',
                '600': 'PlusJakartaSans-SemiBold',
                '700': 'PlusJakartaSans-Bold',
                ...typographyWeightMap,
            },
            variants: {
                body: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 16,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 22,
                    ...(typographyVariants.body ?? {}),
                },
                button: {
                    fontFamily: 'PlusJakartaSans-SemiBold',
                    fontSize: 16,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 20,
                    ...(typographyVariants.button ?? {}),
                },
                callout: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 17,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 21,
                    ...(typographyVariants.callout ?? {}),
                },
                caption1: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 13,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 16,
                    ...(typographyVariants.caption1 ?? {}),
                },
                caption2: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 11,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 14,
                    ...(typographyVariants.caption2 ?? {}),
                },
                footnote: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 13,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 17,
                    ...(typographyVariants.footnote ?? {}),
                },
                largeTitle: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 34,
                    fontWeight: 400,
                    letterSpacing: -0.24,
                    lineHeight: 41,
                    ...(typographyVariants.largeTitle ?? {}),
                },
                subhead: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 15,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 18,
                    ...(typographyVariants.subhead ?? {}),
                },
                title1: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 28,
                    fontWeight: 400,
                    letterSpacing: -0.08,
                    lineHeight: 36,
                    ...(typographyVariants.title1 ?? {}),
                },
                title2: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 22,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 28,
                    ...(typographyVariants.title2 ?? {}),
                },
                title3: {
                    fontFamily: 'PlusJakartaSans-Regular',
                    fontSize: 20,
                    fontWeight: 400,
                    letterSpacing: 0,
                    lineHeight: 26,
                    ...(typographyVariants.title3 ?? {}),
                },
            },
        },

        debug,
    };

    theme.mixins = createMixins(theme as DefaultTheme);
    theme.spacing = theme.mixins.spacing.bind(theme.mixins);

    return theme as DefaultTheme;
}
