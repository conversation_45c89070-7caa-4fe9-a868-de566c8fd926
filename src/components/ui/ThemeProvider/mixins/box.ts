import { FlexStyle, ViewStyle } from 'react-native';
import { css } from 'styled-components/native';
import { WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';
import { cssProperty } from '../utils';

export type BoxMixinProps = {
    borderColor?: ViewStyle['borderColor'];
    borderRadius?: ViewStyle['borderRadius'];
    borderStyle?: ViewStyle['borderStyle'];
    borderWidth?: ViewStyle['borderWidth'];
    bottom?: ViewStyle['bottom'];
    debug?: boolean;
    display?: FlexStyle['display'];
    fullWidth?: boolean;
    height?: number | string;
    left?: ViewStyle['left'];
    maxHeight?: number | string;
    maxWidth?: number | string;
    minHeight?: number | string;
    minWidth?: number | string;
    opacity?: ViewStyle['opacity'];
    overflow?: ViewStyle['overflow'];
    position?: ViewStyle['position'];
    right?: ViewStyle['right'];
    top?: ViewStyle['top'];
    width?: number | string;
    zIndex?: ViewStyle['zIndex'];
};

export function boxMixin() {
    return ({
        theme,
        width,
        height,
        minWidth,
        minHeight,
        maxWidth,
        maxHeight,
        fullWidth,
        display,
        opacity,
        overflow,
        zIndex,
        position,
        top,
        right,
        bottom,
        left,
        borderRadius,
        borderColor,
        borderWidth,
        borderStyle,
        debug,
    }: WithTheme<BoxMixinProps>) => {
        if (debug && !borderColor) {
            borderColor = '#f00';
        }

        return css`
            ${cssProperty('border-radius', borderRadius, theme.mixins.strictUnit)};
            ${cssProperty('width', width, theme.mixins.strictUnit)};
            ${cssProperty('height', height, theme.mixins.strictUnit)};
            ${cssProperty('min-width', minWidth, theme.mixins.strictUnit)};
            ${cssProperty('min-height', minHeight, theme.mixins.strictUnit)};
            ${cssProperty('max-width', maxWidth, theme.mixins.strictUnit)};
            ${cssProperty('max-height', maxHeight, theme.mixins.strictUnit)};
            ${cssProperty('display', display)};
            ${cssProperty('opacity', opacity)};
            ${cssProperty('overflow', overflow)};
            ${cssProperty('zIndex', zIndex)};
            ${cssProperty('position', position)};
            ${cssProperty('top', top, theme.mixins.strictUnit)};
            ${cssProperty('right', right, theme.mixins.strictUnit)};
            ${cssProperty('bottom', bottom, theme.mixins.strictUnit)};
            ${cssProperty('left', left, theme.mixins.strictUnit)};
            ${cssProperty('borderColor', borderColor, theme.mixins.getColor)};
            ${cssProperty('borderWidth', borderWidth ?? (borderColor ? 1 : null), theme.mixins.strictUnit)};
            ${cssProperty('borderStyle', borderStyle ?? (borderColor || borderWidth ? 'solid' : null))};

            ${fullWidth && 'width: 100%;'};
        `;
    };
}

export const boxMixinHelpers = createMixinHelpers<BoxMixinProps>([
    'width',
    'height',
    'minWidth',
    'minHeight',
    'maxWidth',
    'maxHeight',
    'fullWidth',
    'display',
    'opacity',
    'overflow',
    'zIndex',
    'position',
    'top',
    'right',
    'bottom',
    'left',
    'borderRadius',
    'borderColor',
    'borderWidth',
    'borderStyle',
]);
