import { ThemeShadow, ThemeShadows, WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';

export type ShadowMixinProps = {
    shadow?: keyof ThemeShadows | boolean;
};

export function shadow(
    { shadow, theme }: WithTheme<ShadowMixinProps>,
    defaultShadow?: keyof ThemeShadows,
): Partial<ThemeShadow> {
    if (shadow === true) {
        return theme.shadows.small;
    }

    if (shadow) {
        return theme.shadows[shadow];
    }

    if (defaultShadow) {
        return theme.shadows[defaultShadow];
    }

    return {};
}

export const shadowMixinHelpers = createMixinHelpers<ShadowMixinProps>(['shadow']);
