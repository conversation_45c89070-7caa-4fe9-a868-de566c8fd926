import { css } from 'styled-components/native';
import { ThemePalette, WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';
import { cssProperty } from '../utils';

export type BackgroundMixinProps = {
    bgColor?: keyof Omit<ThemePalette, 'typography'> | string;
};

export function backgroundMixin() {
    return ({ bgColor, theme }: WithTheme<BackgroundMixinProps>) => css`
        ${cssProperty('background-color', bgColor, theme.mixins.getColor)}
    `;
}

export const backgroundMixinHelpers = createMixinHelpers<BackgroundMixinProps>(['bgColor']);
