import { FlexStyle } from 'react-native';
import { css } from 'styled-components/native';
import { WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';

export type FlexMixinProps = {
    alignContent?: FlexStyle['alignContent'];
    alignItems?: FlexStyle['alignItems'];
    alignSelf?: FlexStyle['alignSelf'];
    flex?: FlexStyle['flex'] | boolean;
    flexBasis?: FlexStyle['flexBasis'];
    flexDirection?: FlexStyle['flexDirection'];
    flexGrow?: FlexStyle['flexGrow'] | boolean;
    flexShrink?: FlexStyle['flexShrink'] | boolean;
    flexWrap?: FlexStyle['flexWrap'] | boolean;
    justifyContent?: FlexStyle['justifyContent'];
};

function getFlexWrap(flexWrap: 'wrap' | 'nowrap' | 'wrap-reverse' | undefined | boolean): FlexStyle['flexWrap'] {
    if (typeof flexWrap === 'boolean') {
        return flexWrap ? 'wrap' : 'nowrap';
    }

    return flexWrap;
}

export function flexMixin() {
    return ({
        flexDirection,
        alignContent,
        alignItems,
        alignSelf,
        flex,
        flexBasis,
        flexGrow,
        flexShrink,
        flexWrap,
        justifyContent,
    }: WithTheme<FlexMixinProps>) => {
        const value = (val: unknown, property: string) => (typeof val !== 'undefined' && val !== null ? property : '');
        return css`
            ${value(flexDirection, `flex-direction: ${flexDirection};`)}
            ${value(alignContent, `align-content: ${alignContent}`)}
            ${value(alignItems, `align-items: ${alignItems}`)}
            ${value(alignSelf, `align-self: ${alignSelf}`)}
            ${value(flex, `flex: ${Number(flex)}`)}
            ${value(flexBasis, `flex-basis: ${flexBasis}`)}
            ${value(flexGrow, `flex-grow: ${Number(flexGrow)}`)}
            ${value(flexShrink, `flex-shrink: ${Number(flexShrink)}`)}
            ${value(flexWrap, `flex-wrap: ${getFlexWrap(flexWrap)}`)}
            ${value(justifyContent, `justify-content: ${justifyContent}`)}
        `;
    };
}

export const flexMixinHelpers = createMixinHelpers<FlexMixinProps>([
    'alignContent',
    'alignItems',
    'alignSelf',
    'flex',
    'flexBasis',
    'flexDirection',
    'flexGrow',
    'flexShrink',
]);
