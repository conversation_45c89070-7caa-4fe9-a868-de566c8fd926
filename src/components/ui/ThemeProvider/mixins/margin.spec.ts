import { createTheme } from '../createTheme';
import { marginMixin, margins } from './margin';

describe('ThemeProvider.mixins.margin', () => {
    it('should correctly generate the right css based on default props and given props', () => {
        const theme = createTheme();
        const marginProps = margins({ my: 0, theme }, { my: 1 });

        expect(marginProps).toEqual({
            marginTop: 0,
            marginBottom: 0,
        });

        expect(marginMixin({ my: 1 })({ my: 0, theme })).toMatchSnapshot();
    });
});
