import { kebabCase } from 'lodash';
import { FlexStyle } from 'react-native';
import { WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';

export type MarginMixinProps = {
    m?: number;
    margin?: number;
    mb?: number;
    ml?: number;
    mr?: number;
    mt?: number;
    mx?: number;
    my?: number;
};

export function marginMixin(defaultProps: MarginMixinProps = {}) {
    return (props: WithTheme<MarginMixinProps>): string[] => {
        const marginProps = margins(props, defaultProps);

        return Object.entries(marginProps).reduce((acc, [key, value]) => {
            if (typeof value !== 'undefined' && value !== null) {
                return [...acc, `${kebabCase(key)}: ${value}px;`];
            }

            return acc;
        }, [] as string[]);
    };
}

export function margins(
    props: WithTheme<MarginMixinProps>,
    defaultProps: MarginMixinProps = {},
): Pick<FlexStyle, 'marginTop' | 'marginRight' | 'marginBottom' | 'marginLeft'> {
    let { margin, m, my, mx, mt, mr, mb, ml, theme } = {
        ...defaultProps,
        ...props,
    };

    mt = mt ?? my ?? m ?? margin;
    mr = mr ?? mx ?? m ?? margin;
    mb = mb ?? my ?? m ?? margin;
    ml = ml ?? mx ?? m ?? margin;

    return {
        ...(typeof mt !== 'undefined' && mt !== null ? { marginTop: theme.mixins.spacingValue(mt) } : {}),
        ...(typeof mr !== 'undefined' && mr !== null ? { marginRight: theme.mixins.spacingValue(mr) } : {}),
        ...(typeof mb !== 'undefined' && mb !== null ? { marginBottom: theme.mixins.spacingValue(mb) } : {}),
        ...(typeof ml !== 'undefined' && ml !== null ? { marginLeft: theme.mixins.spacingValue(ml) } : {}),
    };
}

export const marginMixinHelpers = createMixinHelpers<MarginMixinProps>([
    'margin',
    'm',
    'my',
    'mx',
    'mt',
    'mr',
    'mb',
    'ml',
]);
