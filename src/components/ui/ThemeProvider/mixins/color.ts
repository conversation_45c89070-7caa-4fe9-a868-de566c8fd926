import { ThemeColorKeys, ThemeTypographyColorKeys, WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';

export type ColorMixinProps<T = false> = {
    color?: (T extends true ? ThemeTypographyColorKeys | ThemeColorKeys : ThemeColorKeys) | string;
};

interface ColorMixinOptions {
    colorProperty?: string;
    isTypography?: boolean;
}

export function colorMixin(
    defaultColor?: ColorMixinProps['color'],
    { isTypography, colorProperty = 'color' }: ColorMixinOptions = {},
) {
    return ({ color, theme }: WithTheme<ColorMixinProps>) => {
        let value: string | null = '';

        if (isTypography) {
            if ((color as ThemeTypographyColorKeys) in theme.palette.typography) {
                value = theme.palette.typography[color as ThemeTypographyColorKeys];
            }
        }

        value = value || theme.mixins.getColor(color, defaultColor);

        if (!value) {
            return '';
        }

        return `${colorProperty}: ${value};`;
    };
}

export const colorMixinHelpers = createMixinHelpers<ColorMixinProps>(['color']);
