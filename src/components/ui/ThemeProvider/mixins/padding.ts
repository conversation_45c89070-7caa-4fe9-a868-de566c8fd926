import { kebabCase } from 'lodash';
import { FlexStyle } from 'react-native';
import { WithTheme } from '../Theme';
import { createMixinHelpers } from '../createMixinHelpers';

export type PaddingMixinProps = {
    p?: number;
    padding?: number;
    pb?: number;
    pl?: number;
    pr?: number;
    pt?: number;
    px?: number;
    py?: number;
};

export function paddingMixin(defaultProps: PaddingMixinProps = {}) {
    return (props: WithTheme<PaddingMixinProps>): string[] => {
        const paddingProps = paddings(props, defaultProps);

        return Object.entries(paddingProps).reduce((acc, [key, value]) => {
            if (typeof value !== 'undefined' && value !== null) {
                return [...acc, `${kebabCase(key)}: ${value}px;`];
            }

            return acc;
        }, [] as string[]);
    };
}

export function paddings(
    props: WithTheme<PaddingMixinProps>,
    defaultProps: PaddingMixinProps = {},
): Pick<FlexStyle, 'paddingTop' | 'paddingRight' | 'paddingBottom' | 'paddingLeft'> {
    let { padding, p, py, px, pt, pr, pb, pl, theme } = {
        ...defaultProps,
        ...props,
    };

    pt = pt ?? py ?? p ?? padding;
    pr = pr ?? px ?? p ?? padding;
    pb = pb ?? py ?? p ?? padding;
    pl = pl ?? px ?? p ?? padding;

    return {
        ...(typeof pt !== 'undefined' && pt !== null ? { paddingTop: theme.mixins.spacingValue(pt) } : {}),
        ...(typeof pr !== 'undefined' && pr !== null ? { paddingRight: theme.mixins.spacingValue(pr) } : {}),
        ...(typeof pb !== 'undefined' && pb !== null ? { paddingBottom: theme.mixins.spacingValue(pb) } : {}),
        ...(typeof pl !== 'undefined' && pl !== null ? { paddingLeft: theme.mixins.spacingValue(pl) } : {}),
    };
}

export const paddingMixinHelpers = createMixinHelpers<PaddingMixinProps>([
    'padding',
    'p',
    'py',
    'px',
    'pt',
    'pr',
    'pb',
    'pl',
]);
