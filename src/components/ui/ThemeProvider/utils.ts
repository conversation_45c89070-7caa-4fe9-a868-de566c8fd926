/* eslint-disable @typescript-eslint/no-explicit-any */
import { darken, lighten, readableColor, tint } from 'polished';
import { ViewStyle } from 'react-native';
import { ThemeColor, ThemeColorShades, WithShades } from './Theme';

export function createShades(main: string): ThemeColorShades {
    const shadeKeys = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

    return shadeKeys.reduce(
        (acc, shade) => ({
            ...acc,
            [`shade${shade}` as any]: tint(1 - shade / 100, main),
        }),
        {} as ThemeColorShades,
    );
}

export function createThemeColor(main: ThemeColor | string, contrast?: string): ThemeColor {
    if (typeof main === 'string') {
        main = {
            main,
        } as ThemeColor;
    }

    const mainColor = main.main;

    return {
        contrast: contrast || main.contrast || readableColor(mainColor),
        dark: main.dark || darken(0.1, mainColor),
        light: main.light || lighten(0.1, mainColor),
        main: mainColor,
    };
}

export function createThemeColorWithShades(main: ThemeColor | string, contrast?: string): WithShades<ThemeColor> {
    const themeColor = createThemeColor(main, contrast);

    return {
        ...themeColor,
        ...createShades(themeColor.main),
        hasShades: true,
    };
}

export function cssProperty<Style = ViewStyle>(
    property: keyof Style,
    value: any,
    valueFormat: ((value: any) => any) | `${string | never}{value}${string | never}` = '{value}',
): string {
    let formattedValue = value;
    if (valueFormat) {
        formattedValue = typeof valueFormat === 'function' ? valueFormat(value) : valueFormat.replace('{value}', value);
    }

    if (value === null || typeof value === 'undefined') {
        return '';
    }

    return `${String(property)}: ${formattedValue};`;
}
