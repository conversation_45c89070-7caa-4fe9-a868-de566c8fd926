/* eslint-disable @typescript-eslint/no-non-null-assertion */
import get from 'lodash/get';
import has from 'lodash/has';
import { stripUnit } from 'polished';
import { ImageStyle, TextStyle, ViewStyle } from 'react-native';
import { DefaultTheme } from 'styled-components';
import { css } from 'styled-components/native';
import { ThemeTypography } from '~/components/ui/ThemeProvider/Theme';
import { TypographyProps } from '~/components/ui/Typography';

export function createMixins(theme: DefaultTheme): DefaultTheme['mixins'] {
    const mixins: DefaultTheme['mixins'] = {
        getColor(color, defaultColor) {
            const { palette } = theme;
            const resolvedDefaultColor = defaultColor ? mixins.getColor(defaultColor) : defaultColor;

            if (!color) {
                return resolvedDefaultColor;
            }

            if (has(palette, color)) {
                const themeColor = get(palette, color);
                return themeColor.main || themeColor || resolvedDefaultColor;
            }

            return color || resolvedDefaultColor;
        },
        getContrastColor(color, defaultColor) {
            const { palette } = theme;
            const defaultColor$ = mixins.getColor(defaultColor)!;

            if (!color || ['transparent', 'currentColor', 'inherit'].includes(color)) {
                return defaultColor$;
            }

            if (color in palette) {
                return mixins.getColor(color.replace(/\.contrast$/g, '') + '.contrast') || defaultColor$;
            }

            return color || defaultColor$;
        },
        getTypography<K extends keyof ThemeTypography>(
            { variant, ...rest }: TypographyProps,
            property: K,
            defaultValue?: ThemeTypography[K],
        ) {
            const typographyVariant = theme.typography.variants[variant || 'body'] ?? theme.typography.variants.body;

            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            return (rest[property] ?? typographyVariant[property] ?? defaultValue) as any;
        },
        getTypographyFamily({ variant, fontFamily, fontWeight }: TypographyProps) {
            const typographyVariant = theme.typography.variants[variant || 'body'] ?? theme.typography.variants.body;
            const defaultFontFamily = typographyVariant.fontFamily;

            if (fontFamily) {
                return fontFamily;
            }

            if (fontWeight) {
                return theme.typography.fontFamilyWeightMap[fontWeight] ?? defaultFontFamily;
            }

            return defaultFontFamily;
        },
        spacing(...args: number[]) {
            const { spacingOptions } = theme;
            const { spacing, spacingValue } = mixins;

            if (args.length === 0) {
                return spacing(1);
            }

            return args.map((value) => mixins.strictUnit(spacingValue(value), spacingOptions.unit)).join(' ');
        },
        spacingValue(value) {
            const { spacingOptions } = theme;

            return value * spacingOptions.size;
        },
        strictUnit(value, unit) {
            if (value === 'auto') {
                return value;
            }

            if (String(value) !== String(stripUnit(value))) {
                return String(value);
            }

            return `${value}${unit || theme.spacingOptions.unit || 'px'}`;
        },
        typography(variant, props) {
            return css`
                font-family: ${theme.mixins.getTypographyFamily({ ...props, variant })};
                font-size: ${mixins.strictUnit(theme.mixins.getTypography({ ...props, variant }, 'fontSize')!)};
                font-weight: ${theme.mixins.getTypography({ ...props, variant }, 'fontWeight')};
                letter-spacing: ${mixins.strictUnit(
                    theme.mixins.getTypography({ ...props, variant }, 'letterSpacing')!,
                )};
                line-height: ${mixins.strictUnit(theme.mixins.getTypography({ ...props, variant }, 'lineHeight')!)};
                text-transform: ${theme.mixins.getTypography({ ...props, variant }, 'textTransform', 'none')};
                text-decoration-line: ${theme.mixins.getTypography(
                    { ...props, variant },
                    'textDecorationLine',
                    'none',
                )};
            `;
        },

        typographyObject(variant, props) {
            return {
                fontFamily: theme.mixins.getTypographyFamily({ ...props, variant }),

                fontSize: Number(stripUnit(theme.mixins.getTypography({ ...props, variant }, 'fontSize')!)),

                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                fontWeight: String(theme.mixins.getTypography({ ...props, variant }, 'fontWeight')) as any,
                letterSpacing: Number(stripUnit(theme.mixins.getTypography({ ...props, variant }, 'letterSpacing')!)),
                lineHeight: Number(stripUnit(theme.mixins.getTypography({ ...props, variant }, 'lineHeight')!)),
                textTransform: theme.mixins.getTypography({ ...props, variant }, 'textTransform', 'none'),
            };
        },
        debug(style?: ViewStyle | TextStyle | ImageStyle): ViewStyle {
            return {
                ...style,
                borderWidth: 1,
                borderColor: style?.borderColor ?? 'red',
                borderStyle: 'solid',
            };
        },
    };

    return mixins;
}
