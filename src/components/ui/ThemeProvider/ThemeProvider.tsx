import { PropsWithChildren, ReactElement } from 'react';
import { useColorScheme } from 'react-native';
import { ThemeProvider as StyledThemeProvider } from 'styled-components/native';
import { darkTheme, lightTheme } from './themes';

export function ThemeProvider({ children }: PropsWithChildren): ReactElement {
    const colorScheme = useColorScheme();

    return <StyledThemeProvider theme={colorScheme ? lightTheme : darkTheme}>{children}</StyledThemeProvider>;
}
