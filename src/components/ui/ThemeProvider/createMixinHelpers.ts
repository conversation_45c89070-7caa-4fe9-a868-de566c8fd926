import omit from 'lodash/omit';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
interface MixinHelpers<T extends object = Record<string, any>, K extends keyof T = keyof T> {
    keys: K[];

    getProps(Theme: T): T;

    omitProps<P extends T>(Theme: P): Exclude<P, T>;
}

export function createMixinHelpers<T extends object, K extends keyof T = keyof T>(keys: K[]): MixinHelpers<T, K> {
    return {
        getProps: (props: T) =>
            keys.reduce((acc, prop) => {
                if (prop in props) {
                    acc[prop] = props[prop];
                }
                return acc;
            }, {} as T),
        keys,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        omitProps: (props) => omit(props, keys) as any,
    };
}

export const shouldForwardMixinProps = (
    helpers: Array<Pick<MixinHelpers, 'keys'>>,
    exclude: string[] = [],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
): ((prop: any) => boolean) => {
    const allKeys = helpers
        .reduce((acc, helper) => acc.concat(helper.keys), [] as string[])
        .filter((key) => !exclude.includes(key));

    return (prop) => !allKeys.includes(`${prop}`);
};

export const mixinKeys = (...keys: string[]): { keys: string[] } => ({ keys });
