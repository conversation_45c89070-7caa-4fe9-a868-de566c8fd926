import { storiesOf } from '@storybook/react-native';
import { startCase, upperFirst } from 'lodash';
import React, { PropsWithChildren } from 'react';
import styled, { useTheme } from 'styled-components/native';
import { HStack, VStack } from '../Grid';
import { Screen } from '../Screen';
import { Typography } from '../Typography';
import { ThemePalette } from './Theme';
import { ThemeProvider } from './ThemeProvider';

const ColorSwatch = styled.View<{ name: string }>`
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 12px;
    border: 1px solid ${({ theme }) => theme.palette.borderOpaque.main};
    background-color: ${({ name, theme }) => theme.mixins.getColor(name)};
`;

function ColorLabel({ name }: { name: string }) {
    return (
        <HStack p={1} minWidth={'50%'}>
            <ColorSwatch name={name} />
            <Typography variant="callout">{name}</Typography>
        </HStack>
    );
}

function HasShades({ name, children }: PropsWithChildren<{ name: keyof ThemePalette }>) {
    const theme = useTheme();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (!(theme.palette[name] as any)?.hasShades) {
        return null;
    }

    return <>{children}</>;
}

function ColorPalette({ name }: { name: keyof ThemePalette }) {
    return (
        <VStack>
            <Typography fontWeight={600} mb={1}>
                {upperFirst(startCase(name))}
            </Typography>
            <HStack flexWrap mb={2}>
                <ColorLabel name={`${name}.main`} />
                <ColorLabel name={`${name}.light`} />
                <ColorLabel name={`${name}.dark`} />
                <ColorLabel name={`${name}.contrast`} />
            </HStack>
            <HasShades name={name}>
                <Typography fontWeight={600} mb={1}>
                    {upperFirst(startCase(name))} Shades
                </Typography>
                <HStack flexWrap mb={2}>
                    <ColorLabel name={`${name}.shade100`} />
                    <ColorLabel name={`${name}.shade90`} />
                    <ColorLabel name={`${name}.shade80`} />
                    <ColorLabel name={`${name}.shade70`} />
                    <ColorLabel name={`${name}.shade60`} />
                    <ColorLabel name={`${name}.shade50`} />
                    <ColorLabel name={`${name}.shade40`} />
                    <ColorLabel name={`${name}.shade30`} />
                    <ColorLabel name={`${name}.shade20`} />
                    <ColorLabel name={`${name}.shade10`} />
                </HStack>
            </HasShades>
        </VStack>
    );
}

function TypographyPalette({ name }: { name: keyof ThemePalette }) {
    return (
        <>
            <Typography fontWeight={600} mb={1}>
                Typography
            </Typography>
            <HStack flexWrap mb={2}>
                <ColorLabel name={`${name}.textPrimary`} />
                <ColorLabel name={`${name}.textSecondary`} />
                <ColorLabel name={`${name}.disabled`} />
                <ColorLabel name={`${name}.primary`} />
                <ColorLabel name={`${name}.secondary`} />
                <ColorLabel name={`${name}.accent`} />
                <ColorLabel name={`${name}.error`} />
                <ColorLabel name={`${name}.success`} />
                <ColorLabel name={`${name}.warning`} />
            </HStack>
        </>
    );
}

function Colors() {
    return (
        <>
            <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={2}>
                Theme Colors
            </Typography>
            <TypographyPalette name={'typography'} />
            <ColorPalette name={'primary'} />
            <ColorPalette name={'secondary'} />
            <ColorPalette name={'accent'} />
            <ColorPalette name={'error'} />
            <ColorPalette name={'warning'} />
            <ColorPalette name={'success'} />
            <ColorPalette name={'backgroundPrimary'} />
            <ColorPalette name={'backgroundSecondary'} />
            <ColorPalette name={'backgroundTertiary'} />
            <ColorPalette name={'contentPrimary'} />
            <ColorPalette name={'contentSecondary'} />
            <ColorPalette name={'contentTertiary'} />
            <ColorPalette name={'borderOpaque'} />
            <ColorPalette name={'borderSelected'} />
            <ColorPalette name={'divider'} />
        </>
    );
}

storiesOf('ThemeProvider', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    Theme Configuration
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Colors', Colors);
