import { ImageStyle, TextStyle, ViewStyle } from 'react-native';
import { FlattenSimpleInterpolation } from 'styled-components';
import { TypographyProps } from '~/components/ui/Typography';

declare module 'styled-components' {
    // eslint-disable-next-line @typescript-eslint/no-empty-interface
    interface DefaultTheme extends Theme {}
}

export interface ThemeColorShades {
    shade10: string;
    shade100: string;
    shade20: string;
    shade30: string;
    shade40: string;
    shade50: string;
    shade60: string;
    shade70: string;
    shade80: string;
    shade90: string;
}

export interface ThemeColor {
    contrast: string;
    dark: string;
    hasShades?: boolean;
    light: string;
    main: string;
}

export type WithShades<T> = T &
    ThemeColorShades & {
        hasShades: true;
    };

export interface ThemeTypographyColor {
    accent: string;
    disabled: string;
    error: string;
    primary: string;
    secondary: string;
    success: string;
    textPrimary: string;
    textSecondary: string;
    warning: string;
}

export interface ThemePalette {
    accent: WithShades<ThemeColor>;
    backgroundPrimary: ThemeColor;
    backgroundSecondary: ThemeColor;
    backgroundTertiary: ThemeColor;
    borderOpaque: ThemeColor;
    borderSelected: ThemeColor;
    contentPrimary: ThemeColor;
    contentSecondary: ThemeColor;
    contentTertiary: ThemeColor;
    divider: ThemeColor;
    error: WithShades<ThemeColor>;
    primary: WithShades<ThemeColor>;
    secondary: WithShades<ThemeColor>;
    success: WithShades<ThemeColor>;
    typography: ThemeTypographyColor;
    warning: WithShades<ThemeColor>;
}

export interface ThemeTypography {
    fontFamily: `PlusJakartaSans-${'Regular' | 'Medium' | 'SemiBold' | 'Bold'}`;
    fontSize: string | number;
    fontWeight: 400 | 500 | 600 | 700;
    letterSpacing: string | number;
    lineHeight: string | number;
    textDecorationLine?: TextStyle['textDecorationLine'];
    textTransform?: 'uppercase' | 'lowercase' | 'capitalize' | 'none';
}

interface ThemeTypographyVariantMap {
    body: ThemeTypography;
    button: ThemeTypography;
    callout: ThemeTypography;
    caption1: ThemeTypography;
    caption2: ThemeTypography;
    footnote: ThemeTypography;
    largeTitle: ThemeTypography;
    subhead: ThemeTypography;
    title1: ThemeTypography;
    title2: ThemeTypography;
    title3: ThemeTypography;
}

export interface ThemeTypographies {
    fontFamilyWeightMap: {
        400: string;
        500: string;
        600: string;
        700: string;
    };

    variants: ThemeTypographyVariantMap;
}

interface ThemeMixins {
    getColor(color: ThemeColorKeys, defaultColor?: never): string;

    getColor(color: string | null | undefined, defaultColor?: never): string | null;

    getColor(color: ThemeColorKeys | string | null | undefined, defaultColor?: ThemeColorKeys | string): string;

    getContrastColor(color?: ThemeColorKeys | string | null, defaultColor?: never): string | null;

    getContrastColor(color: ThemeColorKeys | string | null | undefined, defaultColor?: ThemeColorKeys | string): string;

    getTypography<K extends keyof ThemeTypography>(
        { variant, ...rest }: TypographyProps,
        property: K,
        defaultValue?: ThemeTypography[K],
    ): ThemeTypography[K];

    getTypographyFamily(props: TypographyProps): string;

    spacing(all: number): string;

    spacing(vertical: number, horizontal: number): string;

    spacing(top: number, horizontal: number, bottom: number): string;

    spacing(top: number, left: number, bottom: number, right: number): string;

    spacingValue(value: number): number;

    strictUnit(value: string | number, unit?: string): string;

    typography(variant: keyof ThemeTypographyVariantMap, props: Partial<ThemeTypography>): FlattenSimpleInterpolation;

    typographyObject(variant: keyof ThemeTypographyVariantMap, props: Partial<ThemeTypography>): TextStyle;

    debug(style?: ViewStyle | TextStyle | ImageStyle): ViewStyle;
}

interface ThemeSpacing {
    size: number;
    unit: string;
}

export interface ThemeShadow {
    elevation: ViewStyle['elevation'];
    shadowColor: ViewStyle['shadowColor'];
    shadowOffset: ViewStyle['shadowOffset'];
    shadowOpacity: ViewStyle['shadowOpacity'];
    shadowRadius: ViewStyle['shadowRadius'];
}

export interface ThemeShadows {
    medium: ThemeShadow;
    small: ThemeShadow;
}

export interface Theme {
    debug: Pick<ViewStyle, 'borderStyle' | 'borderWidth' | 'borderColor'>;
    mixins: ThemeMixins;
    mode: 'light' | 'dark';
    palette: ThemePalette;
    shadows: ThemeShadows;
    spacing: ThemeMixins['spacing'];
    spacingOptions: ThemeSpacing;
    typography: ThemeTypographies;
}

type ThemeColorsThatHasShades = {
    [key in keyof ThemePalette as ThemePalette[key] extends { hasShades: true } ? key : never]: ThemePalette[key];
};

export type WithTheme<T> = T & { theme: Theme };
export type ThemeColorKeys =
    | Exclude<keyof ThemePalette, 'typography'>
    | `${Exclude<keyof ThemePalette, 'typography'>}.${keyof Omit<ThemeColor, 'hasShades'> | never}`
    | `${keyof ThemeColorsThatHasShades}.${ThemeColorShadeKeys}`
    | `typography.${ThemeTypographyColorKeys}`;

export type ThemeTypographyColorKeys = keyof ThemeTypographyColor;
export type ThemeTypographyVariants = keyof ThemeTypographyVariantMap;
export type ThemeColorShadeKeys = keyof ThemeColorShades;
