/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { boolean, select } from '@storybook/addon-knobs';
import { storiesOf } from '@storybook/react-native';
import { upperFirst } from 'lodash';
import React from 'react';
import { HStack, VStack } from '../Grid';
import { IconButton } from '../IconButton';
import { Screen } from '../Screen';
import { ThemeProvider } from '../ThemeProvider';
import { Typography } from '../Typography';
import { Button } from './Button';

function buttonProps() {
    return {
        color: select(
            'color',
            [
                'primary',
                'secondary',
                'accent',
                'error',
                'warning',
                'success',
                'backgroundPrimary',
                'backgroundSecondary',
                'backgroundTertiary',
                'contentPrimary',
                'contentSecondary',
                'contentTertiary',
                'borderOpaque',
                'borderSelected',
                'divider',
            ],
            'primary',
        ),
        disabled: boolean('disabled', false),
        fullWidth: boolean('fullWidth', false),
        loading: boolean('loading', false),
        size: select('size', ['xsmall', 'small', 'medium', 'large'], 'medium'),
    };
}

function DefaultButtons() {
    return (
        <>
            {['contained', 'outlined', 'subtle', 'text'].map((variant: any) => (
                <Button
                    variant={variant}
                    label={upperFirst(variant) + ' Button'}
                    mb={2}
                    key={variant}
                    {...buttonProps()}
                />
            ))}
        </>
    );
}

function WithIcons() {
    return (
        <VStack>
            {['contained', 'outlined', 'subtle', 'text'].map((variant: any) => (
                <HStack key={variant}>
                    <IconButton variant={variant} mb={2} {...buttonProps()}>
                        <MaterialIcons name={'favorite'} />
                    </IconButton>
                </HStack>
            ))}

            {['contained', 'outlined', 'subtle', 'text'].map((variant: any) => (
                <HStack key={variant}>
                    <Button
                        variant={variant}
                        startIcon={<MaterialIcons name={'favorite'} />}
                        label="Book now"
                        mb={2}
                        {...buttonProps()}
                    />
                </HStack>
            ))}

            {['contained', 'outlined', 'subtle', 'text'].map((variant: any) => (
                <HStack key={variant}>
                    <Button
                        variant={variant}
                        startIcon={<MaterialIcons name={'favorite'} />}
                        endIcon={<MaterialIcons name={'arrow-right'} />}
                        label="Book now"
                        mb={2}
                        {...buttonProps()}
                    />
                </HStack>
            ))}
        </VStack>
    );
}

storiesOf('Buttons', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    Buttons
                </Typography>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={2}>
                    Button variants, sizes and colors.
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Default buttons', DefaultButtons)
    .add('With icons', WithIcons);
