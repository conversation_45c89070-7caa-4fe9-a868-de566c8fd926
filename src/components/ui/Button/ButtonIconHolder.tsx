import { upperFirst } from 'lodash';
import { ReactElement } from 'react';
import { View, ViewProps } from 'react-native';
import { makeStyles, Styles, sx } from '../makeStyles';
import { ButtonSize } from './types';

type Variants = 'Start' | 'End';
type SizeTag = `${ButtonSize}${Variants}`;
type VariantTag = `variant${Variants}`;

interface ButtonIconHolderProps extends ViewProps {
    disableAbsolute?: boolean;
    edge: Lowercase<Variants>;
    size?: ButtonSize;
}

export function ButtonIconHolder(props: ButtonIconHolderProps): ReactElement {
    const { size, edge, disableAbsolute, ...rest } = props;

    const styles = useStyles(props);

    const sizeTag: SizeTag = `${size ?? 'medium'}${upperFirst(edge) as Variants}`;
    const variantTag: VariantTag = `variant${upperFirst(edge) as Variants}`;

    return <View style={sx(styles.root, size && styles[size], styles[sizeTag], styles[variantTag])} {...rest} />;
}

const useStyles = makeStyles<ButtonIconHolderProps, Styles<'root' | SizeTag | VariantTag | ButtonSize>>(
    ({ disableAbsolute, theme }) => ({
        root: {},
        variantEnd: {
            ...(disableAbsolute ? {} : { position: 'absolute' }),
        },
        largeEnd: {
            ...(disableAbsolute ? {} : { right: theme.mixins.spacingValue(2) }),
        },
        mediumEnd: {
            ...(disableAbsolute ? {} : { right: theme.mixins.spacingValue(1.5) }),
        },
        smallEnd: {
            ...(disableAbsolute ? {} : { right: theme.mixins.spacingValue(1.25) }),
        },

        xsmallEnd: {
            ...(disableAbsolute ? {} : { right: theme.mixins.spacingValue(0.75) }),
        },
        variantStart: {},
        largeStart: {
            marginRight: theme.mixins.spacingValue(2),
        },
        mediumStart: {
            marginRight: theme.mixins.spacingValue(1.5),
        },
        smallStart: {
            marginRight: theme.mixins.spacingValue(1.25),
        },
        xsmallStart: {
            marginRight: theme.mixins.spacingValue(0.75),
        },
        xsmall: {
            minWidth: 18,
        },
        small: {
            minWidth: 20,
        },
        medium: {
            minWidth: 22,
        },
        large: {
            minWidth: 24,
        },
    }),
);
