import { FormikContext, FormikContextType } from 'formik';
import { ReactElement, useCallback, useContext } from 'react';
import { GestureResponderEvent } from 'react-native';
import { Button, ButtonProps } from './Button';

export interface FormikButtonProps extends ButtonProps {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    formik?: Pick<FormikContextType<any>, 'submitForm' | 'isValid' | 'isSubmitting'>;
    validateDirty?: boolean;
}

export function FormikButton({
    onPress,
    disabled,
    loading,
    validateDirty,
    formik,
    ...rest
}: FormikButtonProps): ReactElement {
    const ctx = useContext(FormikContext) || formik;

    const handleButtonPress = useCallback(
        async (event: GestureResponderEvent) => {
            if (onPress) {
                await onPress(event);
            }

            await ctx?.submitForm?.();
            await ctx?.resetForm?.({
                values: ctx?.values,
            });
        },
        [ctx, onPress],
    );

    return (
        <Button
            onPress={handleButtonPress}
            loading={ctx?.isSubmitting || loading}
            disabled={!ctx?.isValid || ctx?.isSubmitting || disabled || (validateDirty && !ctx?.dirty)}
            {...rest}
        />
    );
}
