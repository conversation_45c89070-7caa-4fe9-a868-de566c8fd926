import { useLinkTo } from '@react-navigation/native';
import { To } from '@react-navigation/native/lib/typescript/src/useLinkTo';
import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';
import { forwardRef, useState } from 'react';
import { TouchableOpacity, TouchableOpacityProps, ViewStyle } from 'react-native';
import styled, { css } from 'styled-components/native';
import { TypographyProps } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { IconElement, useResolveIcon } from '~/hooks/useResolveIcon';
import { ThemeColorKeys } from '../ThemeProvider/Theme';
import { mixinKeys, shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';
import { boxMixin, boxMixinHelpers, BoxMixinProps } from '../ThemeProvider/mixins/box';
import { marginMixin, marginMixinHelpers, MarginMixinProps } from '../ThemeProvider/mixins/margin';
import { paddingMixin, paddingMixinHelpers, PaddingMixinProps } from '../ThemeProvider/mixins/padding';
import { ButtonIconHolder } from './ButtonIconHolder';
import { ButtonLabel } from './ButtonLabel';
import { getButtonSize, getButtonStyle } from './styles';
import { ButtonSize, ButtonVariant } from './types';

export interface ButtonProps<ParamList extends ReactNavigation.RootParamList = ReactNavigation.RootParamList>
    extends Omit<TouchableOpacityProps, 'children'>,
        MarginMixinProps,
        PaddingMixinProps,
        BoxMixinProps {
    align?: ViewStyle['justifyContent'];
    color?: ThemeColorKeys | string;
    disabled?: boolean;
    endIcon?: IconElement;
    external?: boolean;
    fullWidth?: boolean;
    href?: string;
    iconColor?: ThemeColorKeys | string;
    keepIcons?: boolean;
    label: string;
    labelFullWidth?: boolean;
    loading?: boolean;
    size?: ButtonSize;
    startIcon?: IconElement;
    textAlign?: TypographyProps['textAlign'];
    textColor?: ThemeColorKeys | string;
    to?: To<ParamList>;
    variant?: ButtonVariant;
}

const ButtonComponent = forwardRef<TouchableOpacity, ButtonProps>((props, ref) => {
    // Not sure why this error appears.
    // eslint-disable-next-line react/prop-types
    const {
        startIcon,
        endIcon,
        size,
        variant,
        color,
        label,
        loading,
        disabled,
        // eslint-disable-next-line react/prop-types
        onPress,
        to,
        href,
        external,
        textColor,
        iconColor,
        fullWidth,
        textAlign,
        keepIcons,
        labelFullWidth,
        ...rest
    } = props;

    const [internalLoading, setInternalLoading] = useState(false);
    const isLoading = loading || internalLoading;

    const startIconElement = useResolveIcon(startIcon, {
        color: iconColor || textColor || color,
        disabled,
        size,
        withContrast: !props.disabled && variant === 'contained',
    });

    const endIconElement = useResolveIcon(endIcon, {
        color: iconColor || textColor || color,
        disabled,
        size,
        withContrast: !props.disabled && variant === 'contained',
    });

    const linkTo = useLinkTo();

    const handlePress = useEvent(async (e) => {
        try {
            setInternalLoading(true);
            if (onPress) {
                await onPress(e);
            }

            if (to) {
                linkTo(to);
            } else if (href) {
                if (!external) {
                    await WebBrowser.openBrowserAsync(href);
                } else {
                    await Linking.openURL(href);
                }
            }
        } finally {
            setInternalLoading(false);
        }
    });

    return (
        <TouchableOpacity
            disabled={isLoading || disabled}
            activeOpacity={0.8}
            ref={ref}
            onPress={handlePress}
            {...rest}
        >
            {!isLoading && (keepIcons || startIconElement) ? (
                <ButtonIconHolder edge={'start'} size={size} disableAbsolute={keepIcons}>
                    {startIconElement}
                </ButtonIconHolder>
            ) : null}
            <ButtonLabel
                buttonVariant={variant}
                buttonSize={size}
                color={textColor || color}
                disabled={isLoading || disabled}
                loading={isLoading}
                fullWidth={labelFullWidth || fullWidth}
                textAlign={textAlign ?? 'center'}
            >
                {label}
            </ButtonLabel>
            {!isLoading && (keepIcons || endIconElement) ? (
                <ButtonIconHolder edge={'end'} size={size} disableAbsolute={keepIcons}>
                    {endIconElement}
                </ButtonIconHolder>
            ) : null}
        </TouchableOpacity>
    );
});

ButtonComponent.displayName = 'ButtonComponent';

export const Button = styled(ButtonComponent).withConfig({
    displayName: 'Button',
    shouldForwardProp: shouldForwardMixinProps(
        [boxMixinHelpers, paddingMixinHelpers, marginMixinHelpers, mixinKeys('align')],
        ['fullWidth'],
    ),
})`
    display: flex;
    align-items: center;
    justify-content: ${({ align }) => align || 'center'};
    flex-direction: row;

    ${(props) =>
        props.fullWidth
            ? css`
                  width: 100%;
                  flex-basis: 100%;
              `
            : ''}

    ${getButtonSize}
    ${getButtonStyle}
    ${boxMixin()}
    ${paddingMixin()}
    ${marginMixin()};
`;

Button.defaultProps = {
    color: 'primary',
    size: 'medium',
    variant: 'contained',
};
