import { tint } from 'polished';
import { css } from 'styled-components/native';
import { WithTheme } from '../ThemeProvider/Theme';
import { ButtonProps } from './Button';
import { ButtonLabelProps } from './ButtonLabel';
import { ButtonSize, ButtonVariant } from './types';

type ButtonStyleProps = WithTheme<Pick<ButtonProps, 'variant' | 'color' | 'disabled' | 'loading'>>;

const buttonStyles: Record<ButtonVariant, (props: ButtonStyleProps) => string | ReturnType<typeof css>> = {
    contained: (props) => {
        const color = props.theme.mixins.getColor(props.color, 'primary');

        if (props.disabled || props.loading) {
            return css`
                background: ${tint(0.94, color)};
            `;
        }

        return css`
            background: ${color};
        `;
    },
    outlined: (props) => {
        const color = props.theme.mixins.getColor(props.color, 'primary');

        if (props.disabled || props.loading) {
            return css`
                border: ${tint(0.8, color)};
            `;
        }

        return css`
            border: ${color};
        `;
    },
    subtle: (props) => {
        const color = props.theme.mixins.getColor(props.color, 'primary');

        return css`
            background: ${tint(props.disabled || props.loading ? 0.95 : 0.9, color)};
        `;
    },
    text: () => css``,
};

const buttonSizes: Record<ButtonSize, (props: WithTheme<ButtonProps>) => ReturnType<typeof css>> = {
    large: ({ theme, startIcon }) => css`
        padding: ${theme.spacing(0, startIcon ? 2.5 : 5, 0, startIcon ? 2.5 : 5)};
        height: ${theme.spacing(7)};
        border-radius: 12px;
    `,
    medium: ({ theme, startIcon }) => css`
        padding: ${theme.spacing(0, startIcon ? 1.5 : 3, 0, startIcon ? 1.5 : 3)};
        height: ${theme.spacing(5)};
        border-radius: 12px;
    `,
    small: ({ theme, startIcon }) => css`
        padding: ${theme.spacing(0, startIcon ? 1 : 2, 0, startIcon ? 1 : 2)};
        height: ${theme.spacing(4.5)};
        border-radius: 10px;
    `,
    xsmall: ({ theme, startIcon }) => css`
        padding: ${theme.spacing(0, startIcon ? 0.75 : 1.5, 0, startIcon ? 0.75 : 1.5)};
        height: ${theme.spacing(3.5)};
        border-radius: 8px;
    `,
};

export function getButtonSize(props: WithTheme<ButtonProps>): ReturnType<typeof css> {
    return (props.size ? buttonSizes[props.size] : buttonSizes.medium)(props);
}

export function getButtonStyle(props: ButtonStyleProps): string | ReturnType<typeof css> {
    return (props.variant ? buttonStyles[props.variant] : buttonStyles.contained)(props);
}

const labelStyles: Record<ButtonVariant, (props: WithTheme<ButtonLabelProps>) => ReturnType<typeof css>> = {
    contained: (props) => {
        const color = props.theme.mixins.getColor(props.color, 'primary');

        if (props.disabled) {
            return css`
                color: ${tint(0.4, color)};
            `;
        }

        return css`
            color: ${props.theme.mixins.getContrastColor(props.color || 'primary')};
        `;
    },
    outlined: (props) => {
        const color = props.theme.mixins.getColor(props.color, 'primary');

        if (props.disabled) {
            return css`
                color: ${tint(0.6, color)};
            `;
        }
        return css`
            color: ${props.theme.mixins.getColor(props.color || 'primary')};
        `;
    },
    subtle: (props) => labelStyles.outlined(props),
    text: (props) => labelStyles.outlined(props),
};

const labelSize = {
    large: {
        fontSize: 16,
        loadingSize: 22,
    },
    medium: {
        fontSize: 15,
        loadingSize: 22,
    },
    small: {
        fontSize: 14,
        loadingSize: 22,
    },
    xsmall: {
        fontSize: 12,
        loadingSize: 22,
    },
};

export function getLabelSize(size: ButtonSize) {
    return labelSize[size];
}

export function getLabelStyle(props: WithTheme<ButtonLabelProps>): ReturnType<typeof css> {
    return (props.buttonVariant ? labelStyles[props.buttonVariant] : labelStyles.contained)(props);
}
