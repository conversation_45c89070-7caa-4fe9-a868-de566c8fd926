import { tint } from 'polished';
import { ReactElement, useMemo } from 'react';
import styled, { useTheme } from 'styled-components/native';
import { CircularProgress } from '../CircularProgress';
import { Typography, TypographyProps } from '../Typography';
import { getLabelSize, getLabelStyle } from './styles';
import { ButtonSize, ButtonVariant } from './types';

export interface ButtonLabelProps extends Omit<TypographyProps, 'disabled'> {
    buttonSize?: ButtonSize;
    buttonVariant?: ButtonVariant;
    disabled?: boolean | null;
    loading?: boolean;
}

function ButtonLabelComponent({ buttonSize, loading, disabled, ...rest }: ButtonLabelProps): ReactElement {
    const { fontSize, loadingSize } = useMemo(() => getLabelSize(buttonSize || 'medium'), [buttonSize]);
    const { mixins } = useTheme();

    if (loading) {
        return (
            <CircularProgress
                indeterminate
                thickness={2}
                spinDuration={1000}
                size={loadingSize}
                color={tint(0.6, mixins.getColor(rest.color) || mixins.getColor('primary'))}
            />
        );
    }

    return <Typography variant={'button'} fontSize={fontSize} disabled={disabled || undefined} {...rest} />;
}

ButtonLabelComponent.displayName = 'ButtonLabelComponent';

export const ButtonLabel = styled(ButtonLabelComponent).withConfig({ displayName: 'ButtonLabel' })`
    ${getLabelStyle}
`;
