/* eslint-disable @typescript-eslint/no-explicit-any */
import { tint } from 'polished';
import { ReactElement, useCallback } from 'react';
import { Pressable, PressableProps, View } from 'react-native';
import { Avatar, AvatarProps } from '~/components/ui/Avatar';
import { makeStyles } from '~/components/ui/makeStyles';
import { Styles } from '~/components/ui/makeStyles/makeStyles';
import { sx } from '~/components/ui/makeStyles/sx';
import { extendChildren } from '~/components/utils/extendChildren';
import { usePressable } from '~/hooks/usePressable';
import { Typography } from '../Typography';

export interface ChipProps<T> extends PressableProps {
    avatar?: ReactElement<AvatarProps, typeof Avatar>;
    checked?: boolean;
    label?: string | number;
    onChecked?: (value: T | null) => void;
    styles?: Partial<Styles<ChipKeyStyles>>;
    value?: T;
}

export function Chip<T>(props: ChipProps<T>) {
    const { label, avatar, value, checked, style, onPress, onChecked, ...rest } = props;
    const [pressed, pressableProps] = usePressable(props);
    const styles = useStyles(props);
    const active = pressed || checked;

    const handlePress = useCallback(
        (e: any) => {
            if (onChecked) {
                onChecked(value ?? null);
            }

            if (onPress) {
                onPress(e);
            }
        },
        [onChecked, onPress, value],
    );

    return (
        <Pressable onPress={handlePress} style={sx(styles.root, style)} {...rest} {...pressableProps}>
            <View style={sx(styles.chip, active && styles.pressed)}>
                {avatar && <View style={styles.avatarHolder}>{extendChildren(avatar, avatarChildrenProps)}</View>}
                <Typography variant={'footnote'} fontWeight={500} style={sx(styles.text, active && styles.pressedText)}>
                    {label}
                </Typography>
            </View>
        </Pressable>
    );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function avatarChildrenProps({ size, ...rest }: AvatarProps): AvatarProps {
    return {
        size: 32,
        ...rest,
    };
}

type ChipKeyStyles = 'root' | 'chip' | 'text' | 'avatarHolder' | 'pressed' | 'pressedText';

const useStyles = makeStyles<ChipProps<any>, Styles<ChipKeyStyles>>(({ theme, checked, avatar, styles }) => ({
    avatarHolder: {
        marginRight: theme.mixins.spacingValue(0.5),
        ...styles?.avatarHolder,
    },
    chip: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: tint(0.9, theme.palette.primary.main),
        borderColor: checked ? theme.palette.accent.main : tint(0.9, theme.palette.primary.main),
        borderRadius: theme.mixins.spacingValue(2.5),
        borderStyle: 'solid',
        borderWidth: 1,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        paddingLeft: theme.mixins.spacingValue(avatar ? 0.5 : 1.5),
        paddingRight: theme.mixins.spacingValue(1.5),
        paddingVertical: theme.mixins.spacingValue(avatar ? 0.5 : 1.25) - 1,
        ...styles?.chip,
    },
    pressed: {
        backgroundColor: tint(0.8, theme.palette.accent.main),
        ...styles?.pressed,
    },
    pressedText: {
        color: theme.palette.accent.main,
        ...styles?.pressedText,
    },
    root: {
        marginBottom: theme.mixins.spacingValue(1),
        marginRight: theme.mixins.spacingValue(1),
        ...styles?.root,
    },
    text: {
        color: theme.palette.primary.main,
        minHeight: theme.mixins.spacingValue(2.5),
        ...styles?.text,
    },
}));
