/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from 'react';
import { Pressable, PressableProps, View } from 'react-native';
import { makeStyles } from '~/components/ui/makeStyles';
import { Styles } from '~/components/ui/makeStyles/makeStyles';
import { sx } from '~/components/ui/makeStyles/sx';
import { usePressable } from '~/hooks/usePressable';
import { Typography } from '../Typography';

export interface ChipProps<T> extends PressableProps {
    checked?: boolean;
    label?: string | number;
    onChecked?: (value: T | null) => void;
    value?: T;
}

export function UserInterestChip<T>(props: ChipProps<T>) {
    const { label, value, checked, style, onPress, onChecked, ...rest } = props;
    const [pressed, pressableProps] = usePressable(props);
    const styles = useStyles(props);
    const active = pressed || checked;

    const handlePress = useCallback(
        (e: any) => {
            if (onChecked) {
                onChecked(value ?? null);
            }

            if (onPress) {
                onPress(e);
            }
        },
        [onChecked, onPress, value],
    );

    return (
        <Pressable onPress={handlePress} style={sx(styles.root, style)} {...rest} {...pressableProps}>
            <View style={sx(styles.chip, active && styles.pressed)}>
                <Typography
                    variant={'caption1'}
                    fontWeight={500}
                    fontSize={12}
                    style={sx(styles.text, active && styles.pressedText)}
                >
                    {label}
                </Typography>
            </View>
        </Pressable>
    );
}

type ChipKeyStyles = 'root' | 'chip' | 'text' | 'avatarHolder' | 'pressed' | 'pressedText';

const useStyles = makeStyles<ChipProps<any>, Styles<ChipKeyStyles>>(({ theme, checked }) => ({
    avatarHolder: {
        marginRight: theme.mixins.spacingValue(0.5),
    },
    chip: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: checked ? theme.palette.accent.main : 'transparent',
        borderRadius: theme.mixins.spacingValue(1),
        borderStyle: 'solid',
        borderWidth: 1,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        paddingLeft: theme.mixins.spacingValue(1.5),
        paddingRight: theme.mixins.spacingValue(1.5),
        paddingVertical: theme.mixins.spacingValue(1.25) - 1,
    },
    pressed: {
        backgroundColor: theme.palette.accent.shade10,
    },
    pressedText: {
        color: theme.palette.accent.main,
    },
    root: {
        marginBottom: theme.mixins.spacingValue(1),
        marginRight: theme.mixins.spacingValue(1),
    },
    text: {
        color: theme.palette.primary.main,
        lineHeight: 15,
    },
}));
