import { ReactElement } from 'react';
import { TouchableOpacity } from 'react-native';
import { useEvent } from '~/hooks/useEvent';
import { useTabs } from '../TabContainer/TabContainerContext';
import { Typography, TypographyProps } from '../Typography';
import { makeStyles, sx } from '../makeStyles';

export interface TabProps {
    children: TypographyProps['children'];
    value: string;

    onPress?(value: string): void;
}

export function Tab({ children, value, onPress }: TabProps): ReactElement {
    const styles = useStyles();
    const tabs = useTabs();

    const isActive = tabs?.currentTab === value;

    const handlePress = useEvent(() => {
        onPress?.(value);
        tabs?.setCurrentTab(value);
    });

    return (
        <TouchableOpacity
            style={sx(styles.root, isActive && styles.activeTab)}
            activeOpacity={0.8}
            onPress={handlePress}
        >
            <Typography variant="caption1" fontWeight={600} color={isActive ? 'textPrimary' : 'textSecondary'}>
                {children}
            </Typography>
        </TouchableOpacity>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        height: 28,
        flexBasis: 0,
        flexGrow: 1,
        flexShrink: 1,
        borderRadius: 8,
        paddingHorizontal: theme.mixins.spacingValue(1),
        alignItems: 'center',
        justifyContent: 'center',
    },
    activeTab: {
        backgroundColor: theme.palette.backgroundPrimary.main,
        ...theme.shadows.small,
    },
}));
