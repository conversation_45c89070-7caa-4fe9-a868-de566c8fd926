import { css } from 'styled-components/native';
import { cssProperty } from '~/components/ui/ThemeProvider/utils';
import { getButtonStyle } from '../Button/styles';
import { ButtonSize } from '../Button/types';
import { WithTheme } from '../ThemeProvider/Theme';
import { IconButtonProps } from './IconButton';

const iconButtonSizes: Record<ButtonSize | 'inherit', (props: WithTheme<IconButtonProps>) => ReturnType<typeof css>> = {
    inherit: (props) => css`
        ${cssProperty('borderRadius', 10, props.theme.mixins.strictUnit)}
    `,
    large: (props) => css`
        ${boxHelper(7, props)}
        ${cssProperty('borderRadius', 18, props.theme.mixins.strictUnit)}
    `,
    medium: (props) => css`
        ${boxHelper(5, props)}
        ${cssProperty('borderRadius', 14, props.theme.mixins.strictUnit)}
    `,
    small: (props) => css`
        ${boxHelper(4.5, props)}
        ${cssProperty('borderRadius', 10, props.theme.mixins.strictUnit)}
    `,
    xsmall: (props) => css`
        ${boxHelper(3.5, props)}
        ${cssProperty('borderRadius', 6, props.theme.mixins.strictUnit)}
    `,
};

const boxHelper = (size: number, { disablePadding, theme }: WithTheme<IconButtonProps>) =>
    disablePadding
        ? ''
        : css`
              height: ${theme.spacing(size)};
              width: ${theme.spacing(size)};
          `;

export function getIconButtonSize(props: WithTheme<IconButtonProps>): ReturnType<typeof css> {
    return (props.size ? iconButtonSizes[props.size] : iconButtonSizes.medium)(props);
}

export function getIconButtonStyle(props: WithTheme<IconButtonProps>): string | ReturnType<typeof css> {
    return getButtonStyle({
        ...props,
        variant: props.variant ?? 'text',
    });
}
