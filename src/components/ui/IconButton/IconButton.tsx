import { useLinkTo } from '@react-navigation/native';
import { To } from '@react-navigation/native/lib/typescript/src/useLinkTo';
import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';
import { ReactElement } from 'react';
import type { GestureResponderEvent, TouchableOpacityProps, ViewStyle } from 'react-native';
import { TouchableOpacity } from 'react-native';
import styled, { css } from 'styled-components/native';
import { useEvent } from '~/hooks/useEvent';
import { IconElement, useResolveIcon } from '~/hooks/useResolveIcon';
import { ButtonSize, ButtonVariant } from '../Button/types';
import { ThemeColorKeys } from '../ThemeProvider/Theme';
import { mixinKeys, shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';
import { boxMixin, boxMixinHelpers, BoxMixinProps } from '../ThemeProvider/mixins/box';
import { marginMixin, marginMixinHelpers, MarginMixinProps } from '../ThemeProvider/mixins/margin';
import { paddingMixin, paddingMixinHelpers, PaddingMixinProps } from '../ThemeProvider/mixins/padding';
import { getIconButtonSize, getIconButtonStyle } from './styles';

export interface IconButtonProps<ParamList extends ReactNavigation.RootParamList = ReactNavigation.RootParamList>
    extends Omit<TouchableOpacityProps, 'children'>,
        MarginMixinProps,
        PaddingMixinProps,
        Omit<BoxMixinProps, 'fullWidth'> {
    borderRadius?: ViewStyle['borderRadius'];
    children: IconElement;
    color?: ThemeColorKeys;
    disablePadding?: boolean;
    disabled?: boolean;
    external?: boolean;
    href?: string;
    iconColor?: ThemeColorKeys;
    loading?: boolean;

    size?: ButtonSize | 'inherit';
    to?: To<ParamList>;
    variant?: ButtonVariant;
}

function IconButtonComponent(props: IconButtonProps): ReactElement {
    const { size, variant, color, loading, disabled, children, onPress, to, href, external, iconColor, ...rest } =
        props;

    const linkTo = useLinkTo();

    const icon$ = useResolveIcon(children, {
        color: iconColor || color,
        disabled,
        size,
        withContrast: !props.disabled && !iconColor && variant === 'contained',
    });

    const handlePress = useEvent(async (e: GestureResponderEvent) => {
        if (onPress) {
            await onPress(e);
        }

        if (to) {
            linkTo(to);
        } else if (href) {
            if (!external) {
                await WebBrowser.openBrowserAsync(href);
            } else {
                await Linking.openURL(href);
            }
        }
    });

    return (
        <TouchableOpacity disabled={loading || disabled} activeOpacity={0.5} onPress={handlePress} {...rest}>
            {icon$}
        </TouchableOpacity>
    );
}

IconButtonComponent.displayName = 'IconButtonComponent';

export const IconButton = styled(IconButtonComponent).withConfig({
    displayName: 'IconButton',
    shouldForwardProp: shouldForwardMixinProps([
        boxMixinHelpers,
        paddingMixinHelpers,
        marginMixinHelpers,
        mixinKeys('fullWidth'),
    ]),
})`
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;

    ${(props) =>
        props.fullWidth
            ? css`
                  width: 100%;
                  flex-basis: 100%;
              `
            : ''}
    ${getIconButtonSize}
    ${getIconButtonStyle}
    ${boxMixin()}
    ${paddingMixin()}
    ${marginMixin()};
`;

IconButton.defaultProps = {
    color: 'primary',
    variant: 'text',
};
