import { tint } from 'polished';
import { View } from 'react-native';
import { Origin } from '~/components/types/Origin';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { Typography, TypographyProps } from '~/components/ui/Typography';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { IconElement, useResolveIcon } from '~/hooks/useResolveIcon';

type BadgeStyles = Styles<'root'>;

export interface BadgeProps extends Omit<TypographyProps, 'children'> {
    absolute?: boolean;
    color?: ThemeColorKeys;
    icon?: IconElement;
    placement?: Partial<Origin>;
    styles?: Partial<BadgeStyles>;
    title?: string;
}

const useStyles = makeStyles<BadgeProps, BadgeStyles>(({ color, absolute, placement, theme }) => ({
    root: {
        alignItems: 'center',
        backgroundColor: tint(0.9, theme.mixins.getColor(color, 'accent')),
        borderRadius: 8,
        justifyContent: 'center',
        overflow: 'hidden',
        position: absolute ? 'absolute' : 'relative',
        ...(absolute ? (placement?.isRight?.() ? { right: 0 } : { left: 0 }) : {}),
        ...(absolute ? (placement?.isBottom?.() ? { bottom: 0 } : { top: 0 }) : {}),
    },
}));

export function Badge(props: BadgeProps) {
    const { title, color, icon, styles: customStyles, absolute, placement, ...rest } = props;
    const styles = useStyles(
        {
            ...props,
            placement: Origin.normalize(placement),
        },
        customStyles,
    );

    const textColor = color || 'accent';
    const iconColor = color || 'accent';

    const icon$ = useResolveIcon(icon, {
        color: iconColor,
    });

    return (
        <View style={styles.root}>
            {title && (
                <Typography
                    variant={'caption1'}
                    fontSize={12}
                    fontWeight={500}
                    color={textColor}
                    px={1}
                    py={3 / 8}
                    {...rest}
                >
                    {title}
                </Typography>
            )}
            {!title && icon$ ? icon$ : null}
        </View>
    );
}
