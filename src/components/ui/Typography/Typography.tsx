import { Animated, Text, TextProps } from 'react-native';
import styled from 'styled-components/native';
import {
    ThemeColorKeys,
    ThemeTypography,
    ThemeTypographyColorKeys,
    ThemeTypographyVariants,
} from '../ThemeProvider/Theme';
import { shouldForwardMixinProps } from '../ThemeProvider/createMixinHelpers';
import { backgroundMixin, backgroundMixinHelpers, BackgroundMixinProps } from '../ThemeProvider/mixins/background';
import { boxMixin, boxMixinHelpers, BoxMixinProps } from '../ThemeProvider/mixins/box';
import { colorMixin, colorMixinHelpers } from '../ThemeProvider/mixins/color';
import { marginMixin, marginMixinHelpers, MarginMixinProps } from '../ThemeProvider/mixins/margin';
import { paddingMixin, paddingMixinHelpers, PaddingMixinProps } from '../ThemeProvider/mixins/padding';

export interface TypographyProps
    extends TextProps,
        Partial<ThemeTypography>,
        PaddingMixinProps,
        MarginMixinProps,
        BoxMixinProps,
        BackgroundMixinProps {
    animated?: boolean;
    color?: ThemeColorKeys | ThemeTypographyColorKeys | string;
    textAlign?: 'left' | 'center' | 'right';
    variant?: ThemeTypographyVariants;
}

function TypographyComponent({ animated, ...rest }: TypographyProps) {
    const Component = animated ? Animated.Text : Text;

    return <Component {...rest} />;
}

export const Typography = styled(TypographyComponent).withConfig({
    shouldForwardProp: shouldForwardMixinProps([
        colorMixinHelpers,
        paddingMixinHelpers,
        marginMixinHelpers,
        backgroundMixinHelpers,
        boxMixinHelpers,
    ]),
})`
    ${(props) => props.theme.mixins.typography(props.variant ?? 'body', props)}
    ${colorMixin('typography.textPrimary', { isTypography: true })}
    text-align: ${(props) => props.textAlign || 'left'};

    ${paddingMixin()}
    ${marginMixin()}
    ${backgroundMixin()}
    ${boxMixin()}
`;
