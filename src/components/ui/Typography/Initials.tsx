import { ReactElement, useMemo } from 'react';
import { Typography, TypographyProps } from './Typography';

export interface InitialsProps extends Omit<TypographyProps, 'children'> {
    count?: number;
    text: string;
}

export function Initials({ text, count = 2, ...rest }: InitialsProps): ReactElement {
    const initials = useMemo(
        () =>
            text
                .split(' ')
                .map((word) => word[0])
                .slice(0, count)
                .join(''),
        [text, count],
    );

    return (
        <Typography textTransform="uppercase" {...rest}>
            {initials}
        </Typography>
    );
}
