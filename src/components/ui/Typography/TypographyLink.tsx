import { ReactElement } from 'react';
import { TouchableOpacityProps } from 'react-native';
import { Link, LinkProps } from '~/components/ui/Link/Link';
import { Typography, TypographyProps } from './Typography';

export type TypographyLinkProps = Omit<LinkProps, keyof TouchableOpacityProps> &
    TypographyProps & {
        touchableOpacityProps?: Omit<TouchableOpacityProps, 'onPress'>;
    };

export function TypographyLink({
    onPress,
    style,
    touchableOpacityProps,
    href,
    external,
    ...rest
}: TypographyLinkProps): ReactElement {
    return (
        <Link onPress={onPress} href={href} external={external} {...touchableOpacityProps}>
            <Typography style={style} variant={'body'} color={'accent'} {...rest} />
        </Link>
    );
}
