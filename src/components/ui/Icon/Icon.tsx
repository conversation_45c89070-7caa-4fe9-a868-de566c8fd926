import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { IconProps as EIconProps } from '@expo/vector-icons/build/createIconSet';
import { ReactElement } from 'react';
import { useTheme } from 'styled-components/native';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { MaterialCommunityIconsName, MaterialIconsName } from '~/types/icons';

export type IconProps = {
    color?: ThemeColorKeys | string;
} & (
    | ({ variant?: never } & EIconProps<MaterialIconsName>)
    | ({ variant?: 'primary' } & EIconProps<MaterialIconsName>)
    | ({ variant?: 'secondary' } & EIconProps<MaterialCommunityIconsName>)
);

const variantComponentMap = {
    primary: MaterialIcons,
    secondary: MaterialCommunityIcons,
};

export function Icon({ color, ...rest }: IconProps): ReactElement {
    const theme = useTheme();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const Component: any = variantComponentMap[rest.variant || 'primary'] ?? variantComponentMap.primary;

    return <Component size={24} color={theme.mixins.getColor(color, 'typography.textPrimary')} {...rest} />;
}
