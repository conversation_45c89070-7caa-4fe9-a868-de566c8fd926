import { ReactElement } from 'react';
import { View, ViewProps } from 'react-native';
import { makeStyles } from '~/components/ui/makeStyles';

export type TabContentProps = ViewProps;

const useStyles = makeStyles(({}) => ({
    root: {
        // Styles
    },
}));

// TODO: implement tab content
export function TabContent({ ...rest }: TabContentProps): ReactElement {
    const styles = useStyles();

    return <View style={styles.root} {...rest} />;
}
