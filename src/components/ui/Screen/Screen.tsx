import BottomTabBarHeightContext from '@react-navigation/bottom-tabs/src/utils/BottomTabBarHeightContext';
import { readableColor } from 'polished';
import { ReactElement, useContext, useEffect, useMemo } from 'react';
import {
    ActivityIndicator,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    ScrollViewProps,
    StatusBar,
    StatusBarProps,
    View,
} from 'react-native';
import { EdgeInsets } from 'react-native-safe-area-context';
import { useTheme } from 'styled-components/native';
import { VStack } from '~/components/ui/Grid';
import { makeStyles, Styles, sx } from '~/components/ui/makeStyles';
import { getContainerPadding } from '~/hooks/useContainerPadding';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { sum } from '~/lib/utils/number';
import { useApplicationStore } from '~/store/useApplicationStore';
import { BackgroundMixinProps } from '../ThemeProvider/mixins/background';
import { PaddingMixinProps, paddings } from '../ThemeProvider/mixins/padding';

export interface ScreenProps extends ScrollViewProps, BackgroundMixinProps, PaddingMixinProps {
    disablePadding?: boolean;
    disableScroll?: boolean;
    loading?: boolean;
    safeArea?: boolean;
    safeBottom?: boolean | number;
    safeTop?: boolean | number;
    statusBarProps?: StatusBarProps;
    statusBarStyle?: StatusBarProps['barStyle'];
}

export function Screen(props: ScreenProps): ReactElement {
    const {
        style,
        bgColor,
        disableScroll,
        disablePadding,
        statusBarStyle,
        statusBarProps,
        safeArea,
        children,
        loading,
        refreshControl,
        ...rest
    } = props;

    const setBottomScreenOffset = useApplicationStore((state) => state.setBottomScreenOffset);
    const insets = useSafeAreaInsets();
    const styles = useStyles({ ...props, insets });
    const theme = useTheme();
    const tabBarHeight = useContext(BottomTabBarHeightContext);

    const bgColor$ = useMemo(() => theme.mixins.getColor(bgColor, 'backgroundPrimary'), [bgColor, theme]);
    const defaultStatusBarStyle = useMemo(
        () => (readableColor(bgColor$, '#000', '#fff') !== '#fff' ? 'dark-content' : 'light-content'),
        [bgColor$],
    );

    const statusBar$ = (
        <StatusBar barStyle={statusBarStyle || defaultStatusBarStyle} backgroundColor={bgColor$} {...statusBarProps} />
    );

    useEffect(() => {
        setBottomScreenOffset(sum(insets?.bottom, tabBarHeight));
    }, [insets?.bottom, setBottomScreenOffset, tabBarHeight]);

    if (disableScroll) {
        return (
            <>
                {statusBar$}
                <View style={sx(styles.container, styles.bgColor, styles.flexGrow, styles.safeArea)} {...rest}>
                    {children}
                </View>
            </>
        );
    }

    return (
        <>
            {statusBar$}
            <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={styles.flex}>
                <ScrollView
                    style={sx(styles.bgColor, styles.container, styles.flexGrow)}
                    contentContainerStyle={sx(styles.safeArea, styles.flexGrow)}
                    keyboardShouldPersistTaps={'handled'}
                    refreshControl={refreshControl}
                    {...rest}
                >
                    {loading ? (
                        <VStack flex alignItems={'center'} justifyContent={'center'}>
                            <ActivityIndicator />
                        </VStack>
                    ) : (
                        children
                    )}
                </ScrollView>
            </KeyboardAvoidingView>
        </>
    );
}

const useStyles = makeStyles<
    ScreenProps & { insets: EdgeInsets },
    Styles<'paddings' | 'bgColor' | 'flex' | 'flexGrow' | 'container' | 'safeArea'>
>((props) => ({
    paddings: {
        ...paddings(props),
    },
    bgColor: {
        backgroundColor: props.theme.mixins.getColor(props.bgColor, 'backgroundPrimary'),
    },
    flex: {
        flex: 1,
    },
    flexGrow: {
        flexGrow: 1,
    },
    container: {
        paddingHorizontal: props.theme.mixins.spacingValue(
            getContainerPadding({
                disabled: props.disablePadding,
            }),
        ),
    },
    safeArea: {
        paddingTop:
            props.safeArea || props.safeTop
                ? props.safeTop
                    ? props.theme.mixins.spacingValue(Number(props.safeTop))
                    : props.insets?.top
                : 0,
        paddingBottom:
            props.safeArea || props.safeBottom
                ? props.safeBottom
                    ? props.theme.mixins.spacingValue(Number(props.safeBottom))
                    : props.insets?.bottom
                : 0,
    },
}));
