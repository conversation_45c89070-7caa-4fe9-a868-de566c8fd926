import { ReactElement } from 'react';
import { NavigateBack } from '~/components/NavigateBack';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { IconElement } from '~/hooks/useResolveIcon';

interface ScreenHeaderBackButtonProps {
    color?: ThemeColorKeys;
    icon?: IconElement;
    onPress?: () => void;
}

export function ScreenHeaderBackButton({ color, icon, onPress }: ScreenHeaderBackButtonProps): ReactElement {
    return (
        <NavigateBack>
            <IconButton size={'large'} disablePadding onPress={onPress}>
                {icon || <Icon name={'arrow-back'} color={color ?? 'typography.textPrimary'} />}
            </IconButton>
        </NavigateBack>
    );
}
