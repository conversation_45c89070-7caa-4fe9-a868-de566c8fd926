import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { storiesOf } from '@storybook/react-native';
import React from 'react';
import { Button } from '../../Button';
import { VStack } from '../../Grid';
import { IconButton } from '../../IconButton';
import { ThemeProvider } from '../../ThemeProvider';
import { Typography } from '../../Typography';
import { Screen } from '../Screen';
import { ScreenHeader } from './index';

function Headers() {
    return (
        <VStack bgColor={'#eee'} p={3} mt={3} fullWidth>
            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Only heading
                </Typography>
                <ScreenHeader headline={'Heading 1'} />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Left Slot + Heading
                </Typography>
                <ScreenHeader
                    headline={'Heading 1'}
                    leftSlot={
                        <IconButton size={'medium'} variant={'text'} disablePadding>
                            <MaterialIcons name={'arrow-back'} />
                        </IconButton>
                    }
                />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Right Slot + Heading
                </Typography>
                <ScreenHeader
                    headline={'Heading 1'}
                    rightSlot={
                        <IconButton size={'medium'} variant={'text'} disablePadding>
                            <MaterialIcons name={'close'} />
                        </IconButton>
                    }
                />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Left Slot
                </Typography>
                <ScreenHeader
                    leftSlot={
                        <IconButton size={'medium'} variant={'text'} disablePadding>
                            <MaterialIcons name={'arrow-back'} />
                        </IconButton>
                    }
                />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Right Slot
                </Typography>
                <ScreenHeader
                    rightSlot={
                        <IconButton size={'medium'} variant={'text'} disablePadding>
                            <MaterialIcons name={'close'} />
                        </IconButton>
                    }
                />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Headline + Headline action
                </Typography>
                <ScreenHeader
                    headline={'Headline 2'}
                    headlineTypographyProps={{ variant: 'title2' }}
                    headlineActions={
                        <IconButton size={'medium'} variant={'text'} disablePadding>
                            <MaterialIcons name={'notifications'} />
                        </IconButton>
                    }
                />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Headline + Headline actions
                </Typography>
                <ScreenHeader
                    headline={'Headline 2'}
                    headlineTypographyProps={{ variant: 'title2' }}
                    headlineActions={
                        <>
                            <IconButton size={'medium'} variant={'text'} mr={1} disablePadding>
                                <MaterialIcons name={'refresh'} />
                            </IconButton>
                            <IconButton size={'medium'} variant={'text'} disablePadding>
                                <MaterialIcons name={'notifications'} />
                            </IconButton>
                        </>
                    }
                />
            </VStack>

            <VStack mb={3}>
                <Typography variant={'caption1'} color={'textSecondary'}>
                    Headline + Headline actions (button)
                </Typography>
                <ScreenHeader
                    headline={'Headline 2'}
                    headlineTypographyProps={{ variant: 'title2' }}
                    headlineActions={
                        <Button
                            label={'Add'}
                            size={'xsmall'}
                            color={'accent'}
                            startIcon={<MaterialIcons name={'add'} />}
                        />
                    }
                />
            </VStack>
        </VStack>
    );
}

storiesOf('Screen', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen disablePadding>
                <VStack px={3}>
                    <Typography variant={'title3'} fontWeight={700} mt={4}>
                        Screen - Headers
                    </Typography>
                    <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500}>
                        Headers will be the first component in the screen that user sees.
                    </Typography>
                </VStack>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Headers', Headers);
