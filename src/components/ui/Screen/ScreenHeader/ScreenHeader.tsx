import { BlurView, BlurViewProps } from 'expo-blur';
import { ReactElement, ReactNode, useMemo } from 'react';
import { View } from 'react-native';
import { SafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';
import styled, { useTheme } from 'styled-components/native';
import { Grid, GridProps, HStack, VStack, VStackProps } from '../../Grid';
import { shouldForwardMixinProps } from '../../ThemeProvider/createMixinHelpers';
import { marginMixin, marginMixinHelpers } from '../../ThemeProvider/mixins/margin';
import { Typography, TypographyProps } from '../../Typography';

export interface ScreenHeaderProps extends VStackProps {
    blur?: boolean | BlurViewProps;
    caption?: string;
    captionTypographyProps?: TypographyProps;
    disableSafeViewArea?: boolean;
    headline?: string;
    headlineActions?: ReactNode | ReactNode[];
    headlineActionsGridProps?: GridProps;
    headlineGridProps?: GridProps;
    headlineTypographyProps?: TypographyProps;
    leftSlot?: ReactNode | ReactNode[];
    leftSlotGridProps?: GridProps;
    rightSlot?: ReactNode | ReactNode[];
    rightSlotGridProps?: GridProps;
    safeAreaViewProps?: SafeAreaViewProps;
}

function withBlurView(blur: ScreenHeaderProps['blur'], children: ReactElement): ReactElement {
    if (blur) {
        return (
            <View style={{ backgroundColor: 'rgba(255,255,255,.9)' }}>
                <BlurView intensity={30} tint="light" {...(typeof blur === 'object' ? blur : {})}>
                    {children}
                </BlurView>
            </View>
        );
    }

    return <>{children}</>;
}

function ScreenHeaderComponent({
    blur,
    children,
    headline,
    headlineTypographyProps,
    headlineGridProps,
    headlineActions,
    headlineActionsGridProps,
    caption,
    captionTypographyProps,
    leftSlot,
    rightSlot,
    leftSlotGridProps,
    rightSlotGridProps,
    safeAreaViewProps,
    bgColor,
    style,
    disableSafeViewArea,
    ...rest
}: ScreenHeaderProps): ReactElement {
    const theme = useTheme();
    const hasLeftSlot = Array.isArray(leftSlot) ? leftSlot.length > 0 : !!leftSlot;
    const hasRightSlot = Array.isArray(rightSlot) ? rightSlot.length > 0 : !!rightSlot;
    const hasHeadlineActions = Array.isArray(headlineActions) ? headlineActions.length > 0 : !!headlineActions;

    const backgroundColor = useMemo(
        () => theme.mixins.getColor(bgColor, blur ? 'transparent' : 'backgroundPrimary'),
        [blur, bgColor, theme.mixins],
    );

    const safeAreaProps = useMemo(
        () => ({
            ...safeAreaViewProps,
            style: [style, safeAreaViewProps?.style, { backgroundColor }],
        }),
        [backgroundColor, safeAreaViewProps, style],
    );

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const restVStackProps = marginMixinHelpers.omitProps(rest) as Record<string, any>;

    const children$ = (
        <HStack pt={1} bgColor={bgColor}>
            <VStack fullWidth flexWrap={'nowrap'} pb={1.75} {...restVStackProps}>
                {hasLeftSlot || hasRightSlot ? (
                    <HStack alignItems={'center'} mb={headline ? 2 : 0}>
                        {hasLeftSlot ? (
                            <Grid
                                container
                                xs
                                alignItems={'center'}
                                justifyContent={'flex-start'}
                                mr={rightSlot ? 1 : 0}
                                {...leftSlotGridProps}
                            >
                                <>{leftSlot}</>
                            </Grid>
                        ) : null}
                        {hasRightSlot ? (
                            <Grid
                                container
                                xs
                                alignItems={'center'}
                                justifyContent={'flex-end'}
                                ml={leftSlot ? 1 : 0}
                                {...rightSlotGridProps}
                            >
                                <>{rightSlot}</>
                            </Grid>
                        ) : null}
                    </HStack>
                ) : null}

                {headline || caption || hasHeadlineActions ? (
                    <HStack alignItems={'center'} alignContent={'center'} flexWrap={'nowrap'}>
                        <VStack
                            flexGrow={1}
                            flexDirection={'column'}
                            alignContent={'flex-start'}
                            mr={rightSlot ? 1 : 0}
                            flexShrink={1}
                            {...headlineGridProps}
                        >
                            {headline ? (
                                <Typography variant={'title1'} fontWeight={700} {...headlineTypographyProps}>
                                    {headline}
                                </Typography>
                            ) : null}

                            {caption ? (
                                <Typography
                                    variant={'subhead'}
                                    fontWeight={500}
                                    color={'textSecondary'}
                                    mt={headline ? 0.5 : 0}
                                    {...captionTypographyProps}
                                >
                                    {caption}
                                </Typography>
                            ) : null}
                        </VStack>

                        {hasHeadlineActions ? (
                            <HStack
                                alignItems={'center'}
                                justifyContent={'flex-end'}
                                mr={rightSlot ? 1 : 0}
                                {...headlineActionsGridProps}
                            >
                                <>{headlineActions}</>
                            </HStack>
                        ) : null}
                    </HStack>
                ) : null}

                {children}
            </VStack>
        </HStack>
    );

    if (disableSafeViewArea) {
        return withBlurView(blur, children$);
    }

    return withBlurView(
        blur,
        <SafeAreaView edges={['top']} {...safeAreaProps}>
            {children$}
        </SafeAreaView>,
    );
}

export const ScreenHeader = styled(ScreenHeaderComponent).withConfig({
    shouldForwardProp: shouldForwardMixinProps([marginMixinHelpers]),
})`
    z-index: 10;
    ${marginMixin()}
`;
