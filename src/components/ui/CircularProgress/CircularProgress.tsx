import { CircleSnail, CircleSnailPropTypes } from 'react-native-progress';
import { useTheme } from 'styled-components/native';
import { ThemeColorKeys } from '../ThemeProvider/Theme';

export interface CircularProgressProps extends CircleSnailPropTypes {
    color?: ThemeColorKeys | string;
}

export function CircularProgress({ color, ...rest }: CircularProgressProps) {
    const theme = useTheme();

    return <CircleSnail size={24} color={theme.mixins.getColor(color, 'accent')} {...rest} />;
}
