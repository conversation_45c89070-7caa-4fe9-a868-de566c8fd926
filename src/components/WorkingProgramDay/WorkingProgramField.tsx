import { useFormikContext } from 'formik';
import { set } from 'lodash';
import { transparentize } from 'polished';
import { memo, ReactElement, useMemo } from 'react';
import SegmentedPicker, { Selections } from 'react-native-segmented-picker';
import { useTheme } from 'styled-components/native';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { Icon } from '~/components/ui/Icon';
import { IconButton } from '~/components/ui/IconButton';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { Typography } from '../ui/Typography';
import { formatTime, GeneratedHours, time } from '../utils/time';

interface WorkingProgramFieldProps {
    collectionName: string;
    name: string;
}

function WorkingProgramFieldComponent({ collectionName, name }: WorkingProgramFieldProps): ReactElement {
    const theme = useTheme();
    const modal = useModal();

    const formik = useFormikContext();
    const { value: start = '09:00' } = formik.getFieldMeta<string>(`${name}.start`);
    const { value: end = '18:00' } = formik.getFieldMeta<string>(`${name}.end`);

    const formattedStart = useMemo(() => formatTime(start), [start]);
    const formattedEnd = useMemo(() => formatTime(end), [end]);

    const handleConfirm = useEvent((selection: Selections) => {
        let start = time(selection.start);
        let end = time(selection.end);

        if (start.isSame(end)) {
            end = end.add(5, 'minutes');
        }

        if (start.isAfter(end)) {
            [end, start] = [start, end];
        }

        formik.setFieldValue(
            `${name}`,
            {
                end: end.format('HH:mm'),
                start: start.format('HH:mm'),
            },
            false,
        );

        modal.close();
    });

    const handleDeletePress = useEvent(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { value } = formik.getFieldMeta<any[]>(collectionName);
        const partialKey = name.replace(collectionName, '').replace(/^\./, '');

        set(value, partialKey, null);

        formik.setFieldValue(collectionName, value.filter(Boolean), true);
    });

    return (
        <>
            <FormFieldBox onPress={modal.open} justifyContent={'space-between'} pr={1} pl={2} my={1} minHeight={36}>
                <Typography variant={'button'} fontWeight={500}>{`${formattedStart} - ${formattedEnd}`}</Typography>

                <IconButton color={'contentTertiary'} onPress={handleDeletePress}>
                    <Icon name={'delete-outline'} />
                </IconButton>
            </FormFieldBox>
            <SegmentedPicker
                visible={modal.isOpen}
                onCancel={modal.close}
                onConfirm={handleConfirm}
                backgroundColor={theme.palette.backgroundPrimary.main}
                confirmTextColor={theme.palette.accent.main}
                pickerItemTextColor={theme.palette.typography.textPrimary}
                toolbarBackgroundColor={theme.palette.backgroundSecondary.main}
                toolbarBorderColor={theme.palette.borderOpaque.main}
                selectionBorderColor={theme.palette.accent.main}
                selectionBackgroundColor={transparentize(0.9, theme.palette.accent.main)}
                defaultSelections={{
                    end,
                    start,
                }}
                options={[
                    {
                        items: GeneratedHours,
                        key: 'start',
                    },
                    {
                        items: GeneratedHours,
                        key: 'end',
                    },
                ]}
            />
        </>
    );
}

export const WorkingProgramField = memo(WorkingProgramFieldComponent);
