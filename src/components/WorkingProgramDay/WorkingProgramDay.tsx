/* eslint-disable @typescript-eslint/no-explicit-any */
import { useFormikContext } from 'formik';
import moment from 'moment';
import { memo, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { WorkingProgramField } from '~/components/WorkingProgramDay/WorkingProgramField';
import { Button } from '~/components/ui/Button';
import { FormikCheckbox } from '~/components/ui/Checkbox';
import { Divider } from '~/components/ui/Divider';
import { HStack, VStack } from '~/components/ui/Grid';
import { Icon } from '~/components/ui/Icon';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useEvent } from '~/hooks/useEvent';

export interface WorkingProgramDayProps {
    divider?: boolean;
    label: string;
    name: string;
}

function WorkingProgramDayComponent({ name, label, divider }: WorkingProgramDayProps): ReactElement {
    const formik = useFormikContext();
    const { t } = useTranslation();
    const { value } = formik.getFieldMeta<any[]>(`${name}.values`);
    const containerPadding = useContainerPadding();

    const handleAddPress = useEvent(() => {
        const { value: values } = formik.getFieldMeta<any[]>(`${name}.values`);
        const last = values[values.length - 1];
        // In case of no last time, take the next hour as default
        const time = last ? moment(last.end, 'HH:mm') : moment().startOf('hour').add(30, 'minutes');

        formik.setFieldValue(
            `${name}.values`,
            [
                ...values,
                {
                    end: time.clone().add(60, 'minutes').format('HH:mm'),
                    start: time.clone().add(30, 'minutes').format('HH:mm'),
                },
            ],
            true,
        );
    });

    return (
        <>
            <VStack px={containerPadding} pb={1} flexWrap={'nowrap'}>
                <HStack mx={-1.5} flexWrap={'nowrap'} justifyContent={'space-between'} alignItems={'center'}>
                    <FormikCheckbox
                        name={`${name}.enabled`}
                        label={label}
                        noFeedback
                        disablePadding={false}
                        labelProps={{ ml: 1 }}
                    />

                    <Button
                        size={'xsmall'}
                        variant={'subtle'}
                        color={'accent'}
                        onPress={handleAddPress}
                        mr={2.75}
                        px={1}
                        startIcon={<Icon name={'add'} />}
                        label={t('addNew')}
                    />
                </HStack>
                <VStack>
                    {value.map((_, index: number) => (
                        <WorkingProgramField
                            key={index}
                            collectionName={`${name}.values`}
                            name={`${name}.values.${index}`}
                        />
                    ))}
                </VStack>
            </VStack>
            {divider ? <Divider /> : null}
        </>
    );
}

export const WorkingProgramDay = memo(WorkingProgramDayComponent);
