import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment';
import { transparentize } from 'polished';
import { ReactElement, useMemo, useState } from 'react';
import { Pressable } from 'react-native';
import { useTheme } from 'styled-components/native';
import { formatDate } from '@bookr-technologies/core';
import { Nullable } from '@bookr-technologies/core/types';
import { BottomSheet } from '~/components/BottomSheet';
import { Calendar, DateData } from '~/components/Calendar';
import { VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';
import { useLayout } from '~/hooks/useLayout';
import { useModal } from '~/hooks/useModal';
import { useWatchValue } from '~/hooks/useWatch';
import { makeStyles } from '../ui/makeStyles';

export interface BottomSheetDatePickerProps {
    label?: string;
    onChange?: (value: string) => void;
    today?: boolean;
    value?: Nullable<string>;
}

const DateFormat = 'YYYY-MM-DD';

export function BottomSheetDatePicker({ value, today, label, onChange }: BottomSheetDatePickerProps): ReactElement {
    const styles = useStyles();
    const layout = useLayout();
    const theme = useTheme();
    const modal = useModal();

    const [current, setCurrent] = useState(() => {
        if (!value && today) {
            return new Date().toISOString();
        }

        return value || '';
    });

    const handleDayPress = useEvent(({ dateString }: DateData) => {
        const date = moment(dateString, DateFormat).toISOString(true);
        setCurrent(date);
        if (onChange) {
            onChange(date);
        }

        modal.close();
    });

    const labelText = useMemo(() => (current ? formatDate(current, 'll') : label), [current, label]);

    useWatchValue(value, (nextValue) => setCurrent((prevValue) => nextValue || prevValue));

    return (
        <>
            <Pressable style={styles.root} onPress={modal.open}>
                <MaterialIcons name={'event-note'} size={24} color={theme.palette.typography.textPrimary} />
                <Typography px={1}>{labelText}</Typography>
            </Pressable>

            <BottomSheet snapPoints={[508]} {...modal.props}>
                <VStack flex mx={3} onLayout={layout.handler}>
                    <Typography variant={'title2'} fontWeight={700} mb={2}>
                        {label}
                    </Typography>
                    {layout.initialized && (
                        <Calendar
                            current={formatDate(current, DateFormat)}
                            markingType={'period'}
                            onDayPress={handleDayPress}
                            calendarWidth={layout.width}
                        />
                    )}
                </VStack>
            </BottomSheet>
        </>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    backdrop: {
        backgroundColor: transparentize(0.4, theme.palette.contentPrimary.main),
        flex: 1,
    },
    root: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: theme.mixins.spacingValue(1.5),
        borderStyle: 'solid',
        borderWidth: 1,
        flexDirection: 'row',
        flexGrow: 1,
        height: theme.mixins.spacingValue(7),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(2),
        width: '100%',
    },
}));

BottomSheetDatePicker.DateFormat = DateFormat;
