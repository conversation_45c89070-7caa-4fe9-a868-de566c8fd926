import { useFormikContext } from 'formik';
import { debounce } from 'lodash';
import { useLogger } from '~/hooks/useLogger';

const debouncedCall = debounce((func) => func(), 500);

export function FormikDebug(): null {
    const formik = useFormikContext();
    const logger = useLogger('FormikDebug');

    debouncedCall(() => {
        logger.log('form values', {
            values: formik.values,
        });
    });

    return null;
}
