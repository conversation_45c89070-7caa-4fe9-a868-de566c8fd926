import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useMemo } from 'react';
import { Modal } from 'react-native';
import { WebView, WebViewProps } from 'react-native-webview';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { makeStyles } from '~/components/ui/makeStyles';
import { ModalStateProps } from '~/hooks/useModal';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';

export interface VirtualTourModalProps extends ModalStateProps {
    source: WebViewProps['source'] | string;
}

export function VirtualTourModal({ open, source, onClose }: VirtualTourModalProps): ReactElement {
    const styles = useStyles();
    const insets = useSafeAreaInsets(true, { min: 0 });

    const src = useMemo(() => {
        if (typeof source === 'string') {
            return { uri: source };
        }

        return source;
    }, [source]);

    return (
        <Modal visible={open} animationType={'fade'}>
            <VStack flex bgColor={'backgroundPrimary'} pt={insets.top} pb={insets.bottom}>
                <HStack px={3} pb={1} justifyContent={'flex-end'}>
                    <IconButton onPress={onClose} disablePadding>
                        <MaterialIcons name={'close'} />
                    </IconButton>
                </HStack>
                <WebView source={src} style={styles.webView} />
            </VStack>
        </Modal>
    );
}

const useStyles = makeStyles(() => ({
    root: {},
    webView: {
        flex: 1,
    },
}));
