import { ReactElement } from 'react';
import { Pressable } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';

interface Props {
    active: boolean;
    index: number;
    onPress: (index: number) => void;
    source: string;
}

const useStyles = makeStyles(({ theme }) => ({
    imageThumbnail: {
        width: theme.mixins.spacingValue(6),
        height: theme.mixins.spacingValue(6),
        marginLeft: theme.mixins.spacingValue(1),
        borderRadius: 3,
        opacity: 0.8,
    },
    imageThumbnailActive: {
        opacity: 1,
    },
}));

export function ThumbnailImage({ source, active, onPress, index }: Props): ReactElement {
    const styles = useStyles();

    const handleOnPress = useEvent(() => {
        onPress(index);
    });

    return (
        <Pressable onPress={handleOnPress}>
            <Image uri={source} style={sx(styles.imageThumbnail, active && styles.imageThumbnailActive)} />
        </Pressable>
    );
}
