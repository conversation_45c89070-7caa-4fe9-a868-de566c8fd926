import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useRef, useState } from 'react';
import { Modal } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import PagerView, { PagerViewOnPageSelectedEvent } from 'react-native-pager-view';
import { LightboxImage } from '~/components/LightboxModal/LightboxImage';
import { ThumbnailImage } from '~/components/LightboxModal/ThumbnailImage';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';

export interface LightboxModalProps {
    images: LightboxImage[];
    initialIndex?: number;
    onClose: () => void;
    open: boolean;
}

const useStyles = makeStyles(() => ({
    root: {},
    image: {
        width: '100%',
        height: '100%',
    },
    pager: {
        flex: 1,
    },
}));

export function LightboxModal({ open, onClose, initialIndex = 0, images }: LightboxModalProps): ReactElement {
    const styles = useStyles();
    const insets = useSafeAreaInsets(true);
    const pagerRef = useRef<PagerView>(null);

    const [currentIndex, setCurrentIndex] = useState<number>(initialIndex);
    const current = images[initialIndex];

    const handlePageSelected = useEvent((event: PagerViewOnPageSelectedEvent) => {
        setCurrentIndex(event.nativeEvent.position);
    });

    const handleThumbnailPress = useEvent((index: number) => {
        setCurrentIndex(index);
        pagerRef.current?.setPage(index);
    });

    return (
        <Modal visible={open} onDismiss={onClose} animationType={'slide'} style={styles.root}>
            <VStack flex bgColor={'backgroundSecondary'}>
                <HStack
                    pt={insets.top}
                    px={2}
                    pb={2}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                    bgColor={'backgroundPrimary'}
                >
                    <VStack pr={2} flex>
                        {current?.title && <Typography variant={'body'}>{current?.title}</Typography>}
                        {current?.subtitle && (
                            <Typography variant={'caption2'} color={'textSecondary'}>
                                {current?.subtitle}
                            </Typography>
                        )}
                    </VStack>

                    <IconButton onPress={onClose} disablePadding>
                        <MaterialIcons name={'close'} />
                    </IconButton>
                </HStack>
                <PagerView
                    initialPage={initialIndex}
                    style={styles.pager}
                    onPageSelected={handlePageSelected}
                    ref={pagerRef}
                >
                    {images.map(({ source }, index) => (
                        <VStack key={index} flex>
                            {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
                            {/* @ts-ignore */}
                            <Image uri={source} style={styles.image} resizeMode={'contain'} />
                        </VStack>
                    ))}
                </PagerView>

                <HStack flexWrap={'nowrap'} pt={2} pb={insets.bottom} bgColor={'backgroundPrimary'}>
                    <HStack
                        pr={2}
                        pl={1}
                        flexWrap={'nowrap'}
                        scrollable
                        scrollViewProps={{
                            horizontal: true,
                            showsHorizontalScrollIndicator: false,
                        }}
                    >
                        {images.map(({ thumbnail, source }, index) => (
                            <ThumbnailImage
                                key={index}
                                source={thumbnail || source}
                                active={currentIndex === index}
                                index={index}
                                onPress={handleThumbnailPress}
                            />
                        ))}
                    </HStack>
                </HStack>
            </VStack>
        </Modal>
    );
}
