import { ReactNode } from 'react';
import { BottomSheetProps } from '~/components/BottomSheet';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { IconElement } from '~/hooks/useResolveIcon';
import { BottomSheetAlertVariant } from './data';

export type BottomSheetAlertVariantType = 'error' | 'success' | 'warning' | 'info';

export interface InfoVariantProps {
    variant?: BottomSheetAlertVariant.Info;
}

export interface ErrorVariantProps {
    variant?: BottomSheetAlertVariant.Error;
}

export interface WarningVariantProps {
    variant?: BottomSheetAlertVariant.Warning;
}

export interface SuccessVariantProps {
    variant?: BottomSheetAlertVariant.Success;
}

export type BottomSheetAlertProps = Omit<BottomSheetProps, 'children'> & {
    children?: ReactNode;
    headline?: string;
    icon?: IconElement;
    iconColor?: ThemeColorKeys;
    iconSize?: number;
    onAction?: () => void;
    onCancel?: () => void;
    primaryColor?: ThemeColorKeys;
    primaryText?: string;
    secondaryText?: string;
    subHeadline?: string;
} & (InfoVariantProps | ErrorVariantProps | SuccessVariantProps | WarningVariantProps);
