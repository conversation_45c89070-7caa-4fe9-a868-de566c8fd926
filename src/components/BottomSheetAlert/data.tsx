import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { IconElement } from '~/hooks/useResolveIcon';

export enum BottomSheetAlertVariant {
    Error = 'error',
    Info = 'info',
    Success = 'success',
    Warning = 'warning',
}

export const VariantIconsMap: Record<BottomSheetAlertVariant, IconElement> = {
    [BottomSheetAlertVariant.Info]: <MaterialIcons name={'info-outline'} />,
    [BottomSheetAlertVariant.Error]: <MaterialIcons name={'error-outline'} />,
    [BottomSheetAlertVariant.Success]: <MaterialIcons name={'check'} />,
    [BottomSheetAlertVariant.Warning]: <MaterialIcons name={'warning'} />,
};

export const VariantColorMap: Record<BottomSheetAlertVariant, ThemeColorKeys> = {
    [BottomSheetAlertVariant.Info]: 'accent',
    [BottomSheetAlertVariant.Error]: 'error',
    [BottomSheetAlertVariant.Success]: 'success',
    [BottomSheetAlertVariant.Warning]: 'warning',
};
