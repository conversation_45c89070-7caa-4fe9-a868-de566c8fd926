import { useBottomSheetDynamicSnapPoints } from '@gorhom/bottom-sheet';
import { tint } from 'polished';
import React, { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components/native';
import { useEvent } from '~/hooks/useEvent';
import { useLoading } from '~/hooks/useLoading';
import { BottomSheet } from '../BottomSheet';
import { Avatar } from '../ui/Avatar';
import { Button } from '../ui/Button';
import { Grid, HStack, VStack } from '../ui/Grid';
import { Spacer } from '../ui/Spacer';
import { Typography } from '../ui/Typography';
import { makeStyles } from '../ui/makeStyles';
import { BottomSheetAlertVariant, VariantColorMap, VariantIconsMap } from './data';
import { BottomSheetAlertProps } from './types';

export function BottomSheetAlert(props: BottomSheetAlertProps): ReactElement {
    const {
        children,
        icon,
        iconColor,
        iconSize,
        headline,
        subHeadline,
        primaryText,
        secondaryText,
        primaryColor,
        variant = BottomSheetAlertVariant.Info,
        onAction,
        onCancel,
        ...rest
    } = props;

    const loading = useLoading<'primary' | 'secondary'>();
    const styles = useStyles(props);
    const theme = useTheme();
    const { t } = useTranslation();
    const variantIcon = useMemo(() => VariantIconsMap[variant], [variant]);
    const variantColor = useMemo(() => primaryColor ?? VariantColorMap[variant], [primaryColor, variant]);
    const avatarColor = useMemo(() => tint(0.8, theme.mixins.getColor(variantColor)), [variantColor, theme.mixins]);

    const handlePrimaryPress = useEvent(async () => {
        try {
            loading.start('primary');
            await onAction?.();
            rest.onClose?.();
        } finally {
            loading.stop('primary');
        }
    });

    const handleSecondaryPress = useEvent(async () => {
        try {
            loading.start('secondary');
            await onCancel?.();
            rest.onClose?.();
        } finally {
            loading.stop('secondary');
        }
    });

    const hasSecondary = !!onCancel || secondaryText;

    const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

    const { animatedHandleHeight, animatedSnapPoints, animatedContentHeight, handleContentLayout } =
        useBottomSheetDynamicSnapPoints(initialSnapPoints);

    return (
        <BottomSheet
            snapPoints={animatedSnapPoints}
            handleHeight={animatedHandleHeight}
            contentHeight={animatedContentHeight}
            {...rest}
        >
            <VStack
                flex
                p={4}
                pb={3}
                justifyContent={'center'}
                alignContent={'center'}
                alignItems={'center'}
                style={styles.root}
                onLayout={handleContentLayout}
            >
                <Spacer />

                <Avatar
                    icon={icon || variantIcon}
                    iconSize={iconSize ?? 64}
                    iconColor={iconColor ?? variantColor}
                    size={88}
                    bgColor={avatarColor}
                    mb={3}
                />

                {headline ? (
                    <Typography variant={'title2'} fontWeight={700} mb={1} textAlign={'center'} fullWidth>
                        {headline}
                    </Typography>
                ) : null}
                {subHeadline ? (
                    <Typography
                        variant={'subhead'}
                        fontWeight={500}
                        color={'textSecondary'}
                        textAlign={'center'}
                        fullWidth
                    >
                        {subHeadline}
                    </Typography>
                ) : null}

                {children}

                <HStack py={4} flexWrap={'nowrap'}>
                    {!loading.any() && hasSecondary && (
                        <Grid xs mr={0.5}>
                            <Button
                                onPress={handleSecondaryPress}
                                variant={'subtle'}
                                loading={loading.isLoading('primary')}
                                disabled={loading.any()}
                                label={secondaryText ?? t('cancel')}
                                size={'large'}
                                px={2}
                            />
                        </Grid>
                    )}

                    <Grid xs ml={loading.any() || !hasSecondary ? 0 : 0.5}>
                        <Button
                            onPress={handlePrimaryPress}
                            variant={hasSecondary ? 'contained' : 'subtle'}
                            loading={loading.isLoading('primary')}
                            disabled={loading.any()}
                            label={primaryText ?? t('ok')}
                            color={variantColor}
                            size={'large'}
                            px={2}
                        />
                    </Grid>
                </HStack>
            </VStack>
        </BottomSheet>
    );
}

const useStyles = makeStyles(() => ({
    root: {},
}));
