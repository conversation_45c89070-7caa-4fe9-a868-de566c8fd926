import Constants, { AppOwnership } from 'expo-constants';
import { requestTrackingPermissionsAsync } from 'expo-tracking-transparency';
import { useEffect } from 'react';
import { Settings } from 'react-native-fbsdk-next';
import { useLogger } from '~/hooks/useLogger';
import { setAnalyticsCollectionEnabled } from '~/lib/analytics/analytics';

export function InitializeTrackUserConsent(): null {
    const logger = useLogger('InitializeTrackUserConsent');

    useEffect(() => {
        (async (): Promise<void> => {
            try {
                const { status } = await requestTrackingPermissionsAsync();
                Settings.initializeSDK();
                if (status === 'granted') {
                    setAnalyticsCollectionEnabled(Constants.appOwnership !== AppOwnership.Expo);
                }
            } catch (error: any) {
                logger.error('Error Initializing Track User Consent', error);
            }
        })();
    }, [logger]);

    return null;
}
