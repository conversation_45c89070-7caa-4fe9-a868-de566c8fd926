import { ReactElement } from 'react';
import { Path } from 'react-native-svg';
import { SvgIcon, SvgIconProps } from '~/components/icons/SvgIcon';

export type BookrProps = SvgIconProps;

export function BookrIcon({ ...rest }: BookrProps): ReactElement {
    return (
        <SvgIcon {...rest}>
            <Path d="M18.1063 13.5872L16.698 12.0866C17.773 11.2146 18.4646 9.86666 18.4646 8.35753C18.4646 5.73416 16.3752 3.59961 13.8074 3.59961H4.80005V20.3996H15.2553C17.4303 20.3996 19.2 18.5927 19.2 16.3706C19.2 15.2927 18.7832 14.3108 18.1063 13.5872ZM7.41177 6.2678H13.8074C14.9346 6.2678 15.8529 7.20487 15.8529 8.35753C15.8529 9.11209 15.459 9.77487 14.8719 10.142L14.5439 9.79195L14.5303 9.77807L14.361 9.59664L14.3516 9.58703C13.7436 8.99042 12.9173 8.62328 12.0095 8.62328C11.9321 8.62328 11.8548 8.62648 11.7786 8.63075C10.9063 8.69159 10.1238 9.09075 9.55652 9.70123L9.39146 9.86986L7.41282 11.9094V6.2678H7.41177ZM15.2553 17.7314H7.41177V15.6993L9.14073 13.917L10.9167 12.0877L11.4338 11.554C11.5749 11.3929 11.7807 11.2915 12.0095 11.2915C12.2017 11.2915 12.3772 11.363 12.513 11.4836L12.5151 11.4857L12.6143 11.5914L13.6465 12.6917H13.6475L14.0403 13.1101L14.8458 13.9682L16.1976 15.408L16.2216 15.4336C16.4494 15.678 16.5883 16.0088 16.5883 16.3706C16.5883 17.1209 15.9897 17.7314 15.2553 17.7314Z" />
        </SvgIcon>
    );
}

export default BookrIcon;
