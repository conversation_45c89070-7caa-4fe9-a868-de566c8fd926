import * as React from 'react';
import { ReactElement } from 'react';
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Path } from 'react-native-svg';
import { SvgIconProps } from '~/components/icons/SvgIcon';

export type RatingIconProps = SvgIconProps;

export const RatingIcon = ({ ...rest }: RatingIconProps): ReactElement => (
    <Svg width={17} height={16} viewBox="0 0 17 16" fill="none" {...rest}>
        <G clipPath="url(#clip0_3323_19175)">
            <Path
                d="M9.532 6.667l-.98-3.227c-.193-.633-1.086-.633-1.273 0l-.987 3.227H3.326c-.647 0-.914.833-.387 1.206l2.427 1.734-.954 3.073c-.193.62.527 1.12 1.04.727l2.46-1.867 2.46 1.873c.514.394 1.234-.106 1.04-.726l-.953-3.074 2.427-1.733c.526-.38.26-1.207-.387-1.207H9.532v-.006z"
                fill="#111"
            />
        </G>
        <Defs>
            <ClipPath id="clip0_3323_19175">
                <Path fill="#fff" transform="translate(.637)" d="M0 0H16V16H0z" />
            </ClipPath>
        </Defs>
    </Svg>
);
