import { ReactNode, useMemo } from 'react';
import { Svg } from 'react-native-svg';
import { useTheme } from 'styled-components/native';
import { ThemeColorKeys } from '../ui/ThemeProvider/Theme';

export interface SvgIconProps {
    children?: ReactNode;
    color?: ThemeColorKeys;
    size?: number;
}

export function SvgIcon({ children, size = 24, color = 'typography.textPrimary' }: SvgIconProps) {
    const theme = useTheme();
    const color$ = useMemo(() => theme.mixins.getColor(color), [theme, color]);

    return (
        <Svg width={size} height={size} viewBox="0 0 24 24" fill={color$}>
            {children}
        </Svg>
    );
}
