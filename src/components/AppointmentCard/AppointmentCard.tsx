/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Badge } from '~/components/ui/Badge';
import { appointmentColors, getAppointmentStatus } from '~/lib/utils/appointments';
import { Grid, GridProps } from '../ui/Grid';
import { Typography } from '../ui/Typography';

interface AppointmentCardProps extends GridProps {
    appointment: AppointmentModel;
    businessView?: boolean;
    client?: UserModel;
    onPress?: () => void;
}

export function AppointmentCard({
    appointment,
    client,
    onPress,
    businessView,
    ...rest
}: AppointmentCardProps): ReactElement {
    const { t } = useTranslation();
    const { navigate } = useNavigation();
    const serviceStartTime = moment(appointment.dateTime).format('HH:mm');
    const serviceEndTime = moment(appointment.dateTime).add(appointment.service.duration, 'minutes').format('HH:mm');
    const appointmentStatus = getAppointmentStatus(appointment);
    const isBusinessDeleted = !appointment.staff?.business?.name;

    const handlePress = () => {
        if (isBusinessDeleted) {
            return;
        }
        if (onPress) {
            onPress();
        } else {
            navigate('AppointmentDetailsScreen', { appointment, businessView, client });
        }
    };

    return (
        <TouchableOpacity onPress={handlePress} activeOpacity={0.7}>
            <Grid
                p={1}
                bgColor={isBusinessDeleted ? '#ff000008' : 'secondary'}
                borderRadius={12}
                flexDirection={'row'}
                alignItems={'flex-start'}
                flexWrap={'wrap'}
                {...rest}
            >
                <Date date={appointment.dateTime} />
                <Grid flexShrink={1} flexGrow={1} flexDirection={'row'} mr={1} ml={1.5} flexWrap={'wrap'}>
                    <Typography
                        width={'100%'}
                        fontWeight={500}
                        variant={'callout'}
                        ellipsizeMode={'tail'}
                        numberOfLines={1}
                    >
                        {isBusinessDeleted ? t('businessDeleted') : appointment.staff.business.name}
                    </Typography>
                    <Grid width={'100%'} flexDirection={'row'} mt={0.5} alignItems={'flex-start'}>
                        <Typography color={'disabled'} variant={'caption1'} fontWeight={600}>
                            {appointment.service.name}
                        </Typography>
                        <Typography fontSize={4} color={'disabled'} variant={'caption1'} fontWeight={500} mx={0.5}>
                            ●
                        </Typography>
                        <Typography color={'disabled'} variant={'caption1'} fontWeight={500}>
                            {serviceStartTime}-{serviceEndTime}
                        </Typography>
                    </Grid>

                    <Typography width={'100%'} fontWeight={500} mt={1} variant={'footnote'} textTransform={'lowercase'}>
                        {appointment.service.price} {appointment.service.currency}
                    </Typography>
                </Grid>
                <Grid minWidth={40}>
                    <Badge title={t(appointmentStatus as any)} color={appointmentColors[appointmentStatus]} />
                </Grid>
            </Grid>
        </TouchableOpacity>
    );
}

function Date({ date }: { date: string }): ReactElement {
    return (
        <Grid
            p={2}
            borderRadius={12}
            bgColor={'backgroundSecondary'}
            justifyContent={'center'}
            alignItems={'center'}
            width={74}
            height={74}
        >
            <Typography fontWeight={700} fontSize={17}>
                {moment(date).format('DD')}
            </Typography>
            <Typography variant={'footnote'} fontWeight={500} color={'disabled'} textTransform={'uppercase'}>
                {moment(date).format('MMM')}
            </Typography>
        </Grid>
    );
}
