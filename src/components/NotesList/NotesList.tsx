import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Formik } from 'formik';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';
import { formatDate } from '@bookr-technologies/core';
import { BottomSheet } from '~/components/BottomSheet';
import { Button, FormikButton } from '~/components/ui/Button';
import { HStack, VStack } from '~/components/ui/Grid';
import { Paper, PaperProps } from '~/components/ui/Paper';
import { Spacer } from '~/components/ui/Spacer';
import { FormikTextField } from '~/components/ui/TextField';
import { Typography } from '~/components/ui/Typography';
import { useEvent } from '~/hooks/useEvent';

interface NotesListProps extends PaperProps {
    createNote: (note: string) => Promise<void>;
    editNote: (note: NoteModel) => Promise<void>;
    notes?: NoteModel[];
}

export function NotesList({ notes, createNote, editNote, ...rest }: NotesListProps) {
    const { t } = useTranslation();
    const [isAddNotesOpen, setIsAddNotesOpen] = useState(false);
    const [isEditNoteOpen, setIsEditNoteOpen] = useState(false);
    const [editNoteValue, setEditNoteValue] = useState<NoteModel>();

    const handlePressAddNotes = useEvent(() => {
        setIsAddNotesOpen(true);
        setIsEditNoteOpen(false);
    });

    const handleCloseAddNotes = useEvent(() => {
        setIsAddNotesOpen(false);
        setIsEditNoteOpen(false);
    });

    const handleOnNotePress = (note: NoteModel) => {
        setIsEditNoteOpen(true);
        setEditNoteValue(note);
    };

    const handleSubmitAddNotes = useCallback(
        async ({ note }: { note: string }) => {
            await createNote(note);
            setIsAddNotesOpen(false);
        },
        [createNote, setIsAddNotesOpen],
    );

    const handleSubmitEditNote = useCallback(
        async (note: NoteModel) => {
            await editNote(note);
            setIsEditNoteOpen(false);
        },
        [editNote, setIsEditNoteOpen],
    );

    return (
        <Paper bgColor={'backgroundPrimary'} p={2} mb={2} {...rest}>
            <VStack>
                <HStack justifyContent={'space-between'} alignItems={'center'}>
                    <Typography variant={'title3'} fontWeight={700}>
                        {t('notes')}
                    </Typography>
                    <Button
                        size={'xsmall'}
                        variant={'subtle'}
                        color={'accent'}
                        label={t('add')}
                        pr={1}
                        startIcon={<MaterialIcons name={'add'} />}
                        onPress={handlePressAddNotes}
                    />
                    <BottomSheet open={isAddNotesOpen} onClose={handleCloseAddNotes} snapPoints={[380]}>
                        <Formik initialValues={{ note: '' }} onSubmit={handleSubmitAddNotes}>
                            <VStack flex safeBottom px={3} focusable>
                                <Typography variant={'title3'} fontWeight={700} mb={3}>
                                    {t('addNotes')}
                                </Typography>
                                <FormikTextField name={'note'} bottomSheetField />
                                <Spacer />
                                <FormikButton size={'large'} label={t('saveNotes')} />
                            </VStack>
                        </Formik>
                    </BottomSheet>
                    <BottomSheet open={isEditNoteOpen} onClose={handleCloseAddNotes} snapPoints={[380]}>
                        {editNoteValue && (
                            <Formik initialValues={editNoteValue} onSubmit={handleSubmitEditNote}>
                                <VStack flex safeBottom px={3} focusable>
                                    <Typography variant={'title3'} fontWeight={700} mb={3}>
                                        {t('editNote')}
                                    </Typography>
                                    <FormikTextField name={'text'} bottomSheetField />
                                    <Spacer />
                                    <FormikButton size={'large'} label={t('saveNotes')} />
                                </VStack>
                            </Formik>
                        )}
                    </BottomSheet>
                </HStack>
            </VStack>
            {notes && notes.length > 0 ? (
                <VStack fullWidth mt={1}>
                    {notes.map((note) => (
                        <TouchableOpacity key={note.id} activeOpacity={0.7} onPress={() => handleOnNotePress(note)}>
                            <Paper bgColor={'backgroundTertiary'} fullWidth mt={1} py={1.5} px={2}>
                                <Typography variant={'footnote'} fontWeight={500} mb={1} fullWidth>
                                    {note.text}
                                </Typography>
                                <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'}>
                                    {formatDate(note.createdAt, 'll')}
                                </Typography>
                            </Paper>
                        </TouchableOpacity>
                    ))}
                </VStack>
            ) : (
                <Typography
                    variant={'footnote'}
                    fontWeight={500}
                    color={'textSecondary'}
                    py={2}
                    textAlign={'center'}
                    fullWidth
                >
                    {t('noNoteSaved')}
                </Typography>
            )}
        </Paper>
    );
}
