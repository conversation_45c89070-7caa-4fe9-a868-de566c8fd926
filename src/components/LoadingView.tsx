import LottieView from 'lottie-react-native';
import { ReactElement, useRef } from 'react';
import { Animated, Easing } from 'react-native';
import { VStack } from '~/components/ui/Grid';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useEvent } from '~/hooks/useEvent';

export function LoadingView(): ReactElement {
    const onFinish = useEvent(() => {
        startAnimation();
    });

    const [opacity, opacityHelpers] = useAnimatedValue(1);
    const ref = useRef(true);

    const startAnimation = useEvent(() => {
        opacityHelpers.timing(ref.current ? 0.6 : 1, { duration: 700, easing: Easing.inOut(Easing.ease) }).start(() => {
            ref.current = !ref.current;
            startAnimation();
        });
    });

    return (
        <VStack flex alignItems={'center'} justifyContent={'center'} bgColor={'backgroundPrimary'}>
            <Animated.View style={{ opacity: opacity.current }}>
                <LottieView
                    autoPlay
                    cacheComposition
                    hardwareAccelerationAndroid
                    loop={false}
                    duration={2000}
                    onAnimationFinish={onFinish}
                    source={require('~/assets/lottie/logo.json')}
                    style={{ height: 300, width: 550 }}
                />
            </Animated.View>
        </VStack>
    );
}
