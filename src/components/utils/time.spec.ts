import { getNearestDatetime, getNearestTime, roundMinutesUp } from './time';

describe('utils#time', () => {
    it('should round minutes up correctly', () => {
        expect(roundMinutesUp(0)).toBe(0);
        expect(roundMinutesUp(1)).toBe(5);
        expect(roundMinutesUp(2)).toBe(5);
        expect(roundMinutesUp(3)).toBe(5);
        expect(roundMinutesUp(4)).toBe(5);
        expect(roundMinutesUp(5)).toBe(5);
        expect(roundMinutesUp(6)).toBe(10);
        expect(roundMinutesUp(7)).toBe(10);
        expect(roundMinutesUp(8)).toBe(10);
        expect(roundMinutesUp(9)).toBe(10);
        expect(roundMinutesUp(10)).toBe(10);
        expect(roundMinutesUp(11)).toBe(15);
        expect(roundMinutesUp(12)).toBe(15);
        expect(roundMinutesUp(13)).toBe(15);
        expect(roundMinutesUp(14)).toBe(15);
        expect(roundMinutesUp(15)).toBe(15);
        expect(roundMinutesUp(16)).toBe(20);
        expect(roundMinutesUp(17)).toBe(20);
        expect(roundMinutesUp(18)).toBe(20);
        expect(roundMinutesUp(19)).toBe(20);
        expect(roundMinutesUp(20)).toBe(20);
        expect(roundMinutesUp(21)).toBe(25);
        expect(roundMinutesUp(22)).toBe(25);
        expect(roundMinutesUp(23)).toBe(25);
        expect(roundMinutesUp(24)).toBe(25);
        expect(roundMinutesUp(25)).toBe(25);
        expect(roundMinutesUp(26)).toBe(30);
        expect(roundMinutesUp(27)).toBe(30);
        expect(roundMinutesUp(28)).toBe(30);
        expect(roundMinutesUp(29)).toBe(30);
        expect(roundMinutesUp(30)).toBe(30);
        expect(roundMinutesUp(31)).toBe(35);
        expect(roundMinutesUp(32)).toBe(35);
        expect(roundMinutesUp(33)).toBe(35);
        expect(roundMinutesUp(34)).toBe(35);
        expect(roundMinutesUp(35)).toBe(35);
        expect(roundMinutesUp(36)).toBe(40);
        expect(roundMinutesUp(37)).toBe(40);
        expect(roundMinutesUp(38)).toBe(40);
        expect(roundMinutesUp(39)).toBe(40);
        expect(roundMinutesUp(40)).toBe(40);
        expect(roundMinutesUp(41)).toBe(45);
        expect(roundMinutesUp(42)).toBe(45);
        expect(roundMinutesUp(43)).toBe(45);
        expect(roundMinutesUp(44)).toBe(45);
        expect(roundMinutesUp(45)).toBe(45);
        expect(roundMinutesUp(46)).toBe(50);
        expect(roundMinutesUp(47)).toBe(50);
        expect(roundMinutesUp(48)).toBe(50);
        expect(roundMinutesUp(49)).toBe(50);
        expect(roundMinutesUp(50)).toBe(50);
        expect(roundMinutesUp(51)).toBe(55);
        expect(roundMinutesUp(52)).toBe(55);
        expect(roundMinutesUp(53)).toBe(55);
        expect(roundMinutesUp(54)).toBe(55);
        expect(roundMinutesUp(55)).toBe(55);
        expect(roundMinutesUp(56)).toBe(0);
        expect(roundMinutesUp(57)).toBe(0);
        expect(roundMinutesUp(58)).toBe(0);
        expect(roundMinutesUp(59)).toBe(0);
        expect(roundMinutesUp(60)).toBe(0);
        expect(roundMinutesUp(61)).toBe(0);
    });
    it('should generate the right time', () => {
        expect(getNearestTime('12:00', 'HH:mm')).toBe('12:00');
        expect(getNearestTime('12:01', 'HH:mm')).toBe('12:05');
        expect(getNearestTime('12:55', 'HH:mm')).toBe('12:55');
        expect(getNearestTime('12:56', 'HH:mm')).toBe('13:00');
        expect(getNearestTime('12:59', 'HH:mm')).toBe('13:00');
    });

    it('should generate the right datetime', () => {
        const format = 'YYYY-MM-DD HH:mm';
        expect(getNearestDatetime('2022-08-18 21:30', format).format(format)).toBe('2022-08-18 21:30');
        expect(getNearestDatetime('2022-08-18 21:31', format).format(format)).toBe('2022-08-18 21:35');
    });
});
