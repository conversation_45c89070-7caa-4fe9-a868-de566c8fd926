import moment, { Moment } from 'moment';
import momentTimezone from 'moment-timezone';
import { TimeslotDTO } from '@bookr-technologies/api/dto/TimeslotDTO';
import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';

export const generateHours = (minutesStep = 5) => {
    const intervals = 24 * (60 / minutesStep);
    let start = moment().startOf('day');

    return new Array(intervals).fill(0).map(() => {
        const value = start.format('HH:mm');
        const label = formatTime(value);
        start = start.add(minutesStep, 'minutes');

        return {
            key: value,
            label,
            value,
        };
    });
};

export const GeneratedHours = generateHours();

export function time(value: string) {
    return moment(value, 'HH:mm');
}

export function formatTime(value: string) {
    const segments = moment(value, 'HH:mm').format('LT').split(':');

    if (segments.length >= 2) {
        segments[0] = segments[0].padStart(2, '0');
    }

    return segments.join(':');
}

export const convertHoursToLocalTimezone = (hours: WorkingHourModel[]) =>
    hours.map((hour) => {
        const toLocalTimezone = { ...hour };
        const offset = moment(hour.lastUpdatedAt).utcOffset();
        toLocalTimezone.start = moment(hour.start, 'HH:mm').add(offset, 'minutes').format('HH:mm');
        toLocalTimezone.end = moment(hour.end, 'HH:mm').add(offset, 'minutes').format('HH:mm');
        return toLocalTimezone;
    });

export const convertHoursToBusinessTimezone = (hours: WorkingHourModel[], businessZoneId: string) =>
    hours.map((hour) => {
        const toBusinessTimezone = { ...hour };
        const offset = moment(hour.lastUpdatedAt).utcOffset();
        toBusinessTimezone.start = moment(hour.start, 'HH:mm')
            .add(offset || 0, 'minutes')
            .tz(businessZoneId)
            .format('HH:mm');
        toBusinessTimezone.end = moment(hour.end, 'HH:mm')
            .add(offset || 0, 'minutes')
            .tz(businessZoneId)
            .format('HH:mm');
        return toBusinessTimezone;
    });

export const convertTimesToBusinessTimezone = (currentDate: string, timeslots: TimeslotDTO[], businessZoneId: string) =>
    timeslots.map((timeslot) => {
        const toBusinessTimezone = {
            originalValue: { ...timeslot },
            displayValue: { ...timeslot },
        };
        toBusinessTimezone.displayValue.start = momentTimezone
            .tz(currentDate + 'T' + timeslot.start, 'UTC')
            .tz(businessZoneId)
            .format('HH:mm');

        toBusinessTimezone.displayValue.end = momentTimezone
            .tz(currentDate + 'T' + timeslot.end, 'UTC')
            .tz(businessZoneId)
            .format('HH:mm');

        return toBusinessTimezone;
    });

export function roundMinutesToNearest(minutes: number, step: number): number {
    return (Math.round(minutes / step) * step) % 60;
}

export function roundMinutesToNearestQuarterHour(minutes: number): number {
    return roundMinutesToNearest(minutes, 15);
}

export function roundMinutesUp(minutes: number): number {
    if (!minutes || Number.isNaN(minutes) || minutes >= 60) {
        return 0;
    }

    if (minutes % 5 !== 0) {
        return roundMinutesUp(minutes + 1);
    }

    return minutes;
}

export function getNearestTime(time?: string, format?: string): string {
    const now = moment(time, format);
    const minutes = now.minutes();
    const nearest = roundMinutesUp(minutes);
    let hours = now.hours();

    if (nearest - minutes < 0) {
        hours += 1;
    }

    return `${hours}:${String(nearest).padStart(2, '0')}`;
}

export function getNearestDatetime(time?: string, format?: string): Moment {
    const now = moment(time, format);
    const minutes = now.minutes();
    const nearest = roundMinutesUp(minutes);
    let hours = now.hours();

    if (nearest - minutes < 0) {
        hours += 1;
    }

    return now.set({
        hours,
        minutes: nearest,
    });
}

/**
 * Helper to get datetime as utc and convert it to local timezone
 */
export function utcToLocal(...args: Parameters<typeof moment.utc>): moment.Moment {
    return moment.utc(...args).local();
}

export function localHoursToUtcHours(hours: string): string | null {
    if (!hours) {
        return null;
    }
    return moment(hours, 'HH:mm').utc().format('HH:mm');
}

/**
 * Convert UTC time to local timezone
 */
export function timeFromUtcToLocal(time: string, relative?: string): string {
    if (relative) {
        const parsedTime = moment(time, 'HH:mm');
        return moment.utc(relative).hours(parsedTime.hours()).minutes(parsedTime.minutes()).local().format('HH:mm');
    }
    return utcToLocal(time, 'HH:mm').format('HH:mm');
}
