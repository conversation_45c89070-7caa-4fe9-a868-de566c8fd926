/* eslint-disable @typescript-eslint/no-explicit-any */
import { Children, cloneElement, isValidElement, ReactChild, ReactElement } from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function extendChildren<C extends ReactChild | ReactChild[], P = C extends ReactElement<infer P> ? P : any>(
    children: C,
    extender: P | ((props: P) => P),
): C {
    const childrenArray = Children.toArray(children) as ReactChild[];

    return Children.map(childrenArray, (child) => {
        if (!isValidElement(child)) {
            return child;
        }

        const newChildProps = {
            ...(child.props as any),
            ...(typeof extender !== 'function' ? extender : (extender as (props: P) => P)(child.props as any)),
        };

        return cloneElement(child, newChildProps as any);
    }) as C;
}
