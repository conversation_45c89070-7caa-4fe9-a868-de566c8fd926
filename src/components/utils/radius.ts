import { GooglePlaceDetail } from 'react-native-google-places-autocomplete';

export const radiansToDegrees = (angle: number): number => angle * (180 / Math.PI);

export const degreesToRadians = (angle: number): number => angle * (Math.PI / 180);

export const latitudesToKM = (latitudes: number): number => latitudes * 110.574;

export const kMToLatitudes = (km: number): number => km / 110.574;

export const longitudesToKM = (longitudes: number, atLatitude: number): number =>
    longitudes * 111.32 * Math.cos(degreesToRadians(atLatitude));

export const kMToLongitudes = (km: number, atLatitude: number): number =>
    (km * 0.0089831) / Math.cos(degreesToRadians(atLatitude));

export const getPlaceSize = (detail: GooglePlaceDetail): number => {
    const viewport = detail.geometry.viewport;
    if (!viewport || !viewport.northeast || !viewport.southwest) {
        return 0;
    }

    // Calculate the approximate size of the place using the viewport
    const latDiff = Math.abs(viewport.northeast.lat - viewport.southwest.lat);
    const lngDiff = Math.abs(viewport.northeast.lng - viewport.southwest.lng);

    // Convert lat/lng differences to meters (approximation)
    const latSize = latDiff * 111320; // 1 degree of latitude is approximately 111.32 km
    const lngSize =
        lngDiff * 111320 * Math.cos(((viewport.northeast.lat + viewport.southwest.lat) / 2) * (Math.PI / 180));

    // Calculate the radius as the average of the lat and lng sizes divided by 2
    const radius = (latSize + lngSize) / 4;

    return radius;
};
