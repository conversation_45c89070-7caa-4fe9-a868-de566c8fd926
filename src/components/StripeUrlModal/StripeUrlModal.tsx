import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { debounce } from 'lodash';
import { ReactElement, useCallback, useEffect, useMemo, useRef } from 'react';
import { Modal, ModalProps, StatusBar } from 'react-native';
import { WebView, WebViewMessageEvent, WebViewNavigation } from 'react-native-webview';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { makeStyles } from '~/components/ui/makeStyles';
import {
    PaymentAppointmentCancel,
    PaymentAppointmentSuccess,
    StripeConnectAccountSuccess,
    PaymentTheWomanSuccess,
} from '~/data/applicationData';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useSafeState } from '~/hooks/useSafeState';

export interface StripeUrlModalProps extends ModalProps {
    onSuccess?: () => void;

    uri: string;

    onClose(): void;
}

const debouncedCall = debounce((callback: () => void) => callback(), 1000);

export function StripeUrlModal({ uri, onClose, onSuccess, ...rest }: StripeUrlModalProps): ReactElement {
    const styles = useStyles();
    const [urls, setUrls] = useSafeState<string[]>([]);
    const insets = useSafeAreaInsets(true);
    const webViewRef = useRef<WebView>(null);

    const source = useMemo(() => {
        if (uri) {
            return { uri };
        }
        return null;
    }, [uri]);

    const handlePressBack = useCallback(() => {
        setUrls((state) => {
            if (state.length <= 1) {
                onClose();

                return [];
            }

            state.pop();

            webViewRef.current?.goBack();

            return [...state];
        });
    }, [onClose, setUrls]);

    const handleNavigationStateChange = useCallback(
        async (e: WebViewNavigation) => {
            const currentURL = e.url;
            console.log('currentUrl = ', currentURL);

            if ([StripeConnectAccountSuccess, PaymentAppointmentSuccess, PaymentTheWomanSuccess].includes(currentURL)) {
                onClose();
                onSuccess && onSuccess();
                setUrls([]);
                return;
            } else if ([PaymentAppointmentCancel].includes(currentURL)) {
                onClose();
                return;
            }

            debouncedCall(() => {
                setUrls((state) => {
                    if (
                        state.includes(currentURL) ||
                        !currentURL.startsWith('http') ||
                        currentURL.includes('https://app.bookr.ro')
                    ) {
                        return state;
                    }

                    return [...state, currentURL];
                });
            });
        },
        [onClose, onSuccess, setUrls],
    );

    const handleMessage = useCallback(
        (event: WebViewMessageEvent) => {
            if (event.nativeEvent.data === 'WINDOW_CLOSED') {
                onClose();
            }
        },
        [onClose],
    );

    useEffect(() => {
        if (!uri) {
            setUrls([]);
        }
    }, [setUrls, uri]);

    return (
        <Modal animationType={'slide'} {...rest}>
            <VStack bgColor={'#000'} flex pt={insets.top}>
                {uri && source ? (
                    <VStack flex>
                        <StatusBar barStyle={'light-content'} />
                        {urls.length > 1 && (
                            <HStack px={1}>
                                <IconButton color={'primary.contrast'} onPress={handlePressBack}>
                                    <MaterialCommunityIcons name={'keyboard-backspace'} />
                                </IconButton>
                            </HStack>
                        )}

                        {source ? (
                            <WebView
                                javaScriptEnabled
                                scrollEnabled
                                startInLoadingState
                                scalesPageToFit
                                ref={webViewRef}
                                originWhitelist={['*']}
                                onNavigationStateChange={handleNavigationStateChange}
                                source={source}
                                onMessage={handleMessage}
                                style={styles.webView}
                            />
                        ) : (
                            <VStack flex alignItems={'center'} justifyContent={'center'} bgColor={'backgroundPrimary'}>
                                <CircularProgress />
                            </VStack>
                        )}
                    </VStack>
                ) : null}
            </VStack>
        </Modal>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        backgroundColor: theme.palette.primary.main,
    },
    webView: {
        height: 200,
    },
}));
