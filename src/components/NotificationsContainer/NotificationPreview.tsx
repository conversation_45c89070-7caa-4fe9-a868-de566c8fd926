/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ReactElement, useEffect, useMemo, useRef } from 'react';
import { Animated, View, ViewStyle } from 'react-native';
import { useTheme } from 'styled-components/native';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useValueRef } from '~/hooks/useValueRef';
import { NotificationTypeEnum } from '~/lib/notifications/NotificationTypeEnum';
import { useApplicationStore } from '~/store/useApplicationStore';

interface NotificationPreviewProps {
    message?: string;
    notificationType?: NotificationTypeEnum;
    onClose: () => void;
    open: boolean;
    title?: string;
}

const useStyles = makeStyles(({ theme }) => ({
    alert: {
        alignItems: 'center',
        backgroundColor: '#fff',
        borderRadius: 10,
        flexDirection: 'row',
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(4),
        paddingVertical: theme.mixins.spacingValue(2),
        shadowColor: '#000',
        shadowOffset: {
            height: 0,
            width: 11,
        },
        shadowOpacity: 0.1,
        elevation: 12,
        shadowRadius: 20,
    },
    root: {
        bottom: 0,
        left: 0,
        paddingHorizontal: theme.mixins.spacingValue(3),
        paddingVertical: theme.mixins.spacingValue(2),
        position: 'absolute',
        right: 0,
    },
    textHolder: {
        marginLeft: theme.mixins.spacingValue(1),
        marginRight: theme.mixins.spacingValue(3),
    },
}));

export function NotificationPreview({
    open,
    title,
    message,
    notificationType,
    onClose,
}: NotificationPreviewProps): ReactElement {
    const styles = useStyles();
    const [offset, offsetAnimation] = useAnimatedValue(24);
    const [opacity, opacityAnimation] = useAnimatedValue(0);
    const theme = useTheme();

    const onCloseRef = useValueRef(onClose);
    const bottomScreenOffset = useApplicationStore((state) => state.bottomScreenOffset);

    const rootStyle: ViewStyle = {
        bottom: bottomScreenOffset,
        opacity: opacity.current as any,
        transform: [{ translateY: offset.current as any }],
    };

    const icon = useMemo(() => {
        if (notificationType === NotificationTypeEnum.Warning) {
            return 'warning';
        }
        if (notificationType === NotificationTypeEnum.Error) {
            return 'error';
        }
        if (notificationType === NotificationTypeEnum.Success) {
            return 'check-circle';
        }
        if (notificationType === NotificationTypeEnum.Info) {
            return 'info';
        }
    }, [notificationType]);

    const iconColor = useMemo(() => {
        if (notificationType === NotificationTypeEnum.Warning) {
            return theme.palette.warning.main;
        }
        if (notificationType === NotificationTypeEnum.Error) {
            return theme.palette.error.main;
        }
        if (notificationType === NotificationTypeEnum.Success) {
            return theme.palette.success.main;
        }
        if (notificationType === NotificationTypeEnum.Info) {
            return theme.palette.accent.main;
        }
    }, [
        notificationType,
        theme.palette.accent.main,
        theme.palette.error.main,
        theme.palette.success.main,
        theme.palette.warning.main,
    ]);

    useEffect(() => {
        offsetAnimation.spring(open ? 0 : 24).start();
        opacityAnimation.timing(open ? 1 : 0).start();
    }, [offsetAnimation, opacityAnimation, open]);

    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    useEffect(() => {
        if (open) {
            timeoutRef.current = setTimeout(() => {
                if (onCloseRef.current) {
                    onCloseRef.current();
                }
            }, 5000);
        } else {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        }
    }, [onCloseRef, open]);

    return (
        <Animated.View style={[styles.root, rootStyle]} pointerEvents={open ? 'auto' : 'none'}>
            <View style={styles.alert}>
                <MaterialIcons name={icon} size={24} color={iconColor} />
                <View style={styles.textHolder}>
                    {title ? (
                        <Typography variant={'subhead'} fontWeight={600} mb={0.5}>
                            {title}
                        </Typography>
                    ) : null}
                    {message ? (
                        <Typography variant={'caption1'} fontWeight={500} color={'textSecondary'}>
                            {message}
                        </Typography>
                    ) : null}
                </View>
            </View>
        </Animated.View>
    );
}
