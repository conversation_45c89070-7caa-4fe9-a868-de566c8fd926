import { ReactElement, useCallback, useEffect, useState } from 'react';
import { NotificationPreview } from '~/components/NotificationsContainer/NotificationPreview';
import { useNotifications } from '~/hooks/useNotifications';
import { NotificationItem } from '~/lib/notifications/NotificationItem';

export function NotificationsContainer(): ReactElement {
    const [active, setActive] = useState<NotificationItem | null>(null);
    const { notifications, close } = useNotifications();

    const handleClose = useCallback(() => {
        setActive((previous) => {
            if (previous) {
                close(previous);
                return { ...previous, wait: true };
            }
            return null;
        });
    }, [close, setActive]);

    useEffect(() => {
        setActive((previous) => {
            if (!previous && notifications && notifications.length > 0) {
                return notifications[0];
            }

            return previous;
        });
    }, [active, notifications]);

    useEffect(() => {
        if (active?.wait) {
            setTimeout(() => setActive(null), 350);
        }
    }, [active?.wait]);

    return (
        <NotificationPreview
            open={!!active && !active.wait}
            notificationType={active?.type}
            message={active?.message}
            title={active?.title}
            onClose={handleClose}
        />
    );
}
