import dialCodes, { DialCode } from '../assets/dialCodes';

export function normalize(phoneNumber: string): string {
    phoneNumber = phoneNumber.replace(/[()]/g, '').trim(); // removes "(" and ")" and spaces
    if (phoneNumber.length >= 2) {
        const number = phoneNumber.replace(/^00/, '+');
        const dialCode = findDialCode(number);
        if (dialCode) {
            const x = number.replace(dialCode.dialCode, '').replace(/^0+/, '');
            phoneNumber = dialCode.dialCode + x;
        }
    }
    return phoneNumber;
}

export function findDialCode(phoneNumber: string): DialCode | null {
    for (let i = 5; i >= 2; i--) {
        const dialCode = dialCodes.find((dc) => dc.dialCode === phoneNumber.slice(0, i));
        if (dialCode) {
            return dialCode;
        }
    }

    return null;
}
