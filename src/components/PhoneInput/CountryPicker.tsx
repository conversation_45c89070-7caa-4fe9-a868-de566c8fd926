import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React, { useMemo, useState } from 'react';
import { Modal, TextInput } from 'react-native';
import { HStack, VStack } from '~/components/ui/Grid';
import { FlatList } from '~/components/ui/List';
import { ListItemButton } from '~/components/ui/ListItemButton';
import { ListItemIcon } from '~/components/ui/ListItemIcon';
import { ListItemText } from '~/components/ui/ListItemText';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { IconButton } from '../ui/IconButton';
import { CountryFlag } from './CountryFlag';
import dialCodes, { DialCode } from './assets/dialCodes';

interface Props {
    visible: boolean;
    onRequestClose(): void;
    onSelect(dialCode: DialCode): void;
}

export function CountryPicker({ visible, onSelect, onRequestClose }: Props) {
    const styles = useStyles();
    const [search, setSearch] = useState('');

    const handleSelect = useEvent(onSelect);

    const elements = useMemo(
        () =>
            dialCodes
                .filter((item) => {
                    if (!search) {
                        return true;
                    }

                    return (
                        item.countryCode.toLocaleUpperCase().includes(search.toLocaleUpperCase()) ||
                        item.name.toLocaleUpperCase().includes(search.toLocaleUpperCase()) ||
                        item.dialCode.toLocaleUpperCase().includes(search.toLocaleUpperCase())
                    );
                })
                .map((item, index) => (
                    <ListItemButton
                        key={item.countryCode}
                        divider={index < dialCodes.length - 1}
                        onPress={() => handleSelect(item)}
                        px={4}
                        dividerProps={{ grow: true, mx: 3 }}
                    >
                        <ListItemIcon>
                            <CountryFlag dialCode={item} />
                        </ListItemIcon>
                        <ListItemText
                            flex
                            primary={item.name}
                            primaryTypographyProps={{ fontWeight: 500 }}
                            secondary={item.dialCode}
                            secondaryTypographyProps={{ fontWeight: 500 }}
                            flexDirection={'row'}
                            alignItems={'center'}
                            justifyContent={'space-between'}
                        />
                    </ListItemButton>
                )),
        [handleSelect, search],
    );

    return (
        <Modal
            visible={visible}
            animationType={'slide'}
            presentationStyle={'pageSheet'}
            onRequestClose={onRequestClose}
            onDismiss={onRequestClose}
        >
            <VStack bgColor={'backgroundPrimary'} flex>
                <HStack pt={3} pb={2} px={3}>
                    {/* TODO: implement TextField */}
                    <TextInput
                        placeholder={'Search'}
                        style={styles.searchField}
                        value={search}
                        onChangeText={setSearch}
                    />
                    <IconButton onPress={onRequestClose} disablePadding ml={2}>
                        <MaterialIcons name={'close'} />
                    </IconButton>
                </HStack>
                <VStack flex pb={4}>
                    <FlatList keyboardShouldPersistTaps="always">{elements}</FlatList>
                </VStack>
            </VStack>
        </Modal>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    searchField: {
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: 8,
        borderWidth: 1,
        flexGrow: 1,
        height: theme.mixins.spacingValue(5),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(2),
        ...theme.mixins.typographyObject('subhead', { fontWeight: 500 }),
    },
}));
