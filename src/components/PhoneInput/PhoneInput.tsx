/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { PhoneNumberFormat as PNF, PhoneNumberUtil } from 'google-libphonenumber';
import React, { useCallback, useEffect, useState } from 'react';
import { Keyboard, TextInput, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { Nullable, Optional } from '@bookr-technologies/core/types';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';
import { CountryFlag } from './CountryFlag';
import { CountryPicker } from './CountryPicker';
import dialCodes, { DialCode } from './assets/dialCodes';
import { findDialCode, normalize } from './utils';

const phoneUtil = PhoneNumberUtil.getInstance();

function isValidNumber(number: string, country: string): boolean {
    const obj = phoneUtil.parse(number, country);
    return phoneUtil.isValidNumber(obj);
}

export interface PhoneInputChangeEvent {
    countryCode: Optional<string>;
    dialCode: Optional<string>;
    e164: Optional<string>;
    input: string;
    isValid: boolean;
}

export interface PhoneInputProps {
    autoFocus?: boolean;
    dismissKeyboard?: boolean;
    initialCountry?: string;
    onChange?: (event: PhoneInputChangeEvent) => void;
    onChangePhoneNumber?: (phoneNumber: string) => void;
    style?: ViewStyle;
    textStyle?: TextStyle;
    value?: string;
}

export function PhoneInput({
    initialCountry = 'US',
    value,
    onChange,
    onChangePhoneNumber,
    style,
    textStyle,
    dismissKeyboard = true,
}: PhoneInputProps) {
    const styles = useStyles();

    const [dialCode, setDialCode] = useState<DialCode | null>(null);
    const [phoneNumber, setPhoneNumber] = useState('');
    const [countryPickerVisible, setCountryPickerVisible] = useState(false);

    const initialDialCode = useCallback(
        (): Nullable<DialCode> =>
            dialCodes.find((x) => initialCountry && x.countryCode === initialCountry.toUpperCase()) ?? null,
        [initialCountry],
    );

    const handleChangeText = useEvent((input: string): void => {
        input = normalize(input);
        let dc = findDialCode(input);
        if (!dc && !input.startsWith('+') && !input.startsWith('00')) {
            dc = initialDialCode();
            if (dc && input.length >= 2) {
                input = dc.dialCode + input.replace(/^0+/, '');
            }
        }
        setDialCode(dc); // update flag icon
        setPhoneNumber(input);
        const number = dc ? dc.dialCode + input.split(dc.dialCode).join('') : input;
        if (onChangePhoneNumber) {
            onChangePhoneNumber(number);
        }
        emitChange(number, dc);
    });

    const emitChange = useEvent((number: string, dialCode: Nullable<DialCode>): void => {
        if (onChange) {
            const event: PhoneInputChangeEvent = {
                countryCode: null,
                dialCode: null,
                e164: null,
                input: number,
                isValid: false,
            };

            if (dialCode) {
                event.dialCode = dialCode.dialCode;
                event.countryCode = dialCode.countryCode;
                let obj = undefined;
                try {
                    obj = phoneUtil.parse(number, dialCode.countryCode);
                } catch {}
                if (obj) {
                    event.isValid = obj ? isValidNumber(number, dialCode.countryCode) : false;
                    event.e164 = event.isValid ? phoneUtil.format(obj, PNF.E164) : null;
                }
            }
            if (event.isValid && dismissKeyboard) {
                Keyboard.dismiss();
            }
            onChange(event);
        }
    });

    const handleOpenPicker = useEvent((): void => {
        Keyboard.dismiss();
        setCountryPickerVisible(true);
    });

    const handleSelect = useEvent((newDialCode: DialCode): void => {
        let number = phoneNumber;
        if (dialCode) {
            number = number.split(dialCode.dialCode).join('');
        }
        setDialCode(newDialCode);
        handleChangeText(newDialCode.dialCode + number);
        setCountryPickerVisible(false);
    });

    const handleOnClose = useEvent(() => setCountryPickerVisible(false));

    useEffect(() => {
        const dialCode = initialDialCode();
        if (dialCode) {
            setDialCode(dialCode);
            setPhoneNumber(dialCode.dialCode);
        }
    }, [initialDialCode]);

    useEffect(() => {
        if (value && value.length) {
            handleChangeText(value);
        }
    }, [handleChangeText, value]);

    return (
        <>
            <View style={sx(styles.root, style)}>
                <TouchableOpacity onPress={handleOpenPicker} style={styles.flag}>
                    <CountryFlag dialCode={dialCode!} />
                </TouchableOpacity>
                <TextInput
                    dataDetectorTypes={['phoneNumber']}
                    keyboardType={'phone-pad'}
                    onChangeText={handleChangeText}
                    value={phoneNumber}
                    style={{
                        borderWidth: 0,
                        flexGrow: 1,
                        height: 40,
                        paddingLeft: 0,
                        ...textStyle,
                    }}
                />
            </View>

            <CountryPicker visible={countryPickerVisible} onSelect={handleSelect} onRequestClose={handleOnClose} />
        </>
    );
}

const useStyles = makeStyles(() => ({
    flag: {},
    root: {
        borderBottomWidth: 1,
        borderColor: '#eee',
        flexDirection: 'row',
    },
}));
