import React from 'react';
import { Image, View } from 'react-native';
import { Optional } from '@bookr-technologies/core/types';
import { makeStyles } from '~/components/ui/makeStyles';
import { DialCode } from './assets/dialCodes';

interface Props {
    dialCode: Optional<DialCode>;
}

export function CountryFlag({ dialCode }: Props) {
    const styles = useStyles();

    return (
        <View style={styles.root}>
            {dialCode && dialCode.icon && <Image style={styles.icon} resizeMode={'contain'} source={dialCode.icon} />}
        </View>
    );
}

const useStyles = makeStyles({
    icon: {
        height: 24,
        width: 24,
    },
    root: {
        alignItems: 'center',
        height: 24,
        justifyContent: 'center',
        marginRight: 12,
        width: 24,
    },
});
