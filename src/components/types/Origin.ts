import { Optional } from '@bookr-technologies/core/types';

export type OriginVertical = 'top' | 'center' | 'bottom';
export type OriginHorizontal = 'left' | 'center' | 'right';

export class Origin {
    public constructor(public vertical: OriginVertical = 'top', public horizontal: OriginHorizontal = 'left') {}

    public static from(vertical: OriginVertical = 'top', horizontal: OriginHorizontal = 'left'): Origin {
        return new Origin(vertical, horizontal);
    }

    public static normalize(origin: Optional<Partial<Origin>>): Origin {
        return Origin.from(origin?.vertical, origin?.horizontal);
    }

    public isBottom(): boolean {
        return this.vertical === 'bottom';
    }

    public isHorizontalCenter(): boolean {
        return this.horizontal === 'center';
    }

    public isLeft(): boolean {
        return this.horizontal === 'left';
    }

    public isRight(): boolean {
        return this.horizontal === 'right';
    }

    public isTop(): boolean {
        return this.vertical === 'top';
    }

    public isVerticalCenter(): boolean {
        return this.vertical === 'center';
    }
}
