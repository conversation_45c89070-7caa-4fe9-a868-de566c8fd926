import { Fontisto } from '@expo/vector-icons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { BlurView } from 'expo-blur';
import { transparentize } from 'polished';
import React, { useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ImageStyle, TouchableOpacity } from 'react-native';
import { Image } from 'react-native-expo-image-cache';
import { useMutation } from 'react-query';
import { useTheme } from 'styled-components';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { LottieFavouriteIcon } from '~/app/common/components/LottieFavouriteIcon/LottieFavouriteIcon';
import { Box } from '~/components/ui/Box';
import { Divider } from '~/components/ui/Divider';
import { Grid, GridProps, HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { getBusinessPhotos } from '~/components/utils/business';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { navigate } from '~/lib/utils/navigation';
import { useFavouritesStore } from '~/store/useFavouritesStore';
import { Icon } from '../ui/Icon';

export interface SearchBusinessCardProps extends GridProps {
    business: SearchBusinessModel;
    minimalist?: boolean;
    onPress?: () => void;
}

const useStyles = makeStyles(({ theme }) => ({
    favourite: {
        backgroundColor: transparentize(0.2, theme.palette.backgroundPrimary.main),
        borderRadius: 8,
        height: 36,
        position: 'absolute',
        right: 24,
        top: 24,
        width: 36,
    },
    image: {
        borderRadius: 12,
        height: '100%',
        width: '100%',
    },
    minimalistImage: {
        borderRadius: 12,
        height: 54,
        width: 54,
    },
    virtualTour: {
        backgroundColor: transparentize(0.2, theme.palette.backgroundPrimary.main),
        position: 'absolute',
        left: 24,
        top: 24,
        flexDirection: 'row',
        alignItems: 'center',
        padding: 8,
        borderRadius: 8,
    },
}));

export function SearchBusinessCard({ business, minimalist, ...rest }: SearchBusinessCardProps) {
    const logger = useLogger('SearchBusinessCard');
    const styles = useStyles();
    const theme = useTheme();
    const photos = getBusinessPhotos(business);
    const { t } = useTranslation();
    const notifications = useNotifications();
    const favourites = useFavouritesStore((state) => state.favourites);
    const resolveFavourites = useFavouritesStore((state) => state.resolveFavourites);
    const isFavourite = favourites.find((f) => f.id === business.id);

    const favouriteMutation = useMutation(
        'favouriteBusiness',
        () => {
            if (isFavourite) {
                return usersEndpoint.removeFavourite(business.id);
            }
            return usersEndpoint.addFavourite(business.id);
        },
        {
            onError: (error) => {
                logger.error('error toggle favourite business', {
                    error,
                    businessId: business.id,
                });
                notifications.error(t('favouriteError'));
            },
            onSuccess: () => {
                resolveFavourites();
                if (!isFavourite) {
                    notifications.success(t('favouriteAdded'));
                } else {
                    notifications.info(t('favouriteRemoved'));
                }
            },
        },
    );

    if (minimalist) {
        return <MinimalistCard business={business} {...rest} />;
    }

    const handleOnFavouritePress = () => {
        favouriteMutation.mutate();
    };

    const servicesToShow = useMemo(() => {
        if (!business.businessServices) {
            return [];
        }

        return business.businessServices.sort((a, b) => a.serviceRank - b.serviceRank).slice(0, 2);
    }, [business.businessServices]);

    const InstantBooking = () => {
        if (
            (!business.services || business.services.length === 0) &&
            (!business.businessServices || business.businessServices.length === 0)
        ) {
            return null;
        }
        return (
            <HStack position={'absolute'} borderRadius={8} top={230 - 48} left={12} overflow={'hidden'}>
                <BlurView
                    intensity={50}
                    tint="dark"
                    style={{
                        borderRadius: 8,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 8,
                    }}
                >
                    <MaterialIcons name={'event-available'} size={16} color={theme.palette.backgroundPrimary.main} />
                    <Typography
                        pl={0.5}
                        variant={'caption1'}
                        textTransform={'capitalize'}
                        fontWeight={500}
                        color={'secondary'}
                    >
                        {t('instantBooking')}
                    </Typography>
                </BlurView>
            </HStack>
        );
    };

    return (
        <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
                rest.onPress?.();
                navigate('BusinessProfileScreen', { businessId: business.id });
            }}
        >
            <Grid {...rest}>
                <Box
                    height={230}
                    borderColor={'borderOpaque'}
                    borderWidth={0.2}
                    borderRadius={photos.length > 0 ? 12 : 20}
                >
                    {photos.length > 0 ? (
                        <Image tint={'light'} uri={photos[0]} style={styles.image as ImageStyle} />
                    ) : (
                        <Grid
                            flex
                            justifyContent={'center'}
                            alignItems={'center'}
                            bgColor={'#eaeaea'}
                            borderRadius={20}
                        >
                            <Typography variant={'caption1'}>{t('noPreviewAvailable')}</Typography>
                        </Grid>
                    )}
                </Box>
                <InstantBooking />
                {business.virtualTourURL ? (
                    <Grid style={styles.virtualTour}>
                        <Icon variant={'secondary'} name={'cube-scan'} color={'accent'} size={16} />
                        <Typography variant={'caption1'} fontWeight={500} ml={1}>
                            {t('virtualTour')}
                        </Typography>
                    </Grid>
                ) : null}
                <Grid style={styles.favourite}>
                    <LottieFavouriteIcon isFavourite={!!isFavourite} onPress={handleOnFavouritePress} />
                </Grid>
                <HStack alignItems={'center'} justifyContent={'space-between'}>
                    <Typography variant={'body'} fontSize={17} lineHeight={25.5} fontWeight={700} mt={1}>
                        {business.name}
                    </Typography>
                    {business.averageRating >= 1 && (
                        <HStack alignItems={'center'}>
                            <IconButton disablePadding mr={0.5} disabled>
                                <Fontisto name={'star'} size={14} color={`primary`} />
                            </IconButton>
                            <Typography variant={'subhead'} fontWeight={700} ml={0.2}>
                                {business.averageRating.toFixed(2).slice(0, 3)}
                            </Typography>
                        </HStack>
                    )}
                </HStack>

                <Typography
                    variant={'footnote'}
                    fontWeight={400}
                    fontSize={12}
                    lineHeight={15}
                    color={'contentTertiary'}
                    numberOfLines={1}
                >
                    {business.formattedAddress}
                </Typography>

                {servicesToShow.length > 0 && (
                    <>
                        <Divider />
                        {servicesToShow.map((service) => (
                            <VStack mt={0.5} mb={1.5} key={service.id}>
                                <HStack alignItems={'center'} justifyContent={'space-between'} mb={0.25}>
                                    <Typography variant={'subhead'} fontWeight={500}>
                                        {service.name}
                                    </Typography>
                                    <Typography variant={'subhead'} fontWeight={500} textTransform={'capitalize'}>
                                        {service.price} {service.currency}
                                    </Typography>
                                </HStack>
                                <Trans
                                    values={{ duration: service.duration, staffMember: service.ownerName }}
                                    i18nKey={'searchServiceDurationAndMember'}
                                    components={{
                                        Text: (
                                            <Typography
                                                variant={'footnote'}
                                                fontWeight={500}
                                                color={'contentTertiary'}
                                                fontSize={13}
                                                lineHeight={19.5}
                                            />
                                        ),
                                        Bold: (
                                            <Typography
                                                variant={'footnote'}
                                                fontWeight={500}
                                                textDecorationLine={'underline'}
                                                color={'primary'}
                                                fontSize={13}
                                                lineHeight={19.5}
                                            />
                                        ),
                                    }}
                                />
                            </VStack>
                        ))}
                        <Typography
                            textDecorationLine={'underline'}
                            variant={'footnote'}
                            fontWeight={500}
                            color={'accent'}
                            fontSize={13}
                            lineHeight={19.5}
                        >
                            {t('viewMoreOnProfile')}
                        </Typography>
                    </>
                )}
            </Grid>
        </TouchableOpacity>
    );
}

function MinimalistCard({ business, ...rest }: SearchBusinessCardProps) {
    const theme = useTheme();
    const styles = useStyles();
    const photos = getBusinessPhotos(business);
    const { t } = useTranslation();

    return (
        <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
                navigate('BusinessProfileScreen', { businessId: business.id });
                rest.onPress?.();
            }}
        >
            <HStack {...rest}>
                {photos.length === 0 ? (
                    <Grid
                        justifyContent={'center'}
                        alignItems={'center'}
                        height={54}
                        width={54}
                        borderRadius={12}
                        bgColor={theme.palette.accent.main}
                    >
                        <Typography variant={'title2'} fontWeight={700} textTransform={'uppercase'} color={'secondary'}>
                            {business.name.charAt(0)}
                        </Typography>
                    </Grid>
                ) : (
                    <Image tint={'light'} uri={photos[0]} style={styles.minimalistImage as ImageStyle} />
                )}
                <VStack flex={1} ml={2}>
                    <HStack>
                        {(business.services && business.services.length > 0) ||
                        (business.businessServices && business.businessServices.length > 0) ? (
                            <>
                                <MaterialIcons name={'event-available'} size={14} color={theme.palette.accent.main} />
                                <Typography
                                    pl={0.5}
                                    variant={'caption2'}
                                    textTransform={'capitalize'}
                                    fontWeight={500}
                                    color={'accent'}
                                >
                                    {t('instantBooking')}
                                </Typography>
                            </>
                        ) : null}
                    </HStack>
                    <Typography variant={'callout'} fontSize={16} lineHeight={24} fontWeight={500}>
                        {business.name}
                    </Typography>
                    <Typography
                        variant={'caption1'}
                        fontWeight={400}
                        fontSize={12}
                        lineHeight={15}
                        color={theme.palette.typography.disabled}
                        numberOfLines={1}
                    >
                        {business.formattedAddress}
                    </Typography>
                </VStack>
            </HStack>
        </TouchableOpacity>
    );
}
