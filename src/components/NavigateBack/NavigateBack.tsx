import { useNavigation } from '@react-navigation/native';
import { ReactElement } from 'react';
import { extendChildren } from '~/components/utils/extendChildren';

type NavigateProps = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    children: ReactElement<{ onPress?: () => any }>;
};

export function NavigateBack({ children }: NavigateProps) {
    const navigate = useNavigation();

    if (!navigate.canGoBack()) {
        return null;
    }

    return extendChildren(children, (rest) => ({
        ...rest,
        onPress: () => {
            if (rest.onPress) {
                rest.onPress();
            }
            navigate.goBack();
        },
    }));
}
