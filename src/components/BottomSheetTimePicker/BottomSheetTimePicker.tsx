import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment-timezone';
import { ReactElement, useMemo, useState } from 'react';
import { Pressable } from 'react-native';
import { useTheme } from 'styled-components/native';
import { Nullable } from '@bookr-technologies/core/types';
import { Typography } from '~/components/ui/Typography';
import { formatTime, GeneratedHours } from '~/components/utils/time';
import { useEvent } from '~/hooks/useEvent';
import { useModal } from '~/hooks/useModal';
import { useWatchValue } from '~/hooks/useWatch';
import { SegmentedPicker, Selections } from '../SegmentedPicker';
import { makeStyles } from '../ui/makeStyles';
import { getNow } from './utils';

export interface BottomSheetTimePickerProps {
    label?: string;
    now?: boolean;
    onChange?: (value: string) => void;
    value?: Nullable<string>;
}

const DateFormat = 'HH:mm';

export function BottomSheetTimePicker({ value, now, label, onChange }: BottomSheetTimePickerProps): ReactElement {
    const styles = useStyles();
    const theme = useTheme();
    const modal = useModal();
    const [current, setCurrent] = useState(() => {
        if (!value && now) {
            return getNow();
        }

        return value || '';
    });

    const currentTime = useMemo(() => moment(current, DateFormat).format(DateFormat), [current]);
    const formattedTime = useMemo(() => formatTime(currentTime), [currentTime]);

    const handleConfirm = useEvent((selection: Selections) => {
        setCurrent(selection.time);
        if (onChange) {
            onChange(selection.time);
        }

        modal.close();
    });

    useWatchValue(value, (nextValue) => setCurrent((prevValue) => nextValue || prevValue));

    return (
        <>
            <Pressable style={styles.root} onPress={modal.open}>
                <MaterialIcons name={'schedule'} size={24} color={theme.palette.typography.textPrimary} />
                <Typography ml={1}>{current ? formattedTime : label}</Typography>
            </Pressable>

            <SegmentedPicker
                onConfirm={handleConfirm}
                defaultSelections={{ time: currentTime }}
                options={[
                    {
                        items: GeneratedHours,
                        key: 'time',
                    },
                ]}
                {...modal.props}
            />
        </>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        alignContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.palette.backgroundSecondary.main,
        borderColor: theme.palette.borderOpaque.main,
        borderRadius: theme.mixins.spacingValue(1.5),
        borderStyle: 'solid',
        borderWidth: 1,
        flexDirection: 'row',
        flexGrow: 1,
        height: theme.mixins.spacingValue(7),
        paddingLeft: theme.mixins.spacingValue(2),
        paddingRight: theme.mixins.spacingValue(2),
        width: '100%',
    },
}));

BottomSheetTimePicker.DateFormat = DateFormat;
