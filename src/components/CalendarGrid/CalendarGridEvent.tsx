import { useNavigation } from '@react-navigation/native';
import { readableColor, tint } from 'polished';
import { useMemo } from 'react';
import { Pressable } from 'react-native';
import { formatDate } from '@bookr-technologies/core';
import { CalendarGridEventType, CalendarGridEventTypeEnum } from '~/components/CalendarGrid/CalendarGridEventType';
import { calculateOffset } from '~/components/CalendarGrid/utils';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { Styles } from '~/components/ui/makeStyles/makeStyles';
import { sx } from '~/components/ui/makeStyles/sx';
import { useEvent } from '~/hooks/useEvent';
import { usePressable } from '~/hooks/usePressable';

interface CalendarGridEventProps {
    count: number;
    event: CalendarGridEventType;
    height: number;
    isFirst: boolean;
    top: number;
}

type CalendarGridEventStylesKey = 'root' | 'pressed' | 'text';

const useStyles = makeStyles<CalendarGridEventProps, Styles<CalendarGridEventStylesKey>>(
    ({ theme, event: { color }, height, top, count, isFirst }) => ({
        pressed: {
            backgroundColor: tint(0.25, color || theme.palette.accent.main),
        },
        root: {
            backgroundColor: color || theme.palette.accent.main,
            borderColor: theme.palette.backgroundSecondary.main,
            borderRadius: 8,
            borderStyle: 'solid',
            borderWidth: 2,
            flexDirection: 'column',
            flexGrow: 1,
            height,
            justifyContent: 'flex-start',
            marginLeft: !isFirst && count > 2 ? -24 : -2,
            marginTop: top,
            paddingVertical: 8,
            zIndex: calculateOffset(top, 1),
        },
        text: {
            color: color ? readableColor(color) : theme.palette.accent.contrast,
        },
    }),
);

export function CalendarGridEvent({ event, height, ...rest }: CalendarGridEventProps) {
    const [isPressed, pressableProps] = usePressable();
    const { navigate } = useNavigation();
    const styles = useStyles({
        event,
        height: Math.max(height, 36),
        ...rest,
    });

    const time = useMemo(
        () => formatDate(event.start, 'LT') + ' - ' + formatDate(event.end, 'LT'),
        [event.start, event.end],
    );

    const handleEventPress = useEvent(() => {
        if (event.type === CalendarGridEventTypeEnum.Appointment) {
            navigate('AppointmentDetailsScreen', {
                appointmentId: event.id,
                businessView: true,
            });
        } else if (event.type === CalendarGridEventTypeEnum.Break) {
            navigate('CreateBreakScreen');
        }
    });

    return (
        <Pressable
            style={sx(styles.root, isPressed && styles.pressed)}
            pointerEvents={'auto'}
            onPress={handleEventPress}
            {...pressableProps}
        >
            <Typography fontWeight={500} fontSize={10} lineHeight={10} px={1} style={styles.text}>
                {`${event.headline} - ${event.description ? event.description : time}`}
            </Typography>

            {event.description ? (
                <Typography fontWeight={500} fontSize={10} lineHeight={10} px={1} style={styles.text} mt={0.5}>
                    {time}
                </Typography>
            ) : null}
        </Pressable>
    );
}
