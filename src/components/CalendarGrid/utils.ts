import moment from 'moment';
import { DateLike } from '@bookr-technologies/core';
import { roundMinutesToNearestQuarterHour } from '~/components/utils/time';

export function calculateOffset(date: DateLike, height: number, useQuarters = false): number {
    const time = moment(date);
    let minutes = time.minutes();
    if (useQuarters) {
        minutes = roundMinutesToNearestQuarterHour(minutes);
    }

    return (time.hours() + minutes / 60) * height;
}

export function calculateRelativeOffset(date: DateLike, height: number, useQuarters = false): number {
    const time = moment(date);
    let minutes = time.minutes();
    if (useQuarters) {
        minutes = roundMinutesToNearestQuarterHour(minutes);
    }

    return (minutes / 60) * height;
}

export function calculateHeight(start: DateLike, end: DateLike, height: number) {
    return Math.abs(calculateOffset(end, height) - calculateOffset(start, height));
}
