import { useMemo, useState } from 'react';
import { View } from 'react-native';
import { formatDate } from '@bookr-technologies/core';
import { calculateOffset } from '~/components/CalendarGrid/utils';
import { Divider } from '~/components/ui/Divider';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { useInterval } from '~/hooks/useInterval';
import { useLayout } from '~/hooks/useLayout';

interface Props {
    height: number;
    maxWidth: number;
    offsetLeft: number;
}

export function CalendarGridCurrentTimeIndicator({ height, offsetLeft, maxWidth }: Props) {
    const [currentTime, setCurrentTime] = useState(new Date());
    const timeLayout = useLayout();

    const time = useMemo(() => formatDate(currentTime, 'LT'), [currentTime]);
    const top = useMemo(() => calculateOffset(currentTime, height) + 23, [currentTime, height]);

    const styles = useStyles({
        maxWidth,
        offsetLeft,
        timeWidth: timeLayout.width,
        top,
    });

    useInterval(() => {
        setCurrentTime(new Date());
    }, 1000);

    return (
        <View style={styles.root} pointerEvents={'none'}>
            <Typography
                fontWeight={500}
                variant={'caption2'}
                color={'error'}
                lineHeight={17}
                onLayout={timeLayout.handler}
                px={1}
            >
                {time}
            </Typography>
            <Divider color={'error'} mb={0} mt={0} style={styles.divider} />
        </View>
    );
}

const useStyles = makeStyles<
    {
        maxWidth: number;
        offsetLeft: number;
        timeWidth: number;
        top: number;
    },
    Styles<'root' | 'divider'>
>(({ top, offsetLeft, maxWidth, timeWidth }) => ({
    divider: {
        flexGrow: 1,
        width: maxWidth - timeWidth,
    },
    root: {
        alignContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        flexShrink: 1,
        height: 18,
        justifyContent: 'flex-start',
        left: offsetLeft,
        position: 'absolute',
        top,
        width: maxWidth,
    },
}));
