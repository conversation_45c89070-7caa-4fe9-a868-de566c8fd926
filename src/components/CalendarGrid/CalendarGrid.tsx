import moment from 'moment';
import { ReactElement, useCallback, useEffect, useRef } from 'react';
import { ScrollView } from 'react-native';
import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';
import { Divider } from '~/components/ui/Divider';
import { HStack, VStack } from '~/components/ui/Grid';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { convertHoursToLocalTimezone } from '~/components/utils/time';
import { useLayout } from '~/hooks/useLayout';
import { useRefreshControl } from '~/hooks/useRefreshControl';
import { CalendarGridCellHeight, CalendarGridHours } from './CalendarGridConstants';
import { CalendarGridCurrentTimeIndicator } from './CalendarGridCurrentTimeIndicator';
import { CalendarGridEventGroup } from './CalendarGridEventGroup';
import { CalendarGridEventType } from './CalendarGridEventType';
import { CalendarGridQuarter } from './CalendarGridQuarter';
import { useGroupedEvents } from './useGroupedEvents';
import { calculateOffset } from './utils';

interface CalendarGridProps {
    activeIntervals: WorkingHourModel[];
    events: CalendarGridEventType[];
    onCreateEvent?: (time: string) => void;
    onRefresh?: () => Promise<void>;
}

export function CalendarGrid({ events, onRefresh, onCreateEvent, activeIntervals }: CalendarGridProps): ReactElement {
    const hoursLayout = useLayout();
    const eventsLayout = useLayout();
    const rootLayout = useLayout();
    const scrollViewRef = useRef<ScrollView>(null);
    const styles = useStyles();

    const refreshControl = useRefreshControl(onRefresh);
    const groupedEvents = useGroupedEvents(events);

    useEffect(() => {
        scrollViewRef.current?.scrollTo({
            animated: false,
            x: 0,
            y: calculateOffset(new Date().toISOString(), CalendarGridCellHeight) - rootLayout.height / 2,
        });
    }, [rootLayout.height]);

    const handleCreateEventFromQuarter = useCallback(
        (hour: string, quarter: number) => () =>
            onCreateEvent?.(moment(hour, 'LT').add(quarter, 'minutes').toISOString()),
        [onCreateEvent],
    );

    const handleCreateEvent = useCallback(
        (hour: string) => () => onCreateEvent?.(moment(hour, 'LT').toISOString()),
        [onCreateEvent],
    );

    function isHourInactive(hour: string, addition: number): boolean {
        return (
            convertHoursToLocalTimezone(activeIntervals).filter((interval) => {
                const start = moment(interval.start, 'HH:mm');
                const end = moment(interval.end, 'HH:mm');
                const hourMoment = moment(hour, 'HH:mm').add(addition, 'minutes');

                return hourMoment.isSameOrAfter(start) && hourMoment.isBefore(end);
            }).length === 0
        );
    }

    return (
        <ScrollView
            style={styles.root}
            refreshControl={refreshControl}
            ref={scrollViewRef}
            onLayout={rootLayout.handler}
        >
            <HStack
                fullWidth
                justifyContent={'flex-start'}
                alignContent={'flex-start'}
                alignItems={'flex-start'}
                py={3}
            >
                <VStack onLayout={hoursLayout.handler}>
                    {CalendarGridHours.map((hour) => (
                        <VStack
                            key={hour}
                            justifyContent={'flex-start'}
                            height={CalendarGridCellHeight}
                            alignItems={'flex-end'}
                            alignContent={'flex-end'}
                        >
                            <Typography
                                variant={'caption2'}
                                color={'disabled'}
                                key={hour}
                                lineHeight={17}
                                textAlign={'right'}
                                pr={1}
                                mt={-1}
                            >
                                {hour}
                            </Typography>
                        </VStack>
                    ))}
                </VStack>
                <VStack flexGrow onLayout={eventsLayout.handler}>
                    {CalendarGridHours.map((hour) => (
                        <VStack
                            key={hour}
                            justifyContent={'flex-start'}
                            height={CalendarGridCellHeight}
                            alignItems={'flex-start'}
                            alignContent={'flex-start'}
                        >
                            <Divider my={0} style={{ position: 'absolute' }} />
                            <CalendarGridQuarter
                                inactive={isHourInactive(hour, 0)}
                                onPress={handleCreateEventFromQuarter(hour, 0)}
                                onLongPress={handleCreateEvent(hour)}
                            />
                            <CalendarGridQuarter
                                inactive={isHourInactive(hour, 15)}
                                onPress={handleCreateEventFromQuarter(hour, 15)}
                                onLongPress={handleCreateEvent(hour)}
                            />
                            <CalendarGridQuarter
                                inactive={isHourInactive(hour, 30)}
                                onPress={handleCreateEventFromQuarter(hour, 30)}
                                onLongPress={handleCreateEvent(hour)}
                            />
                            <CalendarGridQuarter
                                inactive={isHourInactive(hour, 45)}
                                onPress={handleCreateEventFromQuarter(hour, 45)}
                                onLongPress={handleCreateEvent(hour)}
                            />
                        </VStack>
                    ))}
                </VStack>
                {groupedEvents.map(({ id, start, events }) => (
                    <CalendarGridEventGroup
                        offsetTop={calculateOffset(start, CalendarGridCellHeight, true) + 24}
                        offsetLeft={hoursLayout.width}
                        maxWidth={eventsLayout.width}
                        events={events}
                        key={id}
                    />
                ))}
                <CalendarGridCurrentTimeIndicator
                    height={CalendarGridCellHeight}
                    offsetLeft={hoursLayout.width}
                    maxWidth={eventsLayout.width}
                />
            </HStack>
        </ScrollView>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        backgroundColor: theme.palette.backgroundSecondary.main,
        flex: 1,
        paddingHorizontal: theme.mixins.spacingValue(1),
        width: '100%',
    },
}));
