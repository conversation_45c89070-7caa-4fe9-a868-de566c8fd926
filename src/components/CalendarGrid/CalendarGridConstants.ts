import moment from 'moment';
import { DateLike, formatDate } from '@bookr-technologies/core';

const now = moment().startOf('day').startOf('day');

export function getTime(date: DateLike): string {
    const time = formatDate(date, 'LT');
    if (time.indexOf(':') < 2) {
        return '0' + time;
    }

    return time;
}

export const CalendarGridHours = new Array(24).fill(0).map(() => {
    const time = getTime(now);
    now.add(1, 'hour');
    return time;
});

export const CalendarGridCellHeight = 92;
