import moment, { Moment } from 'moment';
import { useMemo } from 'react';
import { getTime } from './CalendarGridConstants';
import { CalendarGridEventType } from './CalendarGridEventType';

interface Group {
    events: CalendarGridEventType[];
    id: string;
    start: Moment;
}

export function useGroupedEvents(events: CalendarGridEventType[]): Group[] {
    return useMemo(() => {
        const collection = Object.values(
            events.reduce((map, event) => {
                const date = moment(event.start).startOf('hour');
                const key = getTime(date);

                return {
                    ...map,
                    [key]: {
                        events: [...(map[key]?.events || []), event],
                        id: map[key]?.id || key,
                        start: map[key]?.start || date,
                    },
                };
            }, {} as Record<string, Group>),
        );

        return collection
            .sort((a, b) => a.start.valueOf() - b.start.valueOf())
            .map((group) => ({
                ...group,
                events: group.events.sort((a, b) => moment(a.start).valueOf() - moment(b.start).valueOf()),
            }));
    }, [events]);
}
