import { View } from 'react-native';
import { makeStyles, Styles } from '~/components/ui/makeStyles';
import { CalendarGridCellHeight } from './CalendarGridConstants';
import { CalendarGridEvent } from './CalendarGridEvent';
import { CalendarGridEventType } from './CalendarGridEventType';
import { calculateHeight, calculateRelativeOffset } from './utils';

interface Props {
    events: CalendarGridEventType[];
    maxWidth: number;
    offsetLeft: number;
    offsetTop: number;
}

export function CalendarGridEventGroup(props: Props) {
    const { events } = props;
    const styles = useStyles(props);

    return (
        <View style={styles.root} pointerEvents={'box-none'}>
            {events.map((event, index) => (
                <CalendarGridEvent
                    key={event.id}
                    event={event}
                    height={calculateHeight(event.start, event.end, CalendarGridCellHeight)}
                    top={calculateRelativeOffset(event.start, CalendarGridCellHeight, true)}
                    count={events.length}
                    isFirst={index === 0}
                />
            ))}
        </View>
    );
}

const useStyles = makeStyles<Props, Styles<'root'>>(({ offsetTop: top, offsetLeft: left, maxWidth }) => ({
    root: {
        alignContent: 'flex-start',
        alignItems: 'flex-start',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        left,
        maxWidth,
        position: 'absolute',
        top,
        width: '100%',
    },
}));
