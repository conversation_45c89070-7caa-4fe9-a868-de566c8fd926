import { transparentize } from 'polished';
import { ReactElement } from 'react';
import { Pressable, PressableProps } from 'react-native';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { usePressable } from '~/hooks/usePressable';

interface Props extends PressableProps {
    inactive?: boolean;
}

export function CalendarGridQuarter(props: Props): ReactElement {
    const { inactive, ...rest } = props;
    const styles = useStyles();

    const [isPressed, pressableProps] = usePressable();

    return (
        <Pressable
            style={sx(
                styles.root,

                inactive && styles.inactive,

                isPressed && styles.pressed,
            )}
            {...rest}
            {...pressableProps}
        />
    );
}

const useStyles = makeStyles(({ theme }) => ({
    pressed: {
        backgroundColor: transparentize(0.97, theme.palette.contentPrimary.main),
    },
    inactive: {
        backgroundColor: transparentize(0.9, theme.palette.contentPrimary.main),
        borderRadius: 0,
    },
    root: {
        borderRadius: 4,
        flex: 1,
        width: '100%',
    },
}));
