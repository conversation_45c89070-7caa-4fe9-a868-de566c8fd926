import { Button, ButtonProps } from '~/components/ui/Button';

interface SelectableButtonProps extends ButtonProps {
    isSelected: boolean;
}

export const SelectableButton = ({ isSelected, label, onPress, ...rest }: SelectableButtonProps) => {
    return (
        <Button
            color={isSelected ? 'primary' : 'backgroundTertiary'}
            label={label}
            width={'30%'}
            mr={1}
            mb={1}
            onPress={onPress}
            {...rest}
        />
    );
};
