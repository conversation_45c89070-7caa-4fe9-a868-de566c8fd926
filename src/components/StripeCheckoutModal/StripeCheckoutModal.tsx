import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { debounce } from 'lodash';
import { ReactElement, useCallback, useEffect, useMemo, useRef } from 'react';
import { Modal, ModalProps, StatusBar } from 'react-native';
import { WebView, WebViewMessageEvent, WebViewNavigation } from 'react-native-webview';
import { env } from '@bookr-technologies/env';
import { CircularProgress } from '~/components/ui/CircularProgress';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { makeStyles } from '~/components/ui/makeStyles';
import {
    PaymentCommissionCancel,
    PaymentCommissionSuccess,
    PaymentsSMSCancel,
    PaymentsSMSSuccess,
    PaymentSubscriptionCancel,
    PaymentSubscriptionSuccess,
    StripePublicKey,
} from '~/data/applicationData';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useSafeState } from '~/hooks/useSafeState';

export interface StripeCheckoutModalProps extends ModalProps {
    onCancel?: () => void;
    onSuccess?: () => void;
    sessionId: string | null;
    uri?: string;

    onClose(): void;
}

const debouncedCall = debounce((callback: () => void) => callback(), 1000);

function generateStripePage(sessionId: string): string {
    return `<!doctype html>
<html lang="en">
<body>
<script src="https://js.stripe.com/v3"></script>
<script>
    var stripe = Stripe('${StripePublicKey[env('app.env')]}');
    window.onload = function () {
        stripe.redirectToCheckout({ sessionId: '${sessionId}' })
            .then(function () {
                window.postMessage("WINDOW_CLOSED", "*");
            })
            .catch(function () {
                window.postMessage("ERROR", "*");
            });
    };
</script>
</body>
</html>
    `;
}

export function StripeCheckoutModal({
    uri,
    onClose,
    sessionId,
    onSuccess,
    onCancel,
    ...rest
}: StripeCheckoutModalProps): ReactElement {
    const styles = useStyles();
    const [urls, setUrls] = useSafeState<string[]>([]);
    const insets = useSafeAreaInsets(true);
    const webViewRef = useRef<WebView>(null);

    const source = useMemo(() => {
        if (uri) {
            return { uri };
        }

        if (!sessionId) {
            return null;
        }

        return {
            baseUrl: 'https://app.bookr.ro',
            html: generateStripePage(sessionId),
        };
    }, [sessionId, uri]);

    const handlePressBack = useCallback(() => {
        setUrls((state) => {
            if (state.length <= 1) {
                onClose();

                return [];
            }

            state.pop();

            webViewRef.current?.goBack();

            return [...state];
        });
    }, [onClose, setUrls]);

    const handleNavigationStateChange = useCallback(
        async (e: WebViewNavigation) => {
            const currentURL = e.url;

            if (
                [
                    PaymentSubscriptionSuccess,
                    PaymentSubscriptionCancel,
                    PaymentCommissionSuccess,
                    PaymentCommissionCancel,
                    PaymentsSMSSuccess,
                    PaymentsSMSCancel,
                ].includes(currentURL)
            ) {
                onClose();
                setUrls([]);

                if (
                    [PaymentSubscriptionSuccess, PaymentCommissionSuccess, PaymentsSMSSuccess].includes(currentURL) &&
                    onSuccess
                ) {
                    await onSuccess();
                } else if (
                    [PaymentSubscriptionCancel, PaymentCommissionCancel, PaymentsSMSCancel].includes(currentURL) &&
                    onCancel
                ) {
                    await onCancel();
                }

                return;
            }

            debouncedCall(() => {
                setUrls((state) => {
                    if (
                        state.includes(currentURL) ||
                        !currentURL.startsWith('http') ||
                        currentURL.includes('https://app.bookr.ro')
                    ) {
                        return state;
                    }

                    return [...state, currentURL];
                });
            });
        },
        [onCancel, onClose, onSuccess, setUrls],
    );

    const handleMessage = useCallback(
        (event: WebViewMessageEvent) => {
            if (event.nativeEvent.data === 'WINDOW_CLOSED') {
                onClose();
            }
        },
        [onClose],
    );

    useEffect(
        () => {
            if (!rest.visible) {
                setUrls([]);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [rest.visible],
    );

    return (
        <Modal animationType={'slide'} {...rest}>
            <VStack bgColor={'#000'} flex pt={insets.top}>
                {rest.visible && source ? (
                    <VStack flex>
                        <StatusBar barStyle={'light-content'} />
                        {urls.length > 1 && (
                            <HStack px={1}>
                                <IconButton color={'primary.contrast'} onPress={handlePressBack}>
                                    <MaterialCommunityIcons name={'keyboard-backspace'} />
                                </IconButton>
                            </HStack>
                        )}

                        {sessionId ? (
                            <WebView
                                javaScriptEnabled
                                scrollEnabled
                                startInLoadingState
                                scalesPageToFit
                                ref={webViewRef}
                                originWhitelist={['*']}
                                onNavigationStateChange={handleNavigationStateChange}
                                source={source}
                                onMessage={handleMessage}
                                style={styles.webView}
                            />
                        ) : (
                            <VStack flex alignItems={'center'} justifyContent={'center'} bgColor={'backgroundPrimary'}>
                                <CircularProgress />
                            </VStack>
                        )}
                    </VStack>
                ) : null}
            </VStack>
        </Modal>
    );
}

const useStyles = makeStyles(({ theme }) => ({
    root: {
        backgroundColor: theme.palette.primary.main,
    },
    webView: {
        height: 200,
    },
}));
