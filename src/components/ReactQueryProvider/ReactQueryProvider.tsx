import { PropsWithChildren } from 'react';
import { QueryClientProvider } from 'react-query';
import { reactQueryClient } from '../../lib/clients/reactQuery';
import './init';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface ReactQueryProviderProps {}

export function ReactQueryProvider({ children }: PropsWithChildren<ReactQueryProviderProps>) {
    return <QueryClientProvider client={reactQueryClient}>{children}</QueryClientProvider>;
}
