import { Component, ErrorInfo, ReactElement, ReactNode } from 'react';
import { createLogger } from '~/lib/logs/createLogger';

export class ErrorBoundary extends Component<
    {
        children: ReactNode;
    },
    {
        error: Error;
        errorInfo: ErrorInfo;
    }
> {
    private log = createLogger('ErrorBoundary');

    public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        this.log.error('did catch an error', {
            error,
            errorInfo,
        });

        this.setState({
            error,
            errorInfo,
        });
    }

    public render(): ReactElement {
        const { children } = this.props;

        return <>{children}</>;
    }
}
