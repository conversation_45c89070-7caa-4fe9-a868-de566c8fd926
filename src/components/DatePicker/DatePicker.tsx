import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment';
import { ReactElement, useCallback, useMemo, useState } from 'react';
import { Animated, Easing, View, ViewProps } from 'react-native';
import { useTheme } from 'styled-components/native';
import { formatDate } from '@bookr-technologies/core';
import { Calendar, CalendarProps, DateData } from '~/components/Calendar';
import { FormFieldBox } from '~/components/ui/FormFieldBox';
import { VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { WithTheme } from '~/components/ui/ThemeProvider/Theme';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, sx } from '~/components/ui/makeStyles';
import { useAnimStyle } from '~/hooks/useAnimStyle';
import { useAnimatedValue } from '~/hooks/useAnimatedValue';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useLayout } from '~/hooks/useLayout';
import { useWatchValue } from '~/hooks/useWatch';

export interface DatePickerProps extends ViewProps {
    availableDays?: string[];
    closeOnSelect?: boolean;
    onChange?: (value: string) => void;
    value?: string;
    withinScreen?: boolean;
}

export function DatePicker({
    value,
    onChange,
    availableDays,
    withinScreen,
    closeOnSelect,
    ...rest
}: DatePickerProps): ReactElement {
    const screenPadding = useContainerPadding(!withinScreen);
    const styles = useStyles({ screenPadding });
    const theme = useTheme();
    const layout = useLayout();
    const [open, setOpen] = useState(false);
    const [current, setCurrent] = useState(() => value || new Date().toISOString());

    const [opacity, opacityHelper] = useAnimatedValue(0);
    const [transform, transformHelper] = useAnimatedValue(theme.mixins.spacingValue(5));

    const animCalendarStyle = useAnimStyle({
        opacity,
        transform: [
            {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                translateY: transform.current as any,
            },
        ],
    });

    const formattedText = useMemo(() => formatDate(current, 'dddd, D MMMM'), [current]);
    const markedDates = useMemo<CalendarProps['markedDates']>(
        () =>
            (availableDays || []).reduce(
                (days, day) => ({
                    ...days,
                    [day]: {
                        ...(days[day] || {}),
                        dotColor: theme.palette.accent.main,
                        marked: true,
                    },
                }),
                {
                    [formatDate(current, 'YYYY-MM-DD')]: {
                        selected: true,
                        selectedColor: theme.palette.typography.textPrimary,
                        selectedTextColor: theme.palette.typography.textPrimary,
                        customContainerStyle: {
                            backgroundColor: theme.palette.typography.primary,
                            height: 34,
                            width: 34,
                            borderRadius: 17,
                        },
                        customTextStyle: {
                            color: theme.palette.typography.secondary,
                        },
                    },
                } as Exclude<CalendarProps['markedDates'], undefined>,
            ),
        [
            availableDays,
            current,
            theme.palette.accent.main,
            theme.palette.typography.primary,
            theme.palette.typography.secondary,
            theme.palette.typography.textPrimary,
        ],
    );

    const handleClose = useCallback(() => {
        Animated.parallel([opacityHelper.timing(0), transformHelper.timing(theme.mixins.spacingValue(5))]).start(() =>
            setOpen(false),
        );
    }, [opacityHelper, theme.mixins, transformHelper]);

    const handleOpen = useCallback(() => {
        if (open) {
            handleClose();
            return;
        }
        const easeConfig = {
            easing: Easing.inOut(Easing.ease),
        };

        setOpen(true);

        Animated.parallel([
            opacityHelper.timing(1, { delay: 100 }),
            transformHelper.timing(theme.mixins.spacingValue(7), easeConfig),
        ]).start();
    }, [handleClose, opacityHelper, open, theme.mixins, transformHelper]);

    const handleDayPress = useCallback(
        ({ dateString }: DateData) => {
            const date = moment(dateString, 'YYYY-MM-DD').toISOString(true);
            setCurrent(date);
            if (onChange) {
                onChange(date);
            }

            if (closeOnSelect) {
                handleClose();
            }
        },
        [onChange, closeOnSelect, handleClose],
    );

    useWatchValue(value, (val) => setCurrent((prev) => val || prev));

    return (
        <View style={styles.root}>
            <VStack bgColor="backgroundPrimary" mx={-3} px={3} zIndex={8}>
                <FormFieldBox onPress={handleOpen} pl={2} pr={1} {...rest}>
                    <MaterialIcons name={'event-note'} size={24} color={theme.palette.contentSecondary.main} />
                    <View style={styles.textHolder}>
                        <Typography ml={2} textTransform={'capitalize'}>
                            {formattedText}
                        </Typography>
                    </View>
                    {open ? (
                        <IconButton onPress={handleClose} size="small">
                            <MaterialIcons name={'close'} size={24} color={theme.palette.contentSecondary.main} />
                        </IconButton>
                    ) : null}
                </FormFieldBox>
            </VStack>

            {open ? (
                <VStack bgColor={'backgroundPrimary'} style={sx(styles.calendar, animCalendarStyle)} animated>
                    <VStack onLayout={layout.handler}>
                        {layout.initialized && (
                            <Calendar
                                current={formatDate(current, 'YYYY-MM-DD')}
                                markingType={'period'}
                                markedDates={markedDates}
                                onDayPress={handleDayPress}
                                calendarWidth={layout.width}
                            />
                        )}
                    </VStack>
                </VStack>
            ) : null}
        </View>
    );
}

const useStyles = makeStyles(({ screenPadding, theme }: WithTheme<{ screenPadding: number }>) => ({
    calendar: {
        borderRadius: screenPadding > 0 ? 0 : 12,
        left: theme.mixins.spacingValue(-screenPadding),
        paddingBottom: theme.mixins.spacingValue(1),
        paddingHorizontal: theme.mixins.spacingValue(screenPadding),
        position: 'absolute',
        right: theme.mixins.spacingValue(-screenPadding),
        zIndex: 3,
        backgroundColor: '#fff',
        ...theme.shadows.medium,
    },
    root: {
        zIndex: 10,
    },
    textHolder: {
        flexGrow: 1,
    },
}));
