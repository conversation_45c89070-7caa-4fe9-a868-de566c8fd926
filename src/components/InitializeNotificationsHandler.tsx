import * as Notifications from 'expo-notifications';
import { useEffect } from 'react';
import { useLogger } from '~/hooks/useLogger';
import { PushNotificationEvents, usePushNotificationsHandler } from '~/hooks/usePushNotificationsHandler';

export function InitializeNotificationsHandler() {
    const logger = useLogger('InitializeNotificationsHandler');
    const handlePushNotification = usePushNotificationsHandler();
    useEffect(() => {
        // 1. set the handler
        Notifications.setNotificationHandler({
            handleNotification: async () => ({
                shouldPlaySound: true,
                shouldSetBadge: false,
                shouldShowAlert: true,
            }),
        });

        // 2. the user received a notification in the foreground
        const received = Notifications.addNotificationReceivedListener((notification) => {
            const { data } = notification.request.content;
            logger.log('*** received notification ***', data);
        });

        // 3. the user received a notification and the user clicked on it
        const clicked = Notifications.addNotificationResponseReceivedListener((response) => {
            const { data } = response.notification.request.content;
            logger.log('*** clicked on notification ***', data);
            handlePushNotification(data as PushNotificationEvents);
        });

        return () => {
            Notifications.removeNotificationSubscription(received);
            Notifications.removeNotificationSubscription(clicked);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return null;
}
