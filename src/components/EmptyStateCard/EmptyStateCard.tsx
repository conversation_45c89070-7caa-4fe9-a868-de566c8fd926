import { ReactElement } from 'react';
import { Image, ImageProps, ImageSourcePropType } from 'react-native';
import { Button } from '~/components/ui/Button';
import { VStack } from '~/components/ui/Grid';
import { Paper } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { makeStyles, sx } from '~/components/ui/makeStyles';

export interface EmptyStateCardProps {
    actionLabel?: string;
    imageProps?: Omit<ImageProps, 'source'>;
    onPress?: () => void;
    source?: ImageSourcePropType;
    subtitle?: string;
    title: string;
}

export function EmptyStateCard({
    title,
    subtitle,
    actionLabel,
    onPress,
    source,
    imageProps,
}: EmptyStateCardProps): ReactElement {
    const styles = useStyles();

    const { style: imageStyle, ...restImageProps } = imageProps || {};

    return (
        <Paper bgColor={'backgroundPrimary'} overflow={'hidden'} flexWrap={'nowrap'}>
            <VStack p={3} alignItems={'flex-start'} justifyContent={'flex-start'} mr={5}>
                <Typography variant={'title2'} fontWeight={700}>
                    {title}
                </Typography>
                {subtitle && (
                    <Typography
                        variant={'footnote'}
                        color={'contentTertiary'}
                        fontWeight={500}
                        fontSize={13}
                        lineHeight={19.5}
                    >
                        {subtitle}
                    </Typography>
                )}

                {actionLabel ? (
                    <Button label={actionLabel} color="accent" size="small" mt={subtitle ? 4 : 2} onPress={onPress} />
                ) : null}
            </VStack>

            {source && <Image source={source} style={sx(styles.image, imageStyle)} {...restImageProps} />}
        </Paper>
    );
}

const useStyles = makeStyles(() => ({
    image: {
        width: '100%',
        height: 260,
    },
}));
