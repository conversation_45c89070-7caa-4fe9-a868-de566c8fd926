import { transparentize } from 'polished';
import { Component, ReactElement } from 'react';
import type RNSegmentedPickerComponent from 'react-native-segmented-picker';
import RNSegmentedPicker from 'react-native-segmented-picker';
import { useTheme } from 'styled-components/native';
import { ModalStateProps } from '~/hooks/useModal';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type RNSegmentedPickerProps = RNSegmentedPickerComponent extends Component<infer P, any> ? P : never;

export interface SegmentedPickerProps
    extends ModalStateProps,
        Partial<
            Omit<
                RNSegmentedPickerProps,
                | 'visible'
                | 'onCancel'
                | 'backgroundColor'
                | 'confirmTextColor'
                | 'pickerItemTextColor'
                | 'toolbarBackgroundColor'
                | 'toolbarBorderColor'
                | 'selectionBorderColor'
                | 'selectionBackgroundColor'
            >
        > {}

export function SegmentedPicker({ open, onClose, ...rest }: SegmentedPickerProps): ReactElement {
    const theme = useTheme();

    return (
        <RNSegmentedPicker
            visible={open}
            onCancel={onClose}
            backgroundColor={theme.palette.backgroundPrimary.main}
            confirmTextColor={theme.palette.accent.main}
            pickerItemTextColor={theme.palette.typography.textPrimary}
            toolbarBackgroundColor={theme.palette.backgroundSecondary.main}
            toolbarBorderColor={theme.palette.borderOpaque.main}
            selectionBorderColor={theme.palette.accent.main}
            selectionBackgroundColor={transparentize(0.9, theme.palette.accent.main)}
            {...rest}
        />
    );
}
