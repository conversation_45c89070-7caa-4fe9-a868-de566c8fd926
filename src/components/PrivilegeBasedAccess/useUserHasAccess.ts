import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { useUser } from '~/hooks/useUser';
import { canUserView } from '~/lib/utils/user';
import { useAuthStore } from '~/store/useAuthStore';

export function useUserHasAccess(privileges: UserPrivilegeType | UserPrivilegeType[]): boolean {
    const user = useUser();
    const subscription = useAuthStore((state) => state.subscription);

    if (!user || !subscription) {
        return false;
    }

    const userPrivileges = Array.isArray(privileges) ? privileges : [privileges];

    return canUserView(user, subscription, userPrivileges);
}
