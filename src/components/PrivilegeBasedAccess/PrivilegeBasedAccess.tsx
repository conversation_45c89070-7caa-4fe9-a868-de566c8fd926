import { useNavigation } from '@react-navigation/native';
import React, { useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import BookrIcon from '~/components/icons/Bookr';
import { Button } from '~/components/ui/Button';
import { Grid } from '~/components/ui/Grid';
import { Paper, PaperProps } from '~/components/ui/Paper';
import { Typography } from '~/components/ui/Typography';
import { useUser } from '~/hooks/useUser';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { canUserView } from '~/lib/utils/user';
import { useAuthStore } from '~/store/useAuthStore';

interface PrivilegeBasedAccessProps extends PaperProps {
    children: JSX.Element;
    privileges: UserPrivilegeType | UserPrivilegeType[];
}

export function PrivilegeBasedAccess({ privileges, children, ...rest }: PrivilegeBasedAccessProps) {
    const { t } = useTranslation();
    const user = useUser();
    const { navigate } = useNavigation();
    const subscription = useAuthStore((state) => state.subscription);
    const userPrivileges = useMemo(() => (Array.isArray(privileges) ? privileges : [privileges]), [privileges]);

    const canView = useMemo(() => {
        if (!user) {
            return false;
        }
        return canUserView(user, subscription, userPrivileges);
    }, [user, subscription, userPrivileges]);

    if (canView) {
        return children;
    }

    const handleClick = () => {
        logAnalyticsEvent(AnalyticsEvent.ChoosePlan);
        navigate('SubscriptionPlansScreen');
    };

    return (
        <Paper
            bgColor={'backgroundPrimary'}
            flexDirection={'row'}
            alignSelf={'center'}
            justifyContent={'center'}
            p={3}
            mt={3}
            {...rest}
        >
            <Grid alignItems={'center'} width={'100%'}>
                <Grid
                    justifyContent={'center'}
                    alignItems={'center'}
                    bgColor={'backgroundSecondary'}
                    width={72}
                    height={72}
                    borderRadius={36}
                >
                    <BookrIcon color={'accent'} size={48} />
                </Grid>
            </Grid>
            <Typography textAlign={'center'} variant={'title2'} fontSize={22} lineHeight={27} fontWeight={700} mt={3}>
                {t('privilegeBasedAccessTitle')}
            </Typography>
            <Paper flexDirection={'row'} bgColor={'backgroundSecondary'} p={2} my={2} width={'100%'}>
                <Trans
                    values={{ value: userPrivileges.map((p) => t(p as any)).join(', ') }}
                    i18nKey={userPrivileges.length > 1 ? 'privilegeBasedAccessMultiple' : 'privilegeBasedAccessSingle'}
                    components={{
                        Text: <Typography variant={'footnote'} fontWeight={500} color={'textSecondary'} />,
                        strong: <Typography variant={'footnote'} fontWeight={700} color={'primary'} />,
                    }}
                />
            </Paper>
            <Button width={'100%'} onPress={handleClick} color={'accent'} label={t('chooseAPlan')} size={'large'} />
        </Paper>
    );
}
