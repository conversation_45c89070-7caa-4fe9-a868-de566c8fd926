import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import moment from 'moment-timezone';
import { ReactElement, useMemo } from 'react';
import { CalendarList as RNCalendar, CalendarListProps as RNCalendarProps } from 'react-native-calendars';
import { useTheme } from 'styled-components/native';
import { formatDate } from '@bookr-technologies/core';
import { HStack, VStack } from '~/components/ui/Grid';
import { IconButton } from '~/components/ui/IconButton';
import { Typography } from '~/components/ui/Typography';
import { makeStyles } from '~/components/ui/makeStyles';
import { useEvent } from '~/hooks/useEvent';

export type CalendarProps = RNCalendarProps;

const useStyles = makeStyles(({ theme }) => ({
    root: {
        backgroundColor: theme.palette.backgroundPrimary.main,
        paddingLeft: 0,
        paddingRight: 0,
    },
}));

export function Calendar({ current, markedDates, ...rest }: CalendarProps): ReactElement {
    const styles = useStyles();
    const theme = useTheme();
    const today = formatDate(new Date(), 'YYYY-MM-DD');

    const weekDays = useMemo(() => {
        const now = moment();
        now.startOf('week');

        return Array(7)
            .fill('')
            .map((_, i) => {
                const day = now.clone().add(i, 'days');
                return day.format('dd').slice(0, 1).toUpperCase();
            });
    }, []);

    const markedDates$ = useMemo<CalendarProps['markedDates']>(() => {
        const key = formatDate(current || new Date(), 'YYYY-MM-DD');

        return {
            ...markedDates,
            [today]: {
                ...(markedDates?.[today] || {}),
                selected: true,
                customTextStyle: {
                    color: theme.palette.typography.primary,
                },
                customContainerStyle: {
                    backgroundColor: theme.palette.backgroundTertiary.main,
                    height: 34,
                    width: 34,
                    borderRadius: 17,
                },
            },
            [key]: {
                ...(markedDates?.[key] || {}),
                selected: true,
                selectedColor: theme.palette.typography.textPrimary,
                selectedTextColor: theme.palette.typography.textPrimary,
            },
        };
    }, [
        current,
        markedDates,
        theme.palette.backgroundTertiary.main,
        theme.palette.typography.primary,
        theme.palette.typography.textPrimary,
        today,
    ]);

    const renderCustomHeader = useEvent(({ onPressArrowLeft, onPressArrowRight, month }) => (
        <VStack>
            <HStack
                alignItems={'center'}
                alignContent={'center'}
                justifyContent={'space-between'}
                bgColor={'backgroundSecondary'}
                mt={1}
                mb={2}
                px={2}
                height={48}
                borderRadius={12}
            >
                <IconButton onPress={(): void => onPressArrowLeft({}, month)}>
                    <MaterialIcons name={'keyboard-arrow-left'} />
                </IconButton>
                <Typography textTransform={'capitalize'} variant={'callout'} fontWeight={500}>
                    {formatDate(new Date(month), 'MMMM YYYY')}
                </Typography>
                <IconButton onPress={(): void => onPressArrowRight({}, month)}>
                    <MaterialIcons name={'keyboard-arrow-right'} />
                </IconButton>
            </HStack>
            <HStack mb={2} flexWrap={'nowrap'} fullWidth>
                {weekDays.map((day, i) => (
                    <Typography
                        width={'14.2857142857%'}
                        key={`${day}-${i}`}
                        textAlign={'center'}
                        variant="footnote"
                        fontWeight={700}
                    >
                        {day}
                    </Typography>
                ))}
            </HStack>
        </VStack>
    ));

    return (
        <RNCalendar
            pagingEnabled
            horizontal
            enableSwipeMonths
            current={current}
            firstDay={moment().localeData().firstDayOfWeek()}
            customHeader={renderCustomHeader}
            calendarStyle={styles.root}
            markedDates={markedDates$}
            theme={{
                calendarBackground: theme.palette.backgroundPrimary.main,
                dayTextColor: theme.palette.typography.primary,
                selectedDayBackgroundColor: theme.palette.accent.main,
                selectedDayTextColor: theme.palette.primary.main,
                textDayFontFamily: theme.typography.fontFamilyWeightMap['500'] as string,
                textDayFontSize: Number(theme.typography.variants.footnote.fontSize),
            }}
            {...rest}
        />
    );
}
