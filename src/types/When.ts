/**
 * @example
 * ```typescript
 * type Item = { name: string } & (When<{ name: 'bob' }, { car: boolean }> | When<{ name: 'joe' }, { ball: boolean }>);
 *
 * const item: Item = {name: 'bob', car: true}; // Works
 * const item: Item = {name: 'joe', ball: true}; // Works
 * const item: Item = {name: 'bob', ball: true}; // Fails
 * const item: Item = {name: 'joe', car: true}; // Fails
 *
 * ```
 */
export type When<T, B> = T & B;
