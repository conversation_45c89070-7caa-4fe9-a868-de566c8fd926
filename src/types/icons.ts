import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Icon } from '@expo/vector-icons/build/createIconSet';

export type MaterialIconsType = typeof MaterialIcons;
export type MaterialCommunityIconsType = typeof MaterialCommunityIcons;

export type MaterialIconsName = MaterialIconsType extends Icon<infer N, string> ? N : never;
export type MaterialCommunityIconsName = MaterialCommunityIconsType extends Icon<infer N, string> ? N : never;
