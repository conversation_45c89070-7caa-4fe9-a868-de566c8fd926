import { RefObject, useCallback, useLayoutEffect, useRef, useState } from 'react';
import {
    GestureResponderEvent,
    LayoutChangeEvent,
    LayoutRectangle,
    MeasureInWindowOnSuccessCallback,
} from 'react-native';

type LayoutHandler = ((event: LayoutChangeEvent) => void) | ((event: GestureResponderEvent) => void) | undefined;

export function useLayout(...pipes: LayoutHandler[]) {
    const pipesRef = useRef(pipes);

    const [layout, setLayout] = useState<LayoutRectangle>({
        height: 0,
        width: 0,
        x: 0,
        y: 0,
    });

    const handler = useCallback((event: LayoutChangeEvent | GestureResponderEvent) => {
        event.persist();
        const layoutEvent = event as LayoutChangeEvent;
        const gestureEvent = event as GestureResponderEvent;

        if (layoutEvent.nativeEvent.layout) {
            setLayout(layoutEvent.nativeEvent.layout);
        } else {
            setLayout((prev) => ({
                ...prev,
                x: gestureEvent.nativeEvent?.locationX || prev.x,
                y: gestureEvent.nativeEvent?.locationY || prev.y,
            }));
        }

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        pipesRef.current.forEach((pipe) => pipe && pipe(event as any));
    }, []);

    useLayoutEffect(() => {
        pipesRef.current = pipes;
    }, [pipes]);

    return {
        ...layout,
        initialized: layout.width !== 0,
        handler,
    };
}

export function withMeasurement(
    ref:
        | RefObject<{
              measureInWindow(callback: MeasureInWindowOnSuccessCallback): void;
          }>
        | undefined,
    callback: (layout: LayoutRectangle) => void,
) {
    return async () => {
        if (ref?.current?.measureInWindow) {
            const measurement = await new Promise<LayoutRectangle>((resolve) => {
                ref?.current?.measureInWindow((x: number, y: number, width: number, height: number) => {
                    resolve({ height, width, x, y });
                });
            });

            callback(measurement);
        }
    };
}
