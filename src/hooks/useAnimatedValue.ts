import { MutableRefObject, useCallback, useRef } from 'react';
import { Animated, Easing } from 'react-native';

interface AnimatedValueHelpers {
    spring(
        newValue: number | Animated.AnimatedValue | Animated.AnimatedValueXY,
        config?: Partial<Omit<Animated.SpringAnimationConfig, 'toValue'>>,
    ): Animated.CompositeAnimation;

    timing(
        newValue: number | Animated.AnimatedValue | Animated.AnimatedValueXY,
        config?: Partial<Omit<Animated.TimingAnimationConfig, 'toValue'>>,
    ): Animated.CompositeAnimation;
}

type useAnimatedValueTuple = [animatedValue: MutableRefObject<Animated.Value>, helpers: AnimatedValueHelpers];

export function useAnimatedValue(value: number): useAnimatedValueTuple {
    const valueRef = useRef(new Animated.Value(value));

    const timing = useCallback<AnimatedValueHelpers['timing']>(
        (newValue, config) =>
            Animated.timing(valueRef.current, {
                duration: 200,
                easing: Easing.cubic,
                toValue: newValue,
                useNativeDriver: true,
                ...config,
            }),
        [],
    );

    const spring = useCallback<AnimatedValueHelpers['spring']>(
        (newValue, config) =>
            Animated.spring(valueRef.current, {
                toValue: newValue,
                useNativeDriver: true,
                ...config,
            }),
        [],
    );

    return [
        valueRef,
        {
            spring,
            timing,
        },
    ];
}
