import { DeviceEventEmitter } from 'react-native';
import { DeviceEvents, DeviceEventsEnum } from '~/constants/DeviceEventsEnum';
import { useEvent } from './useEvent';

type EventFunc = <E extends DeviceEventsEnum, D = DeviceEvents[E]>(eventName: E, eventData?: D) => void;

export function useDeviceEventDispatch(): EventFunc {
    return useEvent((eventName, eventData) => DeviceEventEmitter.emit(eventName, eventData));
}
