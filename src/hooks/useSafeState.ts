import { Dispatch, SetStateAction, useCallback, useState } from 'react';
import { useIsMounted } from './useIsMounted';

export function useSafeState<S>(initialState?: S | (() => S)): [S, Dispatch<SetStateAction<S>>] {
    const isMounted = useIsMounted();
    const [state, setState] = useState<S>(initialState as S);

    const setStateSafe = useCallback<Dispatch<SetStateAction<S>>>(
        (value) => {
            if (isMounted.current) {
                setState(value);
            }
        },
        [isMounted],
    );

    return [state, setStateSafe];
}
