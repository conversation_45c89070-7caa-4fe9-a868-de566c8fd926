import * as Sentry from '@sentry/react-native';
import * as Notifications from 'expo-notifications';
import { NotificationTriggerInput } from 'expo-notifications/src/Notifications.types';
import moment from 'moment';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { useLogger } from '~/hooks/useLogger';
import { useApplicationStore } from '~/store/useApplicationStore';

interface PushNotification {
    body: string;
    data: any;
    title: string;
}

export function useLocalPushNotificationScheduler() {
    const log = useLogger('useLocalPushNotificationScheduler');
    const { t } = useTranslation();

    const scheduleLocalPushNotification = useCallback(
        async (notification: PushNotification, trigger: NotificationTriggerInput) => {
            try {
                const identifier = await Notifications.scheduleNotificationAsync({
                    content: notification,
                    trigger,
                });
                log.log('notification scheduled', { identifier });
            } catch (error) {
                log.error('error scheduling local push notification', { error });
                Sentry.captureException(error);
            }
        },
        [log],
    );

    const cancelLocalPushNotification = useCallback(
        async (identifier: string) => {
            try {
                await Notifications.cancelScheduledNotificationAsync(identifier);
                log.log('cancelled scheduled notification', { identifier });
            } catch (error) {
                log.error('failed to cancel scheduled notification', { identifier });
                Sentry.captureException(error);
            }
        },
        [log],
    );

    const scheduleAppointmentReviewPushNotification = useCallback(
        async (appointment: AppointmentModel) => {
            try {
                useApplicationStore.getState().pushAppointmentsForReview(appointment);
                await Notifications.scheduleNotificationAsync({
                    identifier: `review-appointment-${appointment.id}`,
                    content: {
                        title: t('reviewYourExperience'),
                        body: t('yourExperienceWith', { displayName: appointment.staff.displayName }),
                        data: {
                            type: 'NAVIGATE_SCREEN',
                            payload: {
                                action: 'review',
                                params: {
                                    appointmentId: appointment.id,
                                },
                            },
                        },
                    },
                    trigger: moment(appointment.dateTime).add(appointment.service.duration, 'minutes').toDate(),
                });
                log.log('scheduled notification', {
                    appointmentId: appointment.id,
                });
            } catch (error) {
                log.error('error scheduling appointment review push notification', { error });
            }
        },
        [log, t],
    );

    const cancelAppointmentReviewPushNotification = useCallback(
        async (appointment: AppointmentModel) => {
            try {
                useApplicationStore.getState().deleteFromAppointmentsForReview(appointment.id);
                await cancelLocalPushNotification(`review-appointment-${appointment.id}`);
                log.log('cancelled scheduled', {
                    appointmentId: appointment.id,
                });
            } catch (error) {
                log.error('error cancelling appointment review push notification', { error });
                Sentry.captureException(error);
            }
        },
        [cancelLocalPushNotification, log],
    );

    return {
        scheduleLocalPushNotification,
        cancelLocalPushNotification,
        scheduleAppointmentReviewPushNotification,
        cancelAppointmentReviewPushNotification,
    };
}
