import { FormikConfig } from 'formik';
import { useMemo } from 'react';
import * as Yup from 'yup';
import type { ObjectShape } from 'yup/lib/object';
import { useEvent } from '~/hooks/useEvent';
import { value, Value } from '~/lib/value';

export function useValidationSchema(
    schema: Value<ObjectShape, [yup: typeof Yup]>,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    deps: any[] = [],
): Pick<FormikConfig<unknown>, 'validationSchema' | 'validateOnBlur' | 'validateOnChange'> {
    const createSchema = useEvent(() => Yup.object().shape(value(schema, Yup)));

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const validationSchema = useMemo(() => createSchema(), deps);

    return {
        validateOnBlur: true,
        validateOnChange: true,
        validationSchema,
    };
}
