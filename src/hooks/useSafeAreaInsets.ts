import { useMemo } from 'react';
import { EdgeInsets, useSafeAreaInsets as useInsets } from 'react-native-safe-area-context';
import { useTheme } from 'styled-components/native';

interface Options {
    min: number;
}

export function useSafeAreaInsets(asSpacing = false, { min = 2 }: Partial<Options> = {}): EdgeInsets {
    const theme = useTheme();
    const safeAreaInsets = useInsets();

    return useMemo(() => {
        const insets = { ...safeAreaInsets };
        const minValue = asSpacing ? min : theme.mixins.spacingValue(min);

        if (asSpacing) {
            insets.top = insets.top / theme.spacingOptions.size;
            insets.right = insets.left / theme.spacingOptions.size;
            insets.bottom = insets.bottom / theme.spacingOptions.size;
            insets.left = insets.left / theme.spacingOptions.size;
        }

        insets.top = Math.max(minValue, insets.top);
        insets.right = Math.max(minValue, insets.right);
        insets.bottom = Math.max(minValue, insets.bottom);
        insets.left = Math.max(minValue, insets.left);

        return insets;
    }, [safeAreaInsets, asSpacing, min, theme.mixins, theme.spacingOptions.size]);
}
