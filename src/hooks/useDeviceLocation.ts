import * as Location from 'expo-location';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Nullable } from '@bookr-technologies/core/types';
import { useEvent } from '~/hooks/useEvent';
import { useLogger } from '~/hooks/useLogger';
import { useNotifications } from '~/hooks/useNotifications';
import { FallbackLocation } from '~/lib/map/googleMapsEndpoint';

interface Result {
    coords: Nullable<Location.LocationObjectCoords>;
    fetch: () => Promise<void>;
    fetching: boolean;
    granted: boolean;
}

export function useDeviceLocation(): Result {
    const logger = useLogger('useDeviceLocation');
    const { t } = useTranslation();
    const notifications = useNotifications();
    const [granted, setGranted] = useState<boolean>(true);
    const [fetching, setFetching] = useState<boolean>(true);
    const [coords, setCoords] = useState<Location.LocationObjectCoords | null>(null);

    const fetch = useEvent(async () => {
        try {
            setFetching(true);
            const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Lowest,
            });
            setCoords(location.coords);
        } catch (error) {
            logger.error('could not fetch location', { error: JSON.stringify(error, null, 2) });
        } finally {
            setFetching(false);
        }
    });

    useEffect(
        () => {
            (async (): Promise<void> => {
                setFetching(true);
                const permission = await Location.requestForegroundPermissionsAsync();
                setGranted(permission.granted);

                if (!permission.granted) {
                    notifications.warning(t('locationPermissionsDenied'));
                    logger.warn('Location permission denied', {
                        granted: permission.granted,
                        status: permission.status,
                        canAskAgain: permission.canAskAgain,
                        android: permission.android,
                        ios: permission.ios,
                        expires: permission.expires,
                    });

                    setCoords({
                        ...FallbackLocation,
                        accuracy: 0,
                        altitude: 0,
                        altitudeAccuracy: 0,
                        heading: 0,
                        speed: 0,
                    });
                    setFetching(false);
                    return;
                }

                await fetch();
            })();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    );

    return {
        coords,
        fetch,
        fetching,
        granted,
    };
}
