/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEvent } from '~/hooks/useEvent';
import { Value } from '~/lib/value';
import { useSafeState } from './useSafeState';

type Result<P> = P extends Promise<infer T> ? Promise<T | null> : Promise<P | null>;

type Loader<T extends string> = Record<T | 'default', boolean> & {
    any(): boolean;

    /**
     * This method is used to set loading state based on key (default if not specified),
     * based on the promise argument.
     * The function set the loading state to true, try to resolve the promise and finally
     * set the loading state to false.
     *
     * @example
     * ```ts
     * const loading = useLoading();
     * // ...
     * const handleAsyncEvent = useCallback(async () => {
     *     const promise = new Promise((resolve) => (
     *         setTimeout(() => resolve(true), 1000)
     *     ));
     *
     *     // load the state from a promise
     *     loading.from(promise);
     *
     *     // load the state from a promise function.
     *     loading.from(async () => {
     *         await Promise.resolve();
     *     });
     *
     *     // load the state from a promise function and assign it to a key.
     *     loading.from('key', async () => {
     *         await Promise.resolve();
     *     })
     * }, [loading.from]);
     * ```
     */
    from<P>(promise: Value<P>): Result<P>;
    from<P>(key: T, promise: Value<P>): Result<P>;

    /**
     * This method will create a callback that will automatically handle the loading state,
     * based on the key provided, or the default key if no key is provided.
     * @example
     * ```ts
     * const loading = useLoading();
     * // ...
     * const handleAsyncEvent = loading.use(async () => {
     *     await someOperation();
     * });
     * ```
     */
    use<P>(promise: () => P): () => Result<P>;
    use<P>(key: T, promise: () => P): () => Result<P>;

    isLoading(key?: T): boolean;
    set(key: T | 'default', value: boolean): void;
    start(key?: T): void;
    stop(key?: T): void;
};

interface UseLoadingOptions<T extends string> {
    delay?: number;
    initialState?: boolean | Record<T, boolean>;
}

export function useLoading<T extends string = 'default'>({
    initialState = false,
    delay = 100,
}: UseLoadingOptions<T> = {}): Loader<T> {
    const [state, setState] = useSafeState<Record<string, boolean>>(() => {
        if (typeof initialState === 'boolean') {
            return { default: initialState };
        }

        return {
            default: false,
            ...initialState,
        };
    });

    const isLoading = useEvent((key?: T) => !!(key ? state[key] : state.default));
    const any = useEvent(() => Object.values(state).some((value) => value));

    const set = useEvent((key: T | 'default', value: boolean) =>
        setState((state) => ({
            ...state,
            [key]: value,
        })),
    );

    const start = useEvent((key?: T) => set(key || 'default', true));

    const stop = useEvent((key?: T) => set(key || 'default', false));

    const from = useEvent(async (key, promise) => {
        if (!promise) {
            promise = key;
            key = 'default';
        }

        let result = null;
        // Delay start to avoid quick loaders.
        const cancelRef = setTimeout(() => start(key), delay);

        try {
            if (typeof promise === 'function') {
                result = await promise();
            } else {
                result = await promise;
            }
        } finally {
            clearTimeout(cancelRef);
            stop(key);
        }

        return result;
    });

    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    function use<T>(key: T, func: () => Promise<T>): () => Promise<T> {
        return useEvent(() => from(key, func));
    }

    return {
        ...state,
        any,
        from,
        use,
        isLoading,
        set,
        start,
        stop,
    } as Loader<T>;
}
