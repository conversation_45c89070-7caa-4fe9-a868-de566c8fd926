import { Icon, IconProps } from '@expo/vector-icons/build/createIconSet';
import { tint } from 'polished';
import { ReactElement, useMemo } from 'react';
import { useTheme } from 'styled-components/native';
import type { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';
import { extendChildren } from '~/components/utils/extendChildren';

export interface ResolveIconProps {
    color?: ThemeColorKeys | string;
    disabled?: boolean | null;
    size?: 'xsmall' | 'small' | 'medium' | 'large' | 'inherit' | number;
    withContrast?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type IconElement = ReactElement<IconProps<any> & ResolveIconProps, Icon<any, any>>;

export function useResolveIcon(
    icon?: IconElement | null,
    { color, size, disabled, withContrast }: ResolveIconProps = {},
): IconElement | null {
    const theme = useTheme();

    const iconSize = useMemo(() => {
        switch (size) {
            case 'xsmall':
                return 18;
            case 'small':
                return 20;
            case 'medium':
                return 22;
            case 'large':
                return 24;
            default:
                return size || 24;
        }
    }, [size]);

    const iconColor = useMemo(() => {
        let color$ = theme.mixins.getColor((color || '') + `${withContrast ? '.contrast' : ''}`, 'primary');

        if (disabled) {
            color$ = tint(0.5, color$);
        }

        return color$;
    }, [color, disabled, theme.mixins, withContrast]);

    if (!icon) {
        return null;
    }

    return extendChildren(icon, ({ size, color, ...rest }) => {
        return {
            color: color ? theme.mixins.getColor(color) : iconColor,
            // TODO: find a better way to know if the icon size is not specify, for now 12 is default.
            size: size && size !== 12 ? size : iconSize,
            ...rest,
        };
    });
}
