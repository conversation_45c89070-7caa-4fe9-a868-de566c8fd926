import { TOptions } from 'i18next';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '~/lib/translations/keys';

type Args =
    | [key: TranslationKeys | string, options?: TOptions]
    | [key: TranslationKeys | string, defaultValue?: string, options?: TOptions];

/**
 * @description Use this hook only if you have dynamic translation keys. An alias for the `t` function to avoid
 *              things like `t('key.${extraKey}' as any)`.
 *
 */
export function useI18n(): {
    (...args: Args): string;
    (key: TranslationKeys | string, defaultValue: string, options: TOptions): string;
} {
    const { t } = useTranslation();

    // eslint-disable-next-line @typescript-eslint/no-explicit-any,@typescript-eslint/ban-ts-comment
    // @ts-ignore
    return useCallback((...args) => t(...args), [t]);
}
