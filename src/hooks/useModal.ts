/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEvent } from '~/hooks/useEvent';
import { useLRUCache } from '~/hooks/useLRUCache';
import { useSafeState } from '~/hooks/useSafeState';

export interface ModalStateProps {
    onClose: () => void;
    open: boolean;
}

interface UseBottomSheetAlertReturnType<T> {
    close: () => void;
    context: T | null;
    isOpen: boolean;
    open: () => void;
    props: ModalStateProps;

    openWithContext(context: T): void;

    openWithContextHandler(context: T): () => void;
}

export function useModal<T = any>(): UseBottomSheetAlertReturnType<T> {
    const [contextData, setContextData] = useSafeState<T | null>(null);
    const [open, setOpen] = useSafeState(false);
    const lru = useLRUCache();

    const onOpen = useEvent(() => setOpen(true));

    const onClose = useEvent(() => {
        setOpen(false);
        setContextData(null);
    });

    const openWithContext = useEvent((context: T) => {
        setOpen(true);
        setContextData(context);
    });

    const openWithContextHandler = useEvent((context: T) => {
        const handler = (): void => openWithContext(context);
        if (typeof context !== 'string') {
            return handler;
        }

        if (!lru.has(context)) {
            lru.set(context, handler);
        }

        return lru.get(context);
    });

    return {
        props: { open, onClose },
        context: contextData,
        isOpen: open,
        open: onOpen,
        close: onClose,
        openWithContext,
        openWithContextHandler,
    };
}
