/* eslint-disable prefer-promise-reject-errors */
import { useQuery, UseQueryOptions, UseQueryResult } from 'react-query';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { businessClientEndpoint } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { ClientDetailsModel } from '@bookr-technologies/api/models/ClientDetailsModel';
import { reactQueryClient } from '~/lib/clients/reactQuery';

export const useBusinessClient = (
    clientId?: string,
    options?: Pick<UseQueryOptions<ClientDetailsModel>, 'onError'>,
): UseQueryResult<ClientDetailsModel> =>
    useQuery({
        ...options,
        enabled: !!clientId,
        queryKey: ['business', 'client', clientId],
        queryFn: () => {
            if (!clientId) {
                return Promise.reject();
            }

            return businessClientEndpoint.show(clientId);
        },
        initialData: () => reactQueryClient.getQueryData(['business', 'client', clientId]),
    });

export const useBusinessClientDocuments = (clientId?: string): UseQueryResult<string[]> => {
    const queryKey = ['business', 'client', clientId, 'documents'];
    const queryFn = (): Promise<string[]> => {
        if (!clientId) {
            return Promise.reject();
        }

        return businessClientsEndpoint.getDocuments(clientId);
    };

    return useQuery({
        queryKey,
        queryFn,
        enabled: !!clientId,
        initialData: () => reactQueryClient.getQueryData(queryKey),
    });
};
