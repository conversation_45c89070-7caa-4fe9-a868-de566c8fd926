import { useCallback, useEffect, useLayoutEffect, useRef } from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function useInterval<T extends (...args: any[]) => any>(handler: T, delay: number) {
    const handlerRef = useRef<T>(handler);
    const tickRef = useRef<NodeJS.Timer | null>(null);

    const stop = useCallback(() => {
        if (tickRef.current) {
            clearInterval(tickRef.current);
            tickRef.current = null;
        }
    }, []);

    // Remember the latest callback.
    useLayoutEffect(() => {
        handlerRef.current = handler;
    }, [handler]);

    // Set up the interval.
    useEffect(() => {
        function tick() {
            handlerRef.current(stop);
        }

        tickRef.current = setInterval(tick, delay);

        return stop;
    }, [delay, stop]);
}
