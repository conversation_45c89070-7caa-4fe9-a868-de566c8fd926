import { set } from 'lodash';
import { MutableRefObject, useMemo } from 'react';
import { Animated, ViewStyle } from 'react-native';

type StyleObject<T, K extends keyof T = keyof T> = {
    [key in K]: T[K] | Animated.AnimatedValue | MutableRefObject<Animated.AnimatedValue>;
};

export function useAnimStyle<S extends ViewStyle, T extends StyleObject<S>>(style: T | (() => T)): S {
    return useMemo(() => {
        const styleCollection: T = typeof style === 'function' ? style() : style;

        return Object.entries(styleCollection).reduce((acc, [key, value]) => {
            if (value !== null && typeof value !== 'undefined') {
                if (
                    (value as MutableRefObject<Animated.AnimatedValue>).current &&
                    Object.keys(value as object).length === 1
                ) {
                    set(acc, key, (value as MutableRefObject<Animated.AnimatedValue>).current);
                } else {
                    set(acc, key, value);
                }
            }

            return acc;

            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        }, {} as any);
    }, [style]);
}
