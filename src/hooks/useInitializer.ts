import { useEffect, useState } from 'react';
import { useEvent } from '~/hooks/useEvent';

type Initializer<T> = () => Promise<T>;

export function useInitializer<T>(initializer: Initializer<T>): [ready: boolean, data: T | null] {
    const [ready, setReady] = useState(false);
    const [data, setData] = useState<T | null>(null);

    const handleInitialize = useEvent(initializer);

    useEffect(() => {
        (async (): Promise<void> => {
            try {
                const data = await handleInitialize();
                setData(data);
            } catch (e) {
                console.warn(e);
            } finally {
                setReady(true);
            }
        })();
    }, [handleInitialize]);

    return [ready, data];
}
