/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEvent } from '~/hooks/useEvent';

/**
 * For the sake of passing inline handlers, this will create a memoized
 * function that will should run our inline function but react will not see it as a new
 * function from render to render.
 */
export function useHandler() {
    return useEvent((func: (...args: any) => any) => () => func());
}
