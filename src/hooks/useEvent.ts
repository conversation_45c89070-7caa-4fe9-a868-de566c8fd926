/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from 'react';
import { Optional } from '@bookr-technologies/core/types';
import { useValueRef } from '~/hooks/useValueRef';

type EventHandler<R> = (...args: any[]) => R;

export function useEvent<T extends EventHandler<any>>(callback: Optional<T>): T {
    const callbackRef = useValueRef(callback);

    return useCallback(
        (...args: any[]): any => {
            if (callbackRef.current) {
                return callbackRef.current(...args);
            }
        },
        [callbackRef],
    ) as T;
}
