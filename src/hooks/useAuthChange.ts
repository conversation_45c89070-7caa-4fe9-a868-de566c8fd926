import * as Sentry from '@sentry/react-native';
import { useEffect } from 'react';
import { useAuthRedirect } from '~/hooks/useAuthRedirect';
import { firebaseAuth } from '~/lib/firebase/auth';

const calledRef: Record<string, boolean> = {};

export function useAuthChange(): void {
    const redirect = useAuthRedirect();

    useEffect(
        () =>
            firebaseAuth.onAuthStateChanged(async (firebaseUser) => {
                Sentry.setUser({
                    id: firebaseUser?.uid ?? 'n/a',
                    email: firebaseUser?.email ?? 'n/a',
                    username: firebaseUser?.displayName ?? 'n/a',
                });

                const key = firebaseUser?.uid ?? 'n/a';
                if (!calledRef?.[key]) {
                    calledRef[key] = true;
                    await redirect(firebaseUser);
                }

                calledRef[key] = false;
            }),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    );
}
