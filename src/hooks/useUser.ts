import type { User } from 'firebase/auth';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useAuthStore } from '~/store/useAuthStore';

/**
 * Hook for getting the current user
 */
export function useUser(): UserModel | null {
    return useAuthStore((state) => state.user);
}

/**
 * Hook for getting the firebase user.
 */
export function useFirebaseUser(): User | null {
    return useAuthStore((state) => state.firebaseUser);
}
