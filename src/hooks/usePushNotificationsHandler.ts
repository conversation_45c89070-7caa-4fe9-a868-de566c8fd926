import { CommonActions, useNavigation } from '@react-navigation/native';
import { Linking } from 'react-native';
import { RootStackParamList } from '~/RoutesParams';
import { getCurrentRoute } from '~/lib/utils/navigation';

interface NavigateScreenPayload {
    action: string;
    params?: any;
}

interface OpenURLPayload {
    url: string;
}

type PushNotificationEvent<Type extends string, Payload extends object> = {
    payload: Payload;
    type: Type;
};

export type PushNotificationEvents =
    | PushNotificationEvent<'NAVIGATE_SCREEN', NavigateScreenPayload>
    | PushNotificationEvent<'OPEN_URL', OpenURLPayload>;

export function usePushNotificationsHandler() {
    const { navigate, dispatch } = useNavigation<any>();

    return (notification: PushNotificationEvents) => {
        switch (notification.type) {
            case 'NAVIGATE_SCREEN':
                const [screen, params] = getScreen(notification.payload);
                const currentRoute = getCurrentRoute();
                if (currentRoute === 'MainScreen') {
                    dispatch(
                        CommonActions.reset({
                            index: 0,
                            routes: [
                                { name: 'ClientApplication', params: {} },
                                { name: screen, params },
                            ],
                        }),
                    );
                } else {
                    navigate(screen, params);
                }

                break;
            case 'OPEN_URL':
                Linking.openURL(notification.payload.url);
                break;
            default:
                break;
        }
    };
}

function getScreen(
    payload: NavigateScreenPayload,
): [screen: keyof RootStackParamList, params?: RootStackParamList[keyof RootStackParamList]] {
    switch (payload.action) {
        case 'home':
            return ['ClientApplication', { screen: 'Home', params: payload.params }];
        case 'search':
            return ['SearchMap', payload.params];
        case 'appointment':
            return ['AppointmentDetailsScreen', payload.params];
        case 'review':
            return ['ReviewScreen', payload.params];
        default:
            return ['MainScreen'];
    }
}
