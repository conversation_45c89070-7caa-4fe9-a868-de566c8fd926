import { useBusiness, UseBusinessOptions, UseBusinessResult } from '~/hooks/useBusiness';
import { useRefreshOnFocus } from '~/hooks/useRefreshOnFocus';
import { useUser } from '~/hooks/useUser';

export function useUserBusiness(options: UseBusinessOptions = {}): UseBusinessResult {
    const user = useUser();
    const businessId = typeof user?.business === 'object' ? user?.business?.id : user?.business;
    const query = useBusiness(businessId, options);

    useRefreshOnFocus(!!businessId ? query.refetch : () => null);

    return query;
}
