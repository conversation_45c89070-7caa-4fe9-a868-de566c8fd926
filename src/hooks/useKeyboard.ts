import { useCallback, useEffect, useState } from 'react';
import { Keyboard, KeyboardEvent, ScreenRect } from 'react-native';

const defaultScreenRect = {
    height: 0,
    screenX: 0,
    screenY: 0,
    width: 0,
};

export function useKeyboard() {
    const [coords, setCoords] = useState<ScreenRect>(defaultScreenRect);

    const [height, setHeight] = useState(0);
    const [active, setActive] = useState(false);

    const handleKeyboardShow = useCallback((event: KeyboardEvent) => {
        setActive(true);
        setHeight(event.endCoordinates.height);
        if (event.startCoordinates) {
            setCoords(event.startCoordinates);
        }
    }, []);

    const handleKeyboardHide = useCallback(() => {
        setActive(false);
        setHeight(0);
        setCoords(defaultScreenRect);
    }, []);

    useEffect(() => {
        const subscription = Keyboard.addListener('keyboardWillShow', handleKeyboardShow);
        return () => {
            subscription.remove();
        };
    }, [handleKeyboardShow]);

    useEffect(() => {
        const subscription = Keyboard.addListener('keyboardWillHide', handleKeyboardHide);
        return () => {
            subscription.remove();
        };
    }, [handleKeyboardHide]);

    return {
        active,
        coords,
        height,
    };
}
