/* eslint-disable @typescript-eslint/no-explicit-any */
import { Optional } from '@bookr-technologies/core/types';
import { useDebouncedFunc } from '~/hooks/useDebouncedFunc';
import { useEvent } from '~/hooks/useEvent';
import { useIsMounted } from '~/hooks/useIsMounted';
import { useValueRef } from '~/hooks/useValueRef';

export function useDebouncedEvent<T extends (...args: any[]) => any>(
    callback: Optional<T>,
    delay: number,
    unsafe = false,
): T {
    const callbackRef = useValueRef(callback);
    const debouncedCallback = useDebouncedFunc(delay);
    const isMounted = useIsMounted();

    return useEvent((...args: any[]) =>
        debouncedCallback(() => {
            if (isMounted.current || unsafe) {
                callbackRef.current?.(...args);
            }
        }),
    ) as T;
}
