import * as ImagePicker from 'expo-image-picker';
import { ImagePickerOptions } from 'expo-image-picker';
import { useCallback, useMemo, useState } from 'react';
import { ImageURISource } from 'react-native';
import { UploadFile } from '@bookr-technologies/api/types/UploadFile';
import { useValueRef } from '~/hooks/useValueRef';

interface UseImagePickerReturnType {
    fileName: string;
    image: ImageURISource | null;
    loading: boolean;

    asBlob(): Promise<Blob | null>;

    asFile(): Promise<File | null>;

    asUploadFile(): UploadFile | null;

    pick(): Promise<void>;
}

export function useImagePicker(options?: ImagePickerOptions): UseImagePickerReturnType {
    const [loading, setLoading] = useState(false);
    const [image, setImage] = useState<ImageURISource | null>(null);
    const fileName = useMemo(() => image?.uri?.split('/')?.pop() || 'image.png', [image?.uri]);

    const imageRef = useValueRef(image);
    const fileNameRef = useValueRef<string>(fileName);

    const pick = useCallback(async () => {
        try {
            setLoading(true);
            const result = await ImagePicker.launchImageLibraryAsync({
                allowsEditing: true,
                aspect: [4, 3],
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                quality: 1,
                ...options,
            });

            if (!result.canceled && result.assets?.length > 0) {
                setImage({ uri: result.assets[0].uri });
            }
        } catch (e) {
            setImage(null);
        } finally {
            setLoading(false);
        }
    }, [options]);

    const asBlob = useCallback(async () => {
        if (!image?.uri) {
            return null;
        }

        const picture = await fetch(image.uri);
        return picture.blob();
    }, [image]);

    const asFile = useCallback(async () => {
        const blob = await asBlob();
        if (!blob) {
            return null;
        }

        return new File([blob], fileName || `image.${blob.type.split('/')[1]}`, { type: blob?.type });
    }, [asBlob, fileName]);

    const asUploadFile = useCallback<() => UploadFile | null>(() => {
        const image = imageRef.current;
        const fileName = fileNameRef.current;

        if (!image) {
            return null;
        }

        let mimeType = `image/${fileName?.split('.')?.pop()}`;
        if (mimeType === 'image/jpg') {
            mimeType = 'image/jpeg';
        }

        return {
            type: mimeType,
            name: fileName || '',
            uri: `${image.uri}`,
        };
    }, [fileNameRef, imageRef]);

    return {
        asBlob,
        asFile,
        asUploadFile,
        fileName,
        image,
        loading,
        pick,
    };
}
