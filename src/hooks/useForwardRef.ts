import { ForwardedRef, RefObject, useRef } from 'react';
import { useEvent } from '~/hooks/useEvent';

export function useForwardRef<T>(forwardedRef: ForwardedRef<T>): [ForwardedRef<T>, RefObject<T>] {
    const nextRef = useRef<T | null>(null);

    const forwardRef = useEvent((ref) => {
        if (typeof forwardedRef === 'function') {
            forwardedRef(ref);
        } else if (forwardedRef) {
            forwardedRef.current = ref;
        }

        nextRef.current = ref;
    });

    return [forwardRef, nextRef];
}
