import { Dimensions, useWindowDimensions } from 'react-native';

export function getContainerPadding(options?: { disabled?: boolean; width?: number }): number {
    const width = options?.width ?? Dimensions.get('window').width;

    if (options?.disabled) {
        return 0;
    }

    if (width < 400) {
        return 2;
    }

    return 3;
}

export function useContainerPadding(disabled?: boolean): number {
    const { width } = useWindowDimensions();

    return getContainerPadding({ width, disabled });
}
