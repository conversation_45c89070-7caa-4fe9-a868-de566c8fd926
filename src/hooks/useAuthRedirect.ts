/* eslint-disable @typescript-eslint/no-explicit-any */
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { User } from 'firebase/auth';
import moment from 'moment';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SubscriptionModel, SubscriptionStatus } from '@bookr-technologies/api/models/SubscriptionModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { RootStackParamList } from '~/RoutesParams';
import { useLogger } from '~/hooks/useLogger';
import { useValueRef } from '~/hooks/useValueRef';
import { setAnalyticsUser } from '~/lib/analytics/analytics';
import { isStaffMember } from '~/lib/utils/appointments';
import { getCurrentRoute, setRoot } from '~/lib/utils/navigation';
import { useApplicationStore } from '~/store/useApplicationStore';
import { ResolveUserResponse, useAuthStore } from '~/store/useAuthStore';
import { useBusinessStore } from '~/store/useBusinessStore';

async function canSendNotifications(user: UserModel | null, skipPushNotifications: boolean): Promise<boolean> {
    if (skipPushNotifications) {
        return true;
    }

    const notificationPermissions = await Notifications.getPermissionsAsync();
    if (!notificationPermissions.granted) {
        return false;
    }

    if (!Device.isDevice) {
        return true;
    }

    // TODO: add backend support to check if the user has push token
    return true;
}

async function detectNextScreen({
    skipPushNotifications,
    user,
    metadata,
    subscription,
    visitedIntro,
    creationTime,
}: ResolveUserResponse & {
    creationTime: string;
    skipPushNotifications: boolean;
    subscription: SubscriptionModel | null;
    visitedIntro: boolean;
}): Promise<[screen: keyof RootStackParamList, params?: RootStackParamList[keyof RootStackParamList]]> {
    if (!metadata?.verified) {
        return ['AccountVerificationScreen'];
    }

    if (user?.accountType === AccountType.Client || user?.accountType === AccountType.Invalid) {
        try {
            const data = await businessEndpoint.amIAnEmployee();
            if (data && data.businessId) {
                return ['JoinBusinessScreen'];
            }
        } catch (ignored) {
            // ignore, user not logged in
        }
    }

    if (user?.accountType === AccountType.Invalid) {
        return ['ChooseAccountTypeScreen'];
    }

    if (user?.accountType === AccountType.BusinessOwner) {
        if (!visitedIntro) {
            return ['IntroScreen'];
        }

        if (!user?.business?.id) {
            return ['SignUpBusinessDetailsScreen'];
        }

        if (subscription?.commissionPastDue) {
            return ['UnpaidBillScreen'];
        }

        if (subscription?.shouldSelectSubscriptionPlan && subscription?.status !== SubscriptionStatus.Active) {
            return ['ChooseSubscriptionPlanScreen'];
        }

        const skipSubscription = useApplicationStore.getState().skipChooseSubscriptionPlan;
        const canSkipSubscription = skipSubscription && moment(skipSubscription).diff(moment(), 'days') <= 3;

        if (subscription?.shouldSelectSubscriptionPlan && !canSkipSubscription) {
            return ['ChooseSubscriptionPlanScreen'];
        }
    }

    const areNotificationsAllowed = await canSendNotifications(user, skipPushNotifications);
    if (!areNotificationsAllowed) {
        return ['NotificationPreferenceScreen'];
    }

    const reviewQueue = useApplicationStore.getState().appointmentsForReview;
    if (reviewQueue.length > 0) {
        const now = moment();
        const nextForReview = reviewQueue.find((a) =>
            now.isAfter(moment(a.dateTime).add(a.service.duration, 'minutes')),
        );
        if (nextForReview) {
            return ['ReviewScreen', { appointmentId: nextForReview.id }];
        }
    }

    if (Math.floor((new Date().getTime() - Date.parse(creationTime)) / 1000 / 60 / 60 / 24) > 1) {
        if (!user?.dateOfBirth || !user?.country || !user?.city || !user?.interests) {
            return ['SubmitMissingUserDetailsScreen'];
        }
    }

    if (isStaffMember(user?.accountType)) {
        return ['BusinessApplication'];
    }

    return ['ClientApplication'];
}

export function useAuthRedirect(): (firebaseUser: User | null) => Promise<void> {
    const log = useLogger('useAuthRedirect');

    const isGuest = useAuthStore((state) => state.isGuest);
    const setIsGuest = useAuthStore((state) => state.setIsGuest);
    const setAuthenticated = useAuthStore((state) => state.setAuthenticated);
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const resolveSubscription = useAuthStore((state) => state.resolveSubscription);
    const logout = useAuthStore((state) => state.logout);
    const visitedIntro = useApplicationStore((state) => state.visitedIntro);
    const skipPushNotifications = useApplicationStore((state) => state.skipPushNotifications);

    const visitedIntroRef = useValueRef(visitedIntro);

    return async (firebaseUser: User | null) => {
        log.log('redirectAuthCallback', {
            uid: firebaseUser?.uid ?? 'no user',
        });

        setAnalyticsUser(firebaseUser);

        if (firebaseUser) {
            setIsGuest(false);
            setAuthenticated(null);
            const { metadata, user } = await resolveUser();
            const subscription = await resolveSubscription();
            if (user) {
                const [[screen, params], business] = await Promise.all([
                    detectNextScreen({
                        metadata,
                        skipPushNotifications,
                        subscription,
                        user,
                        visitedIntro: !!visitedIntroRef.current,
                        creationTime: firebaseUser.metadata.creationTime ?? '',
                    }),
                    user.business?.id ? businessEndpoint.show(user.business?.id) : null,
                ]);

                useBusinessStore.getState().setCurrentBusiness(business);
                if (
                    !['SignInScreen', 'SignUpScreen', 'MainScreen', 'ChooseSubscriptionPlanScreen'].includes(
                        getCurrentRoute() ?? '',
                    )
                ) {
                    log.log(
                        "The user is not on the MainScreen or SignIn/Up screens, don't redirect... Probably it was a deeplink (via push notification)",
                    );
                    return;
                }
                setRoot(screen, params);
                return;
            }
        } else if (isGuest) {
            setRoot('ClientApplication');
            return;
        }

        await setAnalyticsUser(null);
        await logout();
        setRoot('SignInScreen');
    };
}
