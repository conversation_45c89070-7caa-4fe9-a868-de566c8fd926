import { useEffect } from 'react';
import { DeviceEventEmitter } from 'react-native';
import { DeviceEvents, DeviceEventsEnum } from '~/constants/DeviceEventsEnum';
import { useEvent } from './useEvent';

export function useDeviceEvent<E extends DeviceEventsEnum, D = DeviceEvents[E]>(
    eventName: E,
    callback: (data: D) => void,
) {
    const callbackEvent = useEvent((data: D) => {
        callback(data);
    });

    useEffect(() => {
        const subscription = DeviceEventEmitter.addListener(eventName, callbackEvent);

        return () => subscription.remove();
    }, [callbackEvent, eventName]);
}
