import { ReactElement, useState } from 'react';
import { RefreshControl } from 'react-native';
import { Optional } from '@bookr-technologies/core/types';
import { useEvent } from './useEvent';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type RefreshCallback<T = any> = () => Promise<T>;

export function useRefreshControl<T extends RefreshCallback>(callback: Optional<T>): ReactElement {
    const [refreshing, setRefreshing] = useState(false);

    const handleRefresh = useEvent(async () => {
        setRefreshing(true);
        if (callback) {
            await callback();
        }
        setRefreshing(false);
    });

    return <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />;
}
