import { useCallback, useState } from 'react';
import { GestureResponderEvent, PressableProps } from 'react-native';

type Props = Pick<PressableProps, 'onPressIn' | 'onPressOut' | 'disabled'>;
export function usePressable(props?: Props): [isPressed: boolean, pressableProps: Props] {
    const [isPressed, setIsPressed] = useState(false);
    const { onPressIn, onPressOut } = props || {};

    const handlePressIn = useCallback(
        (e: GestureResponderEvent) => {
            if (onPressIn) {
                onPressIn(e);
            }

            setIsPressed(true);
        },
        [onPressIn],
    );

    const handlePressOut = useCallback(
        (e: GestureResponderEvent) => {
            if (onPressOut) {
                onPressOut(e);
            }

            setIsPressed(false);
        },
        [onPressOut],
    );

    return [isPressed, { onPressIn: handlePressIn, onPressOut: handlePressOut }];
}
