import { AxiosError } from 'axios';
import { useQuery, UseQueryOptions, UseQueryResult } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { Optional } from '@bookr-technologies/core/types';
import { reactQueryClient } from '~/lib/clients/reactQuery';
import { firstOf } from '~/lib/utils/object';

export type UseBusinessData = Optional<Partial<BusinessModel>>;
export type UseBusinessOptions = Omit<Options, 'queryKey' | 'queryFn'>;
export type UseBusinessResult = UseQueryResult<UseBusinessData, AxiosError>;

type Options = UseQueryOptions<UseBusinessData, AxiosError>;

export function getBusinessKey(businessId: Optional<string>): Array<Optional<string>> {
    return ['business', businessId];
}

export function useBusiness(
    business: Optional<string> | Pick<BusinessModel, 'id'>,
    options: Omit<UseBusinessOptions, 'queryKey' | 'queryFn'> = {},
): UseBusinessResult {
    const businessId = typeof business === 'object' ? business?.id : business;
    const queryKey = getBusinessKey(businessId);

    const options$: Options = options;
    options$.queryKey = queryKey;
    options$.queryFn = (): Promise<UseBusinessData> => businessEndpoint.show(businessId ?? '');
    options$.enabled = !!businessId;

    options$.initialData = firstOf(
        options$.initialData,
        (): UseBusinessData =>
            reactQueryClient.getQueryData(queryKey) ?? (business && typeof business === 'object' ? business : {}),
    );
    options$.retry = (failureCount, error) => {
        if (error.response?.status === 404) {
            return false;
        }
        return failureCount < 3;
    };

    return useQuery(options$);
}
