import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Asset } from 'expo-asset';
import * as Font from 'expo-font';
import { useEffect, useState } from 'react';
import { Image } from 'react-native';

async function cacheImages(images: Array<string | number>): Promise<Array<boolean | Asset>> {
    return Promise.all(
        images.map((image) => {
            if (typeof image === 'string') {
                return Image.prefetch(image);
            }

            return Asset.fromModule(image).downloadAsync();
        }),
    );
}

export function useCachedResources(): boolean {
    const [isLoadingComplete, setLoadingComplete] = useState(false);

    // Load any resources or data that we need prior to rendering the app
    useEffect(() => {
        (async (): Promise<void> => {
            try {
                await Font.loadAsync({
                    ...MaterialIcons.font,
                    ...MaterialCommunityIcons.font,
                    'PlusJakartaSans-Bold': require('../assets/fonts/PlusJakartaSans/ExtraBold.ttf'),
                    'PlusJakartaSans-Medium': require('../assets/fonts/PlusJakartaSans/SemiBold.ttf'),
                    'PlusJakartaSans-Regular': require('../assets/fonts/PlusJakartaSans/Medium.ttf'),
                    'PlusJakartaSans-SemiBold': require('../assets/fonts/PlusJakartaSans/Bold.ttf'),
                });

                // Load images
                await cacheImages([
                    // complete the image list here
                ]);
            } catch (e) {
                // We might want to provide this error information to an error reporting service
                console.warn(e);
            } finally {
                setLoadingComplete(true);
            }
        })();
    }, []);

    return isLoadingComplete;
}
