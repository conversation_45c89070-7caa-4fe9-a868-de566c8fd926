import { useCallback } from 'react';
import { NotificationItem } from '~/lib/notifications/NotificationItem';
import { NotificationsService } from '~/lib/notifications/NotificationService';
import { NotificationTypeEnum } from '~/lib/notifications/NotificationTypeEnum';
import { useNotificationsStore } from '~/store/useNotificationsStore';

export function useNotifications() {
    const notifications = useNotificationsStore((state) => state.queue);

    const notify = useCallback(
        (type: NotificationTypeEnum, message: string, title = '') => NotificationsService.notify(type, message, title),
        [],
    );

    const close = useCallback(
        (notification: string | NotificationItem) => NotificationsService.close(notification),
        [],
    );

    const info = useCallback((message: string, title = '') => NotificationsService.info(message, title), []);

    const success = useCallback((message: string, title = '') => NotificationsService.success(message, title), []);

    const warning = useCallback((message: string, title = '') => NotificationsService.warning(message, title), []);

    const error = useCallback((message: string, title = '') => NotificationsService.error(message, title), []);

    return {
        close,
        error,
        info,
        notifications,
        notify,
        success,
        warning,
    };
}
