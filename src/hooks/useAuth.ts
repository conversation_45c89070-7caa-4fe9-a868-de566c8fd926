import { UserMetadataModel } from '@bookr-technologies/api/models/UserMetadataModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useEvent } from '~/hooks/useEvent';
import { FirebaseUser, signInWithCustomToken, signInWithEmailAndPassword } from '~/lib/firebase/auth';
import { useAuthStore } from '~/store/useAuthStore';

export function useAuth(): {
    authenticated: boolean | null;
    firebaseUser: FirebaseUser | null;
    metadata: UserMetadataModel | null;
    signIn: typeof signInWithEmailAndPassword;
    signInWithToken: typeof signInWithCustomToken;
    user: UserModel | null;
} {
    const authenticated = useAuthStore((state) => state.authenticated);
    const user = useAuthStore((state) => state.user);
    const firebaseUser = useAuthStore((state) => state.firebaseUser);
    const metadata = useAuthStore((state) => state.metadata);

    const signIn = useEvent(async (email: string, password: string) => signInWithEmailAndPassword(email, password));
    const signInWithToken = useEvent(async (token: string) => signInWithCustomToken(token));

    return {
        authenticated,
        firebaseUser,
        metadata,
        signIn,
        signInWithToken,
        user,
    };
}
