{"v": "4.8.0", "meta": {"g": "LottieFiles AE ", "a": "", "k": "", "d": "", "tc": ""}, "fr": 20, "ip": 0, "op": 20, "w": 100, "h": 100, "nm": "<PERSON><PERSON>", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "shape - 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 41, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [49.606, 49.877, 0], "ix": 2}, "a": {"a": 0, "k": [12.5, 11.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.27, 0.27, 0.27], "y": [1.619, 1.619, 1]}, "o": {"x": [0.68, 0.68, 0.68], "y": [-1.164, -1.164, 0]}, "t": 2, "s": [130, 130, 100]}, {"i": {"x": [0.357, 0.357, 0.27], "y": [3.154, 3.154, 1]}, "o": {"x": [0.485, 0.485, 0.68], "y": [-0.948, -0.948, 0]}, "t": 5, "s": [90, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 11, "s": [130, 130, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 38, "s": [130, 130, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 41, "s": [90, 90, 100]}, {"t": 46, "s": [130, 130, 100]}], "ix": 6, "x": "var $bm_rt;\nvar $bm_rt;\nvar bla, bla, t, t, v, amp, freq, decay;\n$bm_rt = $bm_rt = bla = 0;\nif (numKeys > 0) {\n    $bm_rt = $bm_rt = bla = nearestKey(time).index;\n    if (key(bla).time > time) {\n        bla--;\n    }\n}\nif (bla == 0) {\n    $bm_rt = $bm_rt = t = 0;\n} else {\n    $bm_rt = $bm_rt = t = sub(time, key(bla).time);\n}\nif (bla > 0) {\n    v = velocityAtTime(sub(key(bla).time, div(thisComp.frameDuration, 10)));\n    amp = 2;\n    freq = 5;\n    decay = 5;\n    $bm_rt = $bm_rt = add(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.575, 0], [1.215, -1.544], [1.986, 0], [0, -3.68], [-0.837, -1.235], [-0.26, -0.188], [-0.497, 0.364], [-2.435, 3.595], [0, 2.196]], "o": [[-1.984, 0], [-1.215, -1.544], [-3.578, 0], [0, 2.196], [2.434, 3.595], [0.496, 0.364], [0.26, -0.188], [0.839, -1.238], [-0.003, -3.68]], "v": [[5.043, -10.133], [-0.001, -7.652], [-5.045, -10.133], [-11.531, -3.46], [-9.652, 1.513], [-0.835, 9.769], [0.833, 9.769], [9.65, 1.513], [11.531, -3.46]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.2, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.781, 11.633], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": 3, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "shape - 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [12.5, 11.5, 0], "ix": 2}, "a": {"a": 0, "k": [12.5, 11.5, 0], "ix": 1}, "s": {"a": 0, "k": [107.692, 107.692, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.575, 0], [1.215, -1.544], [1.986, 0], [0, -3.68], [-0.837, -1.235], [-0.26, -0.188], [-0.497, 0.364], [-2.435, 3.595], [0, 2.196]], "o": [[-1.984, 0], [-1.215, -1.544], [-3.578, 0], [0, 2.196], [2.434, 3.595], [0.496, 0.364], [0.26, -0.188], [0.839, -1.238], [-0.003, -3.68]], "v": [[5.043, -10.133], [-0.001, -7.652], [-5.045, -10.133], [-11.531, -3.46], [-9.652, 1.513], [-0.835, 9.769], [0.833, 9.769], [9.65, 1.513], [11.531, -3.46]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.9411764705882353, 0.3215686274509804, 0.3254901960784314, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.781, 11.633], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 5, "op": 20, "st": 3, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shape - 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [15, 15, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -181.38], [0, -1]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.637], "y": [0.325]}, "o": {"x": [0.397], "y": [0]}, "t": 2, "s": [100]}, {"i": {"x": [0.551], "y": [1]}, "o": {"x": [0.247], "y": [0.784]}, "t": 7, "s": [46.882]}, {"t": 11, "s": [22]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.454], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [100]}, {"t": 10, "s": [22]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.9411764705882353, 0.3215686274509804, 0.3254901960784314, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "rp", "c": {"a": 0, "k": 6, "ix": 1}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1, "ix": 4, "tr": {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 72, "ix": 4}, "so": {"a": 0, "k": 100, "ix": 5}, "eo": {"a": 0, "k": 100, "ix": 6}, "nm": "Transform"}, "nm": "Repeater 1", "mn": "ADBE Vector Filter - Repeater", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"t": 10, "s": [211, 211], "h": 1}, {"t": 11, "s": [0, 0], "h": 1}], "ix": 3}, "r": {"a": 0, "k": 180, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 56.073], "ix": 2}, "a": {"a": 0, "k": [0, 56.073], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Front", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 12, "op": 12, "st": 0, "bm": 0, "hidden": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "shape - 4", "sr": 1, "ks": {"o": {"a": 0, "k": 10, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [15, 15, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.353, 0.353], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 3, "s": [0, 0]}, {"t": 16, "s": [329, 329]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.217, 0.217], "y": [1, 1]}, "o": {"x": [0.163, 0.163], "y": [0, 0]}, "t": 9, "s": [0, 0]}, {"t": 17, "s": [331, 331]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 2", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.921568627451, 0.121568627451, 0.290196078431, 1], "ix": 4, "x": "var $bm_rt;\nvar $bm_rt;\ntry {\n    $bm_rt = $bm_rt = thisComp.layer('Controller').effect('Color')('ADBE Color Control-0001');\n} catch (e) {\n    $bm_rt = $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [199, 199], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Front", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 16.6669921875, "op": 16.6669921875, "st": 3, "bm": 0, "hidden": 9}], "markers": []}