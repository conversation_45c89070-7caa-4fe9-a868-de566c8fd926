import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import * as SplashScreen from 'expo-splash-screen';
import { ReactElement, useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { initialWindowMetrics, SafeAreaProvider } from 'react-native-safe-area-context';
import { MaintenanceScreen } from '~/app/common/screens/MaintenanceScreen';
import { useAuthReady } from '~/hooks/useAuthReady';
import { useCachedResources } from '~/hooks/useCachedResources';
import { useInitializer } from '~/hooks/useInitializer';
import {
    initializeFirebaseRemoteConfig,
    isRemoteConfigParameterEnabled,
    RemoteConfigParameters,
} from '~/lib/firebase/remoteConfig';
import { Routes } from './Routes';
import { LoadingView } from './components/LoadingView';
import { ReactQueryProvider } from './components/ReactQueryProvider';
import { ThemeProvider } from './components/ui/ThemeProvider';

SplashScreen.preventAutoHideAsync();

export function Application(): ReactElement | null {
    const isResourcesReady = useCachedResources();
    const isAuthReady = useAuthReady();
    const [isRemoteConfigReady] = useInitializer(initializeFirebaseRemoteConfig);

    // Decide if the App is ready to render the app
    const isAppReady = isResourcesReady && isRemoteConfigReady;

    // Decide if the App loading is ready;
    const isReady = isAppReady && isAuthReady;

    useEffect(() => {
        if (isAppReady) {
            SplashScreen.hideAsync();
        }
    }, [isAppReady]);

    if (!isAppReady) {
        return null;
    }

    if (isRemoteConfigParameterEnabled(RemoteConfigParameters.MobileMaintenanceEnabled)) {
        return (
            <SafeAreaProvider initialMetrics={initialWindowMetrics}>
                <ThemeProvider>
                    <MaintenanceScreen />
                </ThemeProvider>
            </SafeAreaProvider>
        );
    }

    if (!isReady) {
        return (
            <ThemeProvider>
                <LoadingView />
            </ThemeProvider>
        );
    }

    return (
        <SafeAreaProvider initialMetrics={initialWindowMetrics}>
            <ThemeProvider>
                <ReactQueryProvider>
                    <GestureHandlerRootView style={{ flex: 1 }}>
                        <BottomSheetModalProvider>
                            <Routes />
                        </BottomSheetModalProvider>
                    </GestureHandlerRootView>
                </ReactQueryProvider>
            </ThemeProvider>
        </SafeAreaProvider>
    );
}

export default Application;
