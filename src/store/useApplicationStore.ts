import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { virtualTourEndpoint } from '@bookr-technologies/api/endpoints/virtualTourEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { ObjectProperties } from '~/types/ObjectProperties';

export interface ApplicationStoreType {
    appointmentsForReview: AppointmentModel[];
    bottomScreenOffset: number;
    latestBusinessesVisited: SearchBusinessModel[];
    notificationsLastChecked: string | null;
    skipChooseSubscriptionPlan: Date | null;
    skipProfilePicture: boolean;
    skipPushNotifications: boolean;
    virtualTourRequest: Date | null;
    visitedIntro: boolean;

    addLatestBusinessVisited(business: SearchBusinessModel): void;

    deleteFromAppointmentsForReview(appointmentId: number): void;

    pushAppointmentsForReview(appointment: AppointmentModel): void;

    setBottomScreenOffset(bottomScreenOffset: number): void;

    setLatestBusinessesVisited(latestBusinessesVisited: SearchBusinessModel[]): void;

    setNotificationsLastChecked(notificationsLastChecked: string | null): void;

    setSkipChooseSubscriptionPlan(): void;

    setSkipPushNotifications(skipPushNotifications: boolean): void;

    setVisitedIntro(visitedIntro: boolean): void;

    requestVirtualTour(businessId: string): Promise<void>;
}

const initialState: ObjectProperties<ApplicationStoreType> = {
    bottomScreenOffset: 0,
    latestBusinessesVisited: [],
    notificationsLastChecked: null,
    appointmentsForReview: [],
    skipChooseSubscriptionPlan: null,
    skipProfilePicture: false,
    skipPushNotifications: false,
    visitedIntro: false,
    virtualTourRequest: null,
};

export const useApplicationStore = create(
    persist<ApplicationStoreType>(
        (set, get) => ({
            ...initialState,

            addLatestBusinessVisited(business: SearchBusinessModel): void {
                let latestBusinessesVisited = get().latestBusinessesVisited;
                if (latestBusinessesVisited.find((latestBusiness) => latestBusiness.id === business.id)) {
                    return;
                }
                if (latestBusinessesVisited.length >= 10) {
                    latestBusinessesVisited.pop();
                }

                latestBusinessesVisited = [business, ...latestBusinessesVisited];

                set({ latestBusinessesVisited });
            },

            deleteFromAppointmentsForReview(appointmentId: number): void {
                const appointmentsForReview = get().appointmentsForReview;
                const index = appointmentsForReview.findIndex((a) => a.id === appointmentId);
                if (index === -1) {
                    return;
                }
                appointmentsForReview.splice(index, 1);
                set({ appointmentsForReview });
            },
            pushAppointmentsForReview(appointment: AppointmentModel): void {
                const appointmentsForReview = get().appointmentsForReview;
                appointmentsForReview.push(appointment);
                set({ appointmentsForReview });
            },
            setBottomScreenOffset(bottomScreenOffset): void {
                set({ bottomScreenOffset });
            },
            setLatestBusinessesVisited(latestBusinessesVisited: SearchBusinessModel[]): void {
                set({ latestBusinessesVisited });
            },
            setNotificationsLastChecked(notificationsLastChecked: string | null): void {
                set({ notificationsLastChecked });
            },
            setSkipChooseSubscriptionPlan(): void {
                set({
                    skipChooseSubscriptionPlan: new Date(),
                });
            },
            setSkipPushNotifications(skipPushNotifications): void {
                set({ skipPushNotifications });
            },
            setVisitedIntro(visitedIntro): void {
                set({ visitedIntro });
            },
            async requestVirtualTour(businessId: string): Promise<void> {
                const { virtualTourRequest } = get();

                if (virtualTourRequest && moment().diff(virtualTourRequest, 'day') <= 1) {
                    return;
                }

                set({ virtualTourRequest: new Date() });
                await virtualTourEndpoint.requestVirtualTour(businessId);
            },
        }),
        {
            storage: createJSONStorage(() => AsyncStorage),
            name: 'applicationStore',
        },
    ),
);
