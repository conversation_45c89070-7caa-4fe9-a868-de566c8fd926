import moment from 'moment-timezone';
import { create } from 'zustand';
import { WorkingDay } from '@bookr-technologies/api/constants/WorkingDay';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';
import { FallbackLocation } from '~/lib/map/googleMapsEndpoint';
import { ObjectProperties } from '~/types/ObjectProperties';

export type WorkingProgramRecord = Record<
    'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday',
    {
        enabled: boolean;
        values: Array<{ end: string; start: string }>;
    }
>;
export interface BusinessSignUpForm
    extends Pick<
        BusinessModel,
        'name' | 'phoneNumber' | 'formattedAddress' | 'latitude' | 'latitudeDelta' | 'longitude' | 'longitudeDelta'
    > {
    categories: [];
    email: string;
    workingProgram: WorkingProgramRecord;
}

export function workingProgramToWorkingHours(
    workingProgram: BusinessSignUpForm['workingProgram'],
): Array<Omit<WorkingHourModel, 'id' | 'lastUpdatedAt'>> {
    const workingHours = Object.entries(workingProgram)
        .map(([day, { enabled, values }]) => {
            if (!enabled) {
                return [];
            }

            return values.map(({ start, end }) => ({
                day: day.toUpperCase() as WorkingDay,
                end: moment(end, 'HH:mm').format('HH:mm'),
                start: moment(start, 'HH:mm').format('HH:mm'),
            }));
        })
        .flat(2);

    return WorkingHourModel.convertHoursToUTC(workingHours);
}

export function workingProgramFromWorkingHours(workingHours: WorkingHourModel[]): BusinessSignUpForm['workingProgram'] {
    workingHours = WorkingHourModel.convertHoursToLocalTimezone(workingHours);

    return Object.values(WorkingDay).reduce((acc, day) => {
        const values = workingHours.filter((hour) => hour.day.toLowerCase() === day.toLowerCase());

        return {
            ...acc,
            [day.toLowerCase()]: {
                enabled: values.length > 0,
                values,
            },
        };
    }, {} as WorkingProgramRecord);
}

export interface BusinessStoreType {
    currentBusiness: BusinessModel | null;
    signUpForm: BusinessSignUpForm;

    createBusiness(): Promise<BusinessModel>;

    patchSignUpForm(signUpForm: Partial<BusinessSignUpForm>): void;

    setCurrentBusiness(business: BusinessModel | null): void;

    setSignUpForm(signUpForm: BusinessSignUpForm): void;
}

const initialState: ObjectProperties<BusinessStoreType> = {
    currentBusiness: null,
    signUpForm: {
        categories: [],
        email: '',
        formattedAddress: '',
        latitude: FallbackLocation.latitude,
        latitudeDelta: FallbackLocation.latitudeDelta,
        longitude: FallbackLocation.longitude,
        longitudeDelta: FallbackLocation.longitudeDelta,
        name: '',
        phoneNumber: '',
        workingProgram: {
            friday: { enabled: true, values: [{ end: '18:00', start: '09:00' }] },
            monday: { enabled: true, values: [{ end: '18:00', start: '09:00' }] },
            saturday: { enabled: false, values: [{ end: '18:00', start: '09:00' }] },
            sunday: { enabled: false, values: [{ end: '18:00', start: '09:00' }] },
            thursday: { enabled: true, values: [{ end: '18:00', start: '09:00' }] },
            tuesday: { enabled: true, values: [{ end: '18:00', start: '09:00' }] },
            wednesday: { enabled: true, values: [{ end: '18:00', start: '09:00' }] },
        },
    },
};

export const useBusinessStore = create<BusinessStoreType>((set, get) => ({
    ...initialState,
    async createBusiness(): Promise<BusinessModel> {
        const { signUpForm } = get();

        const business = await businessEndpoint.create({
            categories: signUpForm.categories.map((name) => ({ name })),
            formattedAddress: signUpForm.formattedAddress,
            latitude: signUpForm.latitude,
            latitudeDelta: signUpForm.latitudeDelta,
            longitude: signUpForm.longitude,
            longitudeDelta: signUpForm.longitudeDelta,
            name: signUpForm.name,
            phoneNumber: signUpForm.phoneNumber,
            workingHours: workingProgramToWorkingHours(signUpForm.workingProgram),
            zoneId: moment.tz.guess(),
        });

        set({ signUpForm: initialState.signUpForm });

        return business;
    },
    patchSignUpForm(signUpForm): void {
        set((state) => ({
            ...state,
            signUpForm: {
                ...state.signUpForm,
                ...signUpForm,
            },
        }));
    },
    setCurrentBusiness(currentBusiness): void {
        set({ currentBusiness });
    },
    setSignUpForm(signUpForm): void {
        set({ signUpForm });
    },
}));
