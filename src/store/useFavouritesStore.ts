import { create } from 'zustand';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { createLogger } from '~/lib/logs/createLogger';
import { useAuthStore } from '~/store/useAuthStore';

export interface FavouritesStoreType {
    favourites: BusinessModel[];

    resolveFavourites(): Promise<void>;
}

const log = createLogger('useFavouritesStore');

export const useFavouritesStore = create<FavouritesStoreType>((set) => ({
    favourites: [],
    resolveFavourites: async () => {
        try {
            if (useAuthStore.getState().isGuest) {
                return set({ favourites: [] });
            }
            const favourites = await usersEndpoint.getFavourites();
            set({ favourites });
        } catch (error) {
            log.error('error retrieving favourites', { error });
        }
    },
}));
