import AsyncStorage from '@react-native-async-storage/async-storage';
import { AxiosError } from 'axios';
import type { ConfirmationResult, User } from 'firebase/auth';
import moment from 'moment';
import { create } from 'zustand';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { SubscriptionModel } from '@bookr-technologies/api/models/SubscriptionModel';
import { UserMetadataModel } from '@bookr-technologies/api/models/UserMetadataModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { delay } from '@bookr-technologies/core/promises';
import { reactQueryClient } from '~/lib/clients/reactQuery';
import { getFirebaseUser, signOut } from '~/lib/firebase/auth';
import { createLogger } from '~/lib/logs/createLogger';
import { changeLanguage } from '~/lib/translations/translations';

const log = createLogger('useAuthStore');

export interface ResolveUserResponse {
    metadata: UserMetadataModel | null;
    user: UserModel | null;
}

export interface AuthStoreType {
    authenticated: boolean | null;
    confirmationResult: ConfirmationResult | null;
    firebaseUser: User | null;
    isGuest: boolean;
    metadata: UserMetadataModel | null;
    subscription: SubscriptionModel | null;
    token: string | null;
    user: UserModel | null;

    applyUserPartialUpdate(data: Partial<UserModel>): UserModel | null;

    logout(): Promise<void>;

    resolveMetadata(): Promise<UserMetadataModel | null>;

    resolveUser(): Promise<ResolveUserResponse>;

    resolveSubscription(): Promise<SubscriptionModel | null>;

    setAuthenticated(authenticated: boolean | null): void;

    setConfirmationResult(confirmationResult: ConfirmationResult | null): void;

    setMetadata(metadata: UserMetadataModel | null): void;

    setToken(token: string | null): void;

    setUser(user: UserModel | null): void;

    updateUser(data: Partial<UserModel>): Promise<UserModel | null>;

    setIsGuest(isGuest: boolean): void;
}

export const useAuthStore = create<AuthStoreType>((set, get) => ({
    applyUserPartialUpdate(data) {
        const { user } = get();
        if (!user) {
            return null;
        }

        const newUser = { ...user, ...data };
        set({ user: newUser });

        return newUser;
    },
    isGuest: false,
    authenticated: null,
    confirmationResult: null,
    firebaseUser: null,
    async logout(): Promise<void> {
        log.log('Logging out');

        try {
            await signOut();
        } catch (e) {
            // Nothing to do
        }
        set({ authenticated: false, metadata: null, user: null });
        reactQueryClient.clear();
        await AsyncStorage.clear();
    },
    metadata: null,
    async resolveMetadata(targetEmail?: string) {
        try {
            const { user, firebaseUser } = get();
            const email = targetEmail || user?.email || firebaseUser?.email;
            if (!email) {
                return null;
            }

            const metadata = await usersEndpoint.getMetadata(email);
            set({ metadata });

            if (metadata.verified) {
                await AsyncStorage.setItem(`metadata:${email}`, JSON.stringify(metadata));
            }

            return metadata;
        } catch (e) {
            return null;
        }
    },
    async resolveUser() {
        try {
            if (get().isGuest) {
                return { metadata: null, subscription: null, user: null };
            }
            const firebaseUser = getFirebaseUser();
            const context = { uid: firebaseUser?.uid };

            log.log('resolveUser', context);
            const token = (await firebaseUser?.getIdToken()) ?? null;
            set({ firebaseUser, token });

            const uid = get().user?.uid || firebaseUser?.uid;
            if (!uid) {
                set({ authenticated: true, user: null });
                return { metadata: null, subscription: null, user: null };
            }

            let retries = 0;

            const retry = async (id: string): Promise<{ metadata: UserMetadataModel; user: UserModel }> => {
                retries++;

                try {
                    const user = await usersEndpoint.show(id);
                    // first check the cache
                    const metadataFromStorage = await AsyncStorage.getItem(`metadata:${user.email}`);
                    if (metadataFromStorage) {
                        const metadata = JSON.parse(metadataFromStorage) as UserMetadataModel;
                        // if the metadata is verified, we can use it
                        if (metadata?.verified) {
                            set({ authenticated: true, metadata, user, isGuest: false });
                            return { metadata, user };
                        }
                    }
                    const currentMetadata = get().metadata;

                    const metadata = currentMetadata?.verified
                        ? currentMetadata
                        : await usersEndpoint.getMetadata(user.email);

                    if (metadata.verified) {
                        await AsyncStorage.setItem(`metadata:${user.email}`, JSON.stringify(metadata));
                    }

                    set({ authenticated: true, metadata, user, isGuest: false });
                    return { metadata, user };
                } catch (e) {
                    await delay(300);

                    if ((e as AxiosError).response?.status === 404 && retries < 5) {
                        log.warn(`User not found, retrying (${retries})`, { ...context, e });
                        return retry(id);
                    } else {
                        throw e;
                    }
                }
            };

            const { user, metadata } = await retry(uid);

            const response: ResolveUserResponse = {
                metadata,
                user,
            };

            const language = user.language.split('-')[0];
            await changeLanguage(language);

            // Setting first day of the week (0 = Sunday, 1 = Monday, etc)
            moment.updateLocale(language, { week: { dow: 1 } });

            return response;
        } catch (e) {
            log.error('Fail to resolve user', { e });
            set({ authenticated: true, user: null });
            return { metadata: null, subscription: null, user: null };
        }
    },
    async resolveSubscription(): Promise<SubscriptionModel | null> {
        if (!get().user?.business?.id) {
            set({ subscription: null });
            return null;
        }

        const subscription = await businessEndpoint.getSubscription();
        log.log('resolveSubscription', { subscription });
        set({ subscription });

        return subscription;
    },
    setAuthenticated(authenticated) {
        return set({ authenticated });
    },
    setConfirmationResult(confirmationResult) {
        return set({ confirmationResult });
    },
    setMetadata(metadata) {
        return set({ metadata });
    },
    setToken(token) {
        return set({ token });
    },
    setUser(user) {
        return set({ user });
    },
    setIsGuest(isGuest) {
        return set({ isGuest });
    },
    subscription: null,
    token: null,
    async updateUser(data) {
        const { user } = get();
        if (!user) {
            return null;
        }

        const updatedUser = await usersEndpoint.update(user.uid, data);

        set({
            user: {
                ...user,
                ...updatedUser,
            },
        });

        return user;
    },
    user: null,
}));
