import { create } from 'zustand';
import { NotificationItem } from '~/lib/notifications/NotificationItem';

export interface NotificationsStoreType {
    queue: NotificationItem[];
    addNotification(notification: NotificationItem): void;
    removeNotification(notification: string | Pick<NotificationItem, 'key'>): void;
}

export const useNotificationsStore = create<NotificationsStoreType>((set, get) => ({
    addNotification(notification: NotificationItem) {
        const { queue } = get();
        const item = queue.find((item) => item.key === notification.key);

        if (!item) {
            set({
                queue: [...queue, notification],
            });
        }
    },
    queue: [],
    removeNotification(notification) {
        const { queue } = get();
        const key = typeof notification === 'string' ? notification : notification.key;

        set({
            queue: queue.filter((notification) => notification.key !== key),
        });
    },
}));
