import { BottomTabNavigationProp, BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp, CompositeScreenProps, NavigatorScreenParams } from '@react-navigation/native';
import type { NativeStackNavigationProp, NativeStackScreenProps } from '@react-navigation/native-stack';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';

export type BusinessApplicationSettingsParamList = {
    SettingsAvailabilityScreen: undefined;

    SettingsMyInfoScreen: undefined;

    SettingsOnlinePaymentsSetupScreen: undefined;
    SettingsPaymentsBillingScreen: undefined;
    SettingsPaymentsBuyMessagesScreen: undefined;
    SettingsPaymentsMessagesConfigureNotificationTypesScreen: undefined;
    SettingsPaymentsMessagesConfigureStaffScreen: undefined;
    SettingsPaymentsMessagesScreen: undefined;
    SettingsPaymentsMessagesStoryScreen: undefined;
    SettingsPaymentsScreen: undefined;
    SettingsPaymentsSubscriptionScreen: undefined;

    SettingsProfileCategoriesScreen: undefined;
    SettingsProfileGalleryScreen?: {
        isEmployee?: boolean;
    };
    SettingsProfileInfoScreen: undefined;
    SettingsProfileLocationScreen: undefined;
    SettingsProfileNewMemberScreen: undefined;
    SettingsProfileScreen: undefined;
    SettingsProfileStaffMembersScreen: undefined;
    SettingsProfileWorkingProgramScreen?: {
        isEmployee?: boolean;
    };
    SettingsPromoLinkScreen: undefined;

    SettingsServiceScreen?: {
        comingFromCreateAppointment?: boolean;
        service?: ServiceModel;
    };
    SettingsServicesScreen: undefined;

    SettingsVirtual3DTourScreen: undefined;
};

export type BusinessApplicationParamList = {
    AppointmentsScreen: {
        actionAfterLoad?: () => void;
    };
    ClientDetailsAppointmentsScreen: { appointments: AppointmentModel[]; client: UserModel };
    ClientDetailsScreen: { clientId: string };
    ClientsScreen: undefined;
    PerformanceScreen: undefined;
    SendNotificationComposeScreen: {
        allSelected?: boolean;
        excludedUserIds?: string[];
        totalClients?: number;
        userIds?: string[];
    };
    SendNotificationConfirmationScreen: {
        allSelected?: boolean;
        excludedUserIds?: string[];
        message: string;
        totalClients?: number;
        userIds: string[];
    };
    SendNotificationSelectClientsScreen: undefined;
    SettingsScreen: undefined;
};

export type ClientApplicationParamList = {
    ProfileSettings: undefined;
};

export type RootStackParamList = {
    AccountVerificationScreen: {
        email?: string;
    };
    AppointmentChoosePaymentScreen: {
        addToCalendar?: (appointment: AppointmentModel) => Promise<void>;
        appointments: AppointmentModel[];
        isEdit?: boolean;
        onClose?: () => void;
        onGoToAppointment?: (appointment: AppointmentModel) => void;
        showAddToCalendar?: boolean;
    };
    AppointmentConfirmationScreen: RootStackParamList['AppointmentChoosePaymentScreen'];
    AppointmentDetailsScreen: {
        appointment?: AppointmentModel;
        appointmentId?: number;
        businessView?: boolean;
        client?: UserModel;
    };
    BookNowScreen: {
        business: BusinessModel;
        service: ServiceModel;
        staff: UserModel;
    };
    BookNowSessionsScreen: RootStackParamList['BookNowScreen'];
    BusinessApplication: NavigatorScreenParams<BusinessApplicationParamList>;
    BusinessDetailsScreen: { businessId: string };
    BusinessLocationScreen: {
        business: BusinessModel;
    };
    BusinessProfileScreen: {
        businessId: string;
    };
    BusinessReviewsDetailsScreen: {
        businessDisplayName: string;
        businessId: string;
        businessImageURL: string;
        reviewsInfo: { averageRating: number; noOfReviews: number };
    };
    BusinessSettingsStack: NavigatorScreenParams<BusinessApplicationSettingsParamList>;
    BusinessStaffScreen: { staff: UserModel };
    ChangePasswordScreen: undefined;
    ChooseAccountTypeScreen: undefined;
    ChooseSubscriptionPlanScreen: {
        comingFromCreateBusiness?: boolean;
    };
    ClientApplication: NavigatorScreenParams<ClientApplicationParamList>;
    CreateAppointmentScreen?: Partial<RootStackParamList['SelectEventTypeScreen']> & {
        appointment?: Partial<AppointmentModel>;
    };
    CreateBreakScreen?: RootStackParamList['SelectEventTypeScreen'];
    EditAppointmentScreen: {
        appointment: AppointmentModel;
    };
    EditProfileScreen: undefined;
    FeedbackScreen: undefined;
    ImportClientScreen: undefined;
    IntroScreen: undefined;
    JoinBusinessScreen: undefined;
    MainScreen: undefined;
    NotificationPreferenceScreen: undefined;
    NotificationsScreen: undefined;
    ProfilePicturePickerScreen: {
        accountType?: AccountType;
        createBusiness?: boolean;
        forBusiness?: boolean;
        navigateBack?: boolean;
    };
    ReviewScreen: {
        appointmentId: number;
    };
    SearchBusinessesCategoryScreen: { category?: string; city: string; latLng: string };
    SearchMap: undefined;
    SearchMapScreen: { category?: string; city: string; latLng: string };
    SearchScreen?: { category?: string; focus?: boolean };
    SelectEventTypeScreen: {
        date?: string;
        staffMemberId: string;
    };
    SignInScreen: {
        showGuestOption?: boolean;
    };
    SignUpBusinessCategoriesScreen: undefined;
    SignUpBusinessDetailsScreen: undefined;
    SignUpBusinessLocationScreen: undefined;
    SignUpBusinessProgramScreen: undefined;
    SignUpScreen: {
        email?: string;
    };
    SubmitMissingUserDetailsScreen: undefined;
    SubscriptionPlansScreen: undefined;
    UnpaidBillScreen: undefined;
    UpdateAppointmentScreen: Record<string, unknown>;
    WaitingListScreen: {
        waitingList: UserModel[];
    };
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<RootStackParamList, T>;
export type RootStackNavigationProp<T extends keyof RootStackParamList> = NativeStackNavigationProp<
    RootStackParamList,
    T
>;

export type BusinessApplicationTabScreenProps<T extends keyof BusinessApplicationParamList> = CompositeScreenProps<
    BottomTabScreenProps<BusinessApplicationParamList, T>,
    RootStackScreenProps<keyof RootStackParamList>
>;

export type RouteKeys = keyof RootStackParamList;

export type BusinessApplicationNavigationProp<T extends keyof BusinessApplicationParamList> = CompositeNavigationProp<
    BottomTabNavigationProp<BusinessApplicationParamList, T>,
    RootStackNavigationProp<keyof RootStackParamList>
>;

export type BusinessApplicationSettingsStackNavigationProp<T extends keyof BusinessApplicationSettingsParamList> =
    CompositeNavigationProp<
        NativeStackNavigationProp<BusinessApplicationSettingsParamList, T>,
        RootStackNavigationProp<keyof RootStackParamList>
    >;

export type BusinessApplicationSettingsStackRouteProp<T extends keyof BusinessApplicationSettingsParamList> =
    CompositeScreenProps<
        NativeStackScreenProps<BusinessApplicationSettingsParamList, T>,
        RootStackScreenProps<keyof RootStackParamList>
    >;

export type ClientApplicationNavigationProp<T extends keyof ClientApplicationParamList> = CompositeNavigationProp<
    BottomTabNavigationProp<ClientApplicationParamList, T>,
    RootStackNavigationProp<keyof RootStackParamList>
>;
