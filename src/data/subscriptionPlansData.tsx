import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import freePlanBanner from '~/assets/plans/freePlanBanner.jpg';
import professionalPlanBanner from '~/assets/plans/professionalPlanBanner.jpg';
import standardPlanBanner from '~/assets/plans/standardPlanBanner.jpg';

const icons = {
    'event-note': <MaterialIcons name={'event-note'} />,
    group: <MaterialIcons name={'group'} />,
    history: <MaterialIcons name={'history'} />,
    'insert-chart-outlined': <MaterialIcons name={'insert-chart-outlined'} />,
    'insert-drive-file': <MaterialIcons name={'insert-drive-file'} />,
    leaderboard: <MaterialIcons name={'leaderboard'} />,
    map: <MaterialIcons name={'map'} />,
    'monetization-on': <MaterialIcons name={'monetization-on'} />,
    'notifications-active': <MaterialIcons name={'notifications-active'} />,
    'notifications-none': <MaterialIcons name={'notifications-none'} />,
    people: <MaterialIcons name={'people'} />,
    person: <MaterialIcons name={'person'} />,
    'person-add': <MaterialIcons name={'person-add'} />,
    'person-pin': <MaterialIcons name={'person-pin'} />,
    sms: <MaterialIcons name={'sms'} />,
    'stay-current-portrait': <MaterialIcons name={'stay-current-portrait'} />,
    store: <MaterialIcons name={'store'} />,
    'support-agent': <MaterialIcons name={'support-agent'} />,
    'view-list': <MaterialIcons name={'view-list'} />,
    web: <MaterialIcons name={'web'} />,
};

export const free = {
    banner: freePlanBanner,
    features: [
        {
            features: [
                {
                    disabled: false,
                    icon: icons['people'],
                    limited: false,
                    name: 'oneStaffMember',
                },
                {
                    disabled: false,
                    icon: icons['event-note'],
                    limited: false,
                    name: 'appointmentsLimit100',
                },
                { disabled: false, icon: icons['store'], limited: false, name: 'businessListing' },
                { disabled: false, icon: icons['web'], limited: false, name: 'yourPersonalWebsite' },
                { disabled: false, icon: icons['group'], limited: false, name: 'communityAccess' },
                { disabled: false, icon: icons['history'], limited: false, name: 'activityHistory' },

                { disabled: false, icon: icons['map'], limited: false, name: 'googleIntegration' },
                {
                    disabled: true,
                    icon: icons['person-pin'],
                    limited: false,
                    name: 'waitingList',
                },
            ],
            name: 'booking',
        },
        {
            features: [
                {
                    disabled: true,
                    icon: icons['monetization-on'],
                    limited: false,
                    name: 'salesReports',
                },
                {
                    disabled: true,
                    icon: icons['insert-chart-outlined'],
                    limited: false,
                    name: 'performanceIndicators',
                },
                {
                    disabled: true,
                    icon: icons['leaderboard'],
                    limited: false,
                    name: 'staffMembersTop',
                },
                {
                    disabled: false,
                    icon: icons['store'],
                    limited: true,
                    name: 'teamManagement',
                },
            ],
            name: 'businessManagement',
        },
        {
            features: [
                {
                    disabled: true,
                    icon: icons['person'],
                    limited: false,
                    name: 'clientHistory',
                },
                {
                    disabled: true,
                    icon: icons['person-add'],
                    limited: false,
                    name: 'clientImport',
                },
                {
                    disabled: true,
                    icon: icons['view-list'],
                    limited: false,
                    name: 'clientListing',
                },
                {
                    disabled: true,
                    icon: icons['insert-drive-file'],
                    limited: false,
                    name: 'clientDocuments',
                },
            ],
            name: 'crm',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['stay-current-portrait'],
                    limited: false,
                    name: 'automaticNotifications',
                },
                {
                    disabled: false,
                    icon: icons['notifications-active'],
                    limited: false,
                    name: 'reminders',
                },
                {
                    disabled: true,
                    icon: icons['notifications-none'],
                    limited: false,
                    name: 'customNotifications',
                },
                {
                    disabled: false,
                    icon: icons['sms'],
                    limited: false,
                    name: 'smsNotifications',
                },
            ],
            name: 'notifications',
        },
        {
            features: [
                {
                    disabled: true,
                    icon: icons['support-agent'],
                    limited: false,
                    name: 'technicalSupport',
                },
            ],
            name: 'support',
        },
    ],
    bannerDescription: 'free_things',
};

export const standard = {
    banner: standardPlanBanner,
    features: [
        {
            features: [
                {
                    disabled: false,
                    icon: icons['people'],
                    limited: false,
                    name: 'unlimitedStaffMembers',
                },
                {
                    disabled: false,
                    icon: icons['event-note'],
                    limited: false,
                    name: 'unlimitedAppointments',
                },
                { disabled: false, icon: icons['store'], limited: false, name: 'businessListing' },
                { disabled: false, icon: icons['web'], limited: false, name: 'yourPersonalWebsite' },
                { disabled: false, icon: icons['group'], limited: false, name: 'communityAccess' },
                { disabled: false, icon: icons['history'], limited: false, name: 'activityHistory' },
                { disabled: false, icon: icons['map'], limited: false, name: 'googleIntegration' },
                {
                    disabled: false,
                    icon: icons['person-pin'],
                    limited: false,
                    name: 'waitingList',
                },
            ],
            name: 'booking',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['monetization-on'],
                    limited: true,
                    name: 'salesReports',
                },
                {
                    disabled: false,
                    icon: icons['insert-chart-outlined'],
                    limited: true,
                    name: 'performanceIndicators',
                },
                {
                    disabled: true,
                    icon: icons['leaderboard'],
                    limited: false,
                    name: 'staffMembersTop',
                },
                {
                    disabled: false,
                    icon: icons['store'],
                    limited: false,
                    name: 'teamManagement',
                },
            ],
            name: 'businessManagement',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['person'],
                    limited: false,
                    name: 'clientHistory',
                },
                {
                    disabled: true,
                    icon: icons['person-add'],
                    limited: false,
                    name: 'clientImport',
                },
                {
                    disabled: false,
                    icon: icons['view-list'],
                    limited: false,
                    name: 'clientListing',
                },
                {
                    disabled: true,
                    icon: icons['insert-drive-file'],
                    limited: false,
                    name: 'clientDocuments',
                },
            ],
            name: 'crm',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['stay-current-portrait'],
                    limited: false,
                    name: 'automaticNotifications',
                },
                {
                    disabled: false,
                    icon: icons['notifications-active'],
                    limited: false,
                    name: 'reminders',
                },
                {
                    disabled: true,
                    icon: icons['notifications-none'],
                    limited: false,
                    name: 'customNotifications',
                },
                {
                    disabled: false,
                    icon: icons['sms'],
                    limited: false,
                    name: 'smsNotifications',
                },
            ],
            name: 'notifications',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['support-agent'],
                    limited: true,
                    name: 'technicalSupport',
                },
            ],
            name: 'support',
        },
    ],
    bannerDescription: 'standard_things',
};

export const professional = {
    banner: professionalPlanBanner,
    features: [
        {
            features: [
                {
                    disabled: false,
                    icon: icons['people'],
                    limited: false,
                    name: 'unlimitedStaffMembers',
                },
                {
                    disabled: false,
                    icon: icons['event-note'],
                    limited: false,
                    name: 'unlimitedAppointments',
                },
                { disabled: false, icon: icons['store'], limited: false, name: 'businessListing' },
                { disabled: false, icon: icons['web'], limited: false, name: 'yourPersonalWebsite' },
                { disabled: false, icon: icons['group'], limited: false, name: 'communityAccess' },
                { disabled: false, icon: icons['history'], limited: false, name: 'activityHistory' },
                { disabled: false, icon: icons['map'], limited: false, name: 'googleIntegration' },
                {
                    disabled: false,
                    icon: icons['person-pin'],
                    limited: false,
                    name: 'waitingList',
                },
            ],
            name: 'booking',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['monetization-on'],
                    limited: false,
                    name: 'salesReports',
                },
                {
                    disabled: false,
                    icon: icons['insert-chart-outlined'],
                    limited: false,
                    name: 'performanceIndicators',
                },
                {
                    disabled: false,
                    icon: icons['leaderboard'],
                    limited: false,
                    name: 'staffMembersTop',
                },
                {
                    disabled: false,
                    icon: icons['store'],
                    limited: false,
                    name: 'teamManagement',
                },
            ],
            name: 'businessManagement',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['person'],
                    limited: false,
                    name: 'clientHistory',
                },
                {
                    disabled: false,
                    icon: icons['person-add'],
                    limited: false,
                    name: 'clientImport',
                },
                {
                    disabled: false,
                    icon: icons['view-list'],
                    limited: false,
                    name: 'clientListing',
                },
                {
                    disabled: false,
                    icon: icons['insert-drive-file'],
                    limited: false,
                    name: 'clientDocuments',
                },
            ],
            name: 'crm',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['stay-current-portrait'],
                    limited: false,
                    name: 'automaticNotifications',
                },
                {
                    disabled: false,
                    icon: icons['notifications-active'],
                    limited: false,
                    name: 'reminders',
                },
                {
                    disabled: false,
                    icon: icons['notifications-none'],
                    limited: false,
                    name: 'customNotifications',
                },
                {
                    disabled: false,
                    icon: icons['sms'],
                    limited: false,
                    name: 'smsNotifications',
                },
            ],
            name: 'notifications',
        },
        {
            features: [
                {
                    disabled: false,
                    icon: icons['support-agent'],
                    limited: false,
                    name: 'technicalSupport',
                },
            ],
            name: 'support',
        },
    ],
    bannerDescription: 'professional_things',
};
