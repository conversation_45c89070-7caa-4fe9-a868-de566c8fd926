import { EnvironmentType } from '@bookr-technologies/env';

export const PrivacyPolicyUrl = 'https://bookr.ro/documents/privacy-policy.pdf';
export const TermsAndConditionsLink = 'https://bookr.ro/documents/terms-and-conditions.pdf';

export const PaymentSubscriptionSuccess = 'https://app.bookr.ro/payments/subscriptions/success';
export const PaymentSubscriptionCancel = 'https://app.bookr.ro/payments/subscriptions/cancel';

export const PaymentCommissionSuccess = 'https://app.bookr.ro/payments/commission/success';
export const PaymentCommissionCancel = 'https://app.bookr.ro/payments/commission/cancel';

export const PaymentTheWomanSuccess = 'https://app.bookr.ro/payments/thewoman/success';

export const PaymentsSMSSuccess = 'https://app.bookr.ro/settings/application/messages?success=true';
export const PaymentsSMSCancel = 'https://app.bookr.ro/settings/application/messages?cancel=true';

export const StripeConnectAccountSuccess = 'https://app.bookr.ro/connectAccount/success';

export const PaymentAppointmentSuccess = 'https://app.bookr.ro/payments/appointment/success';
export const PaymentAppointmentCancel = 'https://app.bookr.ro/payments/appointment/cancel';

export const StripePublicKey: Record<EnvironmentType['app']['env'], string> = {
    local: 'pk_test_51HmckPHtRf7gEyFlrAIcJOQrBtkekVg4EkclZwNOmZW44QA3lAbRA3URjjvlRDkofAvqzjBq9N9u8fhQOjVdByuC00LIeTxCNf',
    production:
        'pk_live_51HmckPHtRf7gEyFlMo6dp4GlztKq5Omnm2ROdeHKYHWKmPTeuxycNA40pstlqUpccwzvN5NzJJewYtJsuOAu9SHo00OUwwSGcs',
    staging:
        'pk_test_51HmckPHtRf7gEyFlrAIcJOQrBtkekVg4EkclZwNOmZW44QA3lAbRA3URjjvlRDkofAvqzjBq9N9u8fhQOjVdByuC00LIeTxCNf',
};
