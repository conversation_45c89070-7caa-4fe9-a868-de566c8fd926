import { LayoutRectangle } from 'react-native';
import { Region } from 'react-native-maps';
import { num } from '~/lib/utils/number';

export const computeCoordsWithDelta = (
    coords: Partial<Region> | null,
    layout: Pick<LayoutRectangle, 'width' | 'height'>,
): Region | null => {
    const hasLayout = layout.width && layout.height;
    const hasDelta = coords?.latitudeDelta && coords?.longitudeDelta;
    const hasCoords = coords && coords.latitude && coords.longitude;

    if ((!hasLayout && !hasDelta) || !hasCoords) {
        return null;
    }

    return {
        latitude: num(coords.latitude),
        longitude: num(coords.longitude),

        // Calculate the Zoom Level of the Map View
        latitudeDelta: coords.latitudeDelta || num(coords.latitude) / (layout.width * 16),
        longitudeDelta: coords.longitudeDelta || num(coords.longitude) / (layout.height * 16),
    };
};
