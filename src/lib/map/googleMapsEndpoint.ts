/* eslint-disable @typescript-eslint/no-explicit-any */
import AsyncStorage from '@react-native-async-storage/async-storage';
import haversine from 'haversine-distance';
import { Region } from 'react-native-maps';
import { createResource } from '@bookr-technologies/api/createResource';
import { GeocodeResponse } from '~/lib/map/GeocodeResponse';

export const GoogleMapsKey = 'AIzaSyDx1bKyeB_aqTTWKLwtE1fiMeWvKLTgybE';

const GeocodePreviousResponseKey = 'geocode_previous_coords';
const GeocodePreviousCoordsKey = 'geocode_previous_response';

interface GeocodeOptions {
    cached?: false | string;
    distance?: number; // in meters
    latitude: number;
    longitude: number;
}

/**
 * Fallback location in case of no permissions or no location
 * Default to the center of the Romania
 */

export const FallbackLocation: Region = {
    latitude: 46.7833803,
    latitudeDelta: 46.7833803 / (300 * 10),
    longitude: 23.5814933,
    longitudeDelta: 23.5814933 / (300 * 10),
};

interface GoogleMapsEndpointType {
    geocode(options: GeocodeOptions): Promise<GeocodeResponse>;
}

export const googleMapsEndpoint = createResource<any, GoogleMapsEndpointType>(
    `https://maps.googleapis.com/maps/api`,
    ({ api }) => ({
        async geocode(options: GeocodeOptions) {
            const { cached = false, distance = 3000, latitude, longitude } = options;

            const fetchGeocode = async () => {
                const { data } = await api.get<GeocodeResponse>('geocode/json', {
                    params: {
                        latlng: `${latitude},${longitude}`,
                    },
                });

                return data;
            };

            if (!cached) {
                return fetchGeocode();
            }

            const existingCoords = await AsyncStorage.getItem(`${GeocodePreviousCoordsKey}:${cached}`);
            const value = await AsyncStorage.getItem(`${GeocodePreviousResponseKey}:${cached}`);

            if (existingCoords && value) {
                const parsedCoords = JSON.parse(existingCoords);
                const parsedValue = JSON.parse(value);

                const distanceBetweenCoords = haversine(parsedCoords, { latitude, longitude });

                if (distanceBetweenCoords < distance) {
                    return parsedValue;
                }
            }

            const response = await fetchGeocode();
            await Promise.all([
                AsyncStorage.setItem(`${GeocodePreviousCoordsKey}:${cached}`, JSON.stringify({ latitude, longitude })),
                AsyncStorage.setItem(`${GeocodePreviousResponseKey}:${cached}`, JSON.stringify(response)),
            ]);

            return response;
        },
    }),
    {
        params: {
            key: GoogleMapsKey,
        },
    },
);
