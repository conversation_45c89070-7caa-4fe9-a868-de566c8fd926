interface PlusCode {
    compound_code: string;
    global_code: string;
}

export interface Coords {
    lat: number;
    lng: number;
}

export interface Compass {
    northeast: Coords;
    southwest: Coords;
}

export interface Geometry {
    bounds: Compass;
    location: Coords;
    location_type: string;
    viewport: Compass;
}

export interface GeoCodeAddressComponent {
    long_name: string;
    short_name: string;
    types: string[];
}

export interface GeocodeResult {
    address_components: GeoCodeAddressComponent[];
    formatted_address: string;
    geometry: Geometry;
    place_id: string;
    plus_code: PlusCode;
    types: string[];
}

export interface GeocodeResponse {
    plus_code: PlusCode;
    results: GeocodeResult[];
    status: string;
}
