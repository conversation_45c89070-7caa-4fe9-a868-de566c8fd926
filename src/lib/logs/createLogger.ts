/* eslint-disable @typescript-eslint/no-explicit-any */
import * as <PERSON><PERSON> from '@sentry/react-native';
import * as Device from 'expo-device';
import { InteractionManager, Platform } from 'react-native';
import { ConsoleTransport, Logger, LoggerContext, SentryTransport } from '@bookr-technologies/logger';

export function createLogger(namespace: string, context: LoggerContext = {}): Logger {
    const logger = Logger.create({
        transports: [new ConsoleTransport(), new SentryTransport(Sentry)],
        asyncFunc: InteractionManager.runAfterInteractions,
        context: {
            platform: Platform.OS,
            deviceName: Device?.deviceName,
        },
        namespace: 'mobileapp',
    });

    return logger.child(context, namespace);
}
