/* eslint-disable @typescript-eslint/no-explicit-any */
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { BottomTabNavigationOptions } from '@react-navigation/bottom-tabs';
import { CommonActions, NavigationContainerRef } from '@react-navigation/native';
import { createRef } from 'react';
import { Platform } from 'react-native';
import { RouteKeys } from '~/RoutesParams';
import { Typography } from '~/components/ui/Typography';
import { MaterialIconsName } from '~/types/icons';

export const topLevelNavigatorRef = createRef<NavigationContainerRef<keyof ReactNavigation.RootParamList>>();

export function setRoot<K extends RouteKeys>(routeName: K, params?: ReactNavigation.RootParamList[K]): void {
    if (topLevelNavigatorRef.current?.getCurrentRoute()?.name !== routeName) {
        topLevelNavigatorRef.current?.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: routeName, params: params as any }],
            }),
        );
    }
}

export function getCurrentRoute(): string | undefined {
    return topLevelNavigatorRef.current?.getCurrentRoute()?.name;
}

export function navigateBack(): void {
    if (topLevelNavigatorRef.current?.canGoBack()) {
        topLevelNavigatorRef.current?.goBack();
    }
}

export function navigate<K extends RouteKeys>(routeName: K, params?: ReactNavigation.RootParamList[K]): void {
    topLevelNavigatorRef.current?.navigate(routeName as any, params as any);
}

export function tabBarOptions(text: string, iconName: MaterialIconsName): BottomTabNavigationOptions {
    return {
        headerShown: false,
        tabBarItemStyle: {
            flexDirection: 'column',
            alignItems: 'center',
        },
        tabBarIcon: ({ color }) => (
            <MaterialIcons
                name={iconName}
                color={color}
                size={24}
                style={{ marginTop: Platform.OS === 'ios' ? -12 : 0 }}
            />
        ),
        tabBarLabel: ({ color }) => (
            <Typography
                fontWeight={500}
                variant={'caption2'}
                textTransform={'capitalize'}
                color={color}
                mt={Platform.OS === 'ios' ? -2 : 0}
                mb={Platform.OS === 'android' ? 0.5 : 0}
            >
                {text}
            </Typography>
        ),
    };
}
