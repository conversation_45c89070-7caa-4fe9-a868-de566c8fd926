import { Optional } from '@bookr-technologies/core/types';
import { value, Value } from '~/lib/value';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function str(val: Optional<any> | Value<Optional<any>>): string {
    if (!val) {
        return '';
    }

    return String(value(val));
}

export function joinStr(values: Array<Optional<string>>, separator: string): string {
    return values.map(str).filter(Boolean).join(separator);
}
