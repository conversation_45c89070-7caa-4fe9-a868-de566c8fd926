import get from 'lodash/get';
import moment from 'moment';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { SearchBusinessModel } from '@bookr-technologies/api/models/SearchBusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';

export const convertBusinessToSearchBusinessModel = (business: BusinessModel): SearchBusinessModel => {
    return {
        averageRating: business.reviewInfo?.averageRating ?? 0,
        categories: (business.categories || []).map((category) => category.name),
        description: business.description,
        formattedAddress: business.formattedAddress,
        id: business.id,
        name: business.name,
        photos: business.photos,
        profilePicture: business.profilePicture,
        services: (business.staffMembers || [])
            .map((staffMember) => staffMember.services || [])
            .flat()
            .map((service) => service.name),
        virtualTourURL: business.virtualTourURL,
    };
};

export const isBusinessStartingDateInFuture = (businessStartingDate: string): boolean => {
    if (businessStartingDate) {
        const NOW = moment();
        if (moment(businessStartingDate, 'YYYY-MM-DD').isAfter(NOW)) {
            return true;
        }
    }
    return false;
};

export const getMinStaffAppointmentDate = (businessStartingDate: string): string => {
    return isBusinessStartingDateInFuture(businessStartingDate)
        ? moment(businessStartingDate, 'YYYY-MM-DD').format('YYYY-MM-DD')
        : moment().format('YYYY-MM-DD');
};

export const getMaxStaffAppointmentDate = (service: ServiceModel, staff: UserModel): string => {
    if (service.numberOfSessions > 0) {
        if (service.daysInAdvanceSessionsCanBeBooked > 0) {
            return moment().add(service.daysInAdvanceSessionsCanBeBooked, 'days').format('YYYY-MM-DD');
        }
        return moment()
            .add(Math.max(90, get(staff, 'maxFutureDaysAppointment', 30)), 'days')
            .format('YYYY-MM-DD');
    }

    return moment().add(get(staff, 'maxFutureDaysAppointment', 30), 'days').format('YYYY-MM-DD');
};
