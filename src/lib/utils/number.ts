import { Optional } from '@bookr-technologies/core/types';
import { Value, value } from '~/lib/value';

/**
 * Returns everytime a number no matter what we pass as parameter
 * @param {Optional<string> | Optional<number>} num
 * @returns {number}
 */
export function num(num: Optional<string> | Optional<number> | Value<Optional<string | number>>): number {
    if (typeof num === 'number' && !Number.isNaN(num)) {
        return num;
    }

    const numberValue = value(num);
    if (!numberValue) {
        return 0;
    }

    const val = Number(numberValue);
    if (Number.isNaN(val)) {
        return 0;
    }

    return val;
}

export function sum(...nums: Array<Optional<string> | Optional<number>>): number {
    return nums.reduce<number>((acc, item) => acc + num(item), 0);
}
