import moment from 'moment';
import { createLogger } from '~/lib/logs/createLogger';

const logger = createLogger('stopwatch');
let stopwatchValue: Date;
let stopwatchLapValue: Date;

export function stopwatch(message: string): void {
    if (!stopwatchValue) {
        stopwatchValue = new Date();
    }

    logger.log(`${message} [${moment().diff(stopwatchValue, 'seconds')}s] since beginning`);
}

export function stopwatchLap(message: string): void {
    if (!stopwatchLapValue) {
        stopwatchLapValue = new Date();
    }

    logger.log(`${message} [${moment().diff(stopwatchLapValue, 'seconds')}s] since the last lap`);

    stopwatchLapValue = new Date();
}
