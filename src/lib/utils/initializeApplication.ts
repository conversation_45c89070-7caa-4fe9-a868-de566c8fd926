import * as Sentry from '@sentry/react-native';
import { ReactNativeTracing } from '@sentry/react-native';
import * as Device from 'expo-device';
import * as Updates from 'expo-updates';
import { Alert, LogBox, Platform, UIManager } from 'react-native';
import { env } from '@bookr-technologies/env';
import { createLogger } from '~/lib/logs/createLogger';

export function initializeApplication(): void {
    LogBox.ignoreLogs(['ViewPropTypes will be removed from React Native.']);

    if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
    }

    const canLog = Device.isDevice && env('app.env') !== 'local';
    Sentry.init({
        debug: canLog,
        enabled: Device.isDevice && canLog,
        dsn: 'https://<EMAIL>/6141067',
        environment: env('app.env'),
        integrations: [
            new ReactNativeTracing({
                tracingOrigins: ['localhost', 'bookr.ro', /^\//],
            }),
        ],
    });

    if (Device.isDevice && env('app.env') === 'production') {
        checkForUpdates();
    }
}

function checkForUpdates(): void {
    const logger = createLogger('updatesManager');

    // TODO: find a better way to check for updates
    setInterval(async () => {
        try {
            const update = await Updates.checkForUpdateAsync();

            if (update.isAvailable) {
                const updateFetched = await Updates.fetchUpdateAsync();
                if (!updateFetched.isNew) {
                    return;
                }
                Alert.alert(
                    'New Update available',
                    'The application will be restarted.',
                    [
                        {
                            text: 'OK',
                            async onPress(): Promise<void> {
                                try {
                                    await Updates.reloadAsync();
                                } catch (error) {
                                    logger.error('error restarting app', { error });
                                }
                            },
                        },
                    ],
                    { cancelable: false },
                );
            }
        } catch (error) {
            logger.error('error getting update', { error });
        }
    }, 1000 * 60 * 2); // every 2 minutes
}
