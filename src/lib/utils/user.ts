import * as Sentry from '@sentry/react-native';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { SubscriptionModel } from '@bookr-technologies/api/models/SubscriptionModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';

export const canUserView = (
    user: UserModel,
    subscription: SubscriptionModel | null,
    userPrivileges: UserPrivilegeType[],
): boolean => {
    if (!user) {
        return false;
    }
    if (!subscription) {
        Sentry.captureMessage(`User ${user.uid} has no subscription! Check his account!`);
        return false;
    }
    if (subscription?.commission && !subscription?.commissionPastDue) {
        return true;
    }
    return (user && UserModel.hasPrivileges(user, ...userPrivileges)) || SubscriptionModel.isTrial(subscription);
};
