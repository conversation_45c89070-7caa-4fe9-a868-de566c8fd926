import { num, sum } from './number';

describe('Library utilities#number', () => {
    it('should get the right number no matter what parameter we pass', () => {
        expect(num('-')).toBe(0);
        expect(num(0)).toBe(0);
        expect(num(1)).toBe(1);
        expect(num('2')).toBe(2);
        expect(num(0.000005)).toBe(0.000005);
        expect(num('0.000005')).toBe(0.000005);
        expect(num(null)).toBe(0);
        expect(num(undefined)).toBe(0);

        expect(num(() => '-')).toBe(0);
        expect(num(() => 0)).toBe(0);
        expect(num(() => 1)).toBe(1);
        expect(num(() => '2')).toBe(2);
        expect(num(() => 0.000005)).toBe(0.000005);
        expect(num(() => '0.000005')).toBe(0.000005);
        expect(num(() => null)).toBe(0);
        expect(num(() => undefined)).toBe(0);
    });

    it('should get the right sum', () => {
        expect(sum(0, 1, 2, 3, 4, 5)).toBe(15);
    });
});
