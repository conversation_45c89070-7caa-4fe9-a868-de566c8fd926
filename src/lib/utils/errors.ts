import { FirebaseError } from 'firebase/app';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';

export function getErrorMessageWithFirebase(error: Error | FirebaseError, defaultValue?: string): string {
    const firebaseErrorCode = (error as FirebaseError).code ?? '';
    const firebaseErrorMessage = firebaseErrorCode ? firebaseErrorCode : getErrorMessage(error, defaultValue);

    return firebaseErrorMessage && firebaseErrorMessage !== firebaseErrorCode
        ? firebaseErrorMessage
        : 'somethingWentWrong';
}
