import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Sentry from '@sentry/react-native';
import * as Calendar from 'expo-calendar';
import moment from 'moment';
import { Platform } from 'react-native';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { createLogger } from '~/lib/logs/createLogger';

export const BOOKR_CALENDAR_TITLE = 'BOOKR Calendar';

export interface AddToCalendarResponse {
    code?: string;
    status: 'success' | 'error';
}

const logger = createLogger('calendar');

export const addToCalendar = async (
    eventTitle: string,
    timestamp: number,
    service: ServiceModel,
    staff: UserModel,
    business: BusinessModel,
    appointmentId: string,
): Promise<AddToCalendarResponse> => {
    try {
        const { status: s1 } = await Calendar.requestCalendarPermissionsAsync();
        if (s1 !== 'granted') {
            return Promise.resolve({ status: 'error', code: 'requestCalendarPermissionsAsyncNotGranted' });
        }

        if (Platform.OS === 'ios') {
            const { status: s2 } = await Calendar.requestRemindersPermissionsAsync();
            if (s2 !== 'granted') {
                return Promise.resolve({ status: 'error', code: 'requestRemindersPermissionsAsyncNotGranted' });
            }
        }

        const startDate = moment(timestamp * 1000).toDate();
        const endDate = moment(startDate).add(service.duration, 'minutes').toDate();
        const event = {
            title: eventTitle,
            startDate,
            endDate,
            location: business.formattedAddress,
            alarms: [
                {
                    relativeOffset: -3 * 60, // three hours before
                    method: Calendar.AlarmMethod.ALERT,
                },
                {
                    relativeOffset: -24 * 60, // 24 hours before
                    method: Calendar.AlarmMethod.ALERT,
                },
            ],
        };
        const eventId = await Calendar.createEventAsync(await getBookrCalendarId(), event);
        AsyncStorage.setItem('appointment_' + appointmentId, eventId);

        return Promise.resolve({ status: 'success' });
    } catch (error) {
        logger.error('error saving to calendar', {
            error,
            eventTitle,
            appointmentId,
            datetime: timestamp,
            serviceId: service.id,
            staffId: staff.uid,
            businessId: business.id,
        });
        Sentry.captureException(error, {
            extra: {
                message: 'error saving to calendar',
                eventTitle,
                appointmentId,
                datetime: timestamp,
                serviceId: service.id,
                staffId: staff.uid,
                businessId: business.id,
            },
        });

        return Promise.resolve({ status: 'error', code: 'errorSavingToCalendar' });
    }
};

const createCalendar = async () => {
    const defaultCalendarSource = {
        isLocalAccount: true,
        name: BOOKR_CALENDAR_TITLE,
        type: Calendar.CalendarType.LOCAL,
    };

    return Calendar.createCalendarAsync({
        title: BOOKR_CALENDAR_TITLE,
        color: 'blue',
        entityType: Calendar.EntityTypes.EVENT,
        source: defaultCalendarSource,
        name: 'bookr_calendar',
        ownerAccount: 'personal',
        accessLevel: Calendar.CalendarAccessLevel.OWNER,
    });
};

const getBookrCalendarId = async () => {
    const calendars = await Calendar.getCalendarsAsync();
    const bookrCalendar = calendars.find((calendar) => calendar.title === BOOKR_CALENDAR_TITLE);
    if (!bookrCalendar) {
        return await createCalendar();
    }
    return bookrCalendar.id;
};

export const deleteCalendarEvent = async (appointmentId: string) => {
    try {
        const eventId = await AsyncStorage.getItem('appointment_' + appointmentId);
        if (eventId) {
            // noinspection ES6MissingAwait
            Calendar.deleteEventAsync(eventId);

            // noinspection ES6MissingAwait
            AsyncStorage.removeItem('appointment_' + appointmentId);
        }
    } catch (error) {
        logger.error('error deleting calendar event', { error, appointmentId });
    }
};

export const existsCalendarEvent = async (appointmentId: string) => {
    try {
        const eventId = await AsyncStorage.getItem('appointment_' + appointmentId);
        return !!eventId;
    } catch (error) {
        logger.error('error checking calendar event', { error, appointmentId });
    }
};
