import { appleAuth } from '@invertase/react-native-apple-authentication';
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage';
import reactNativeFirebaseAuth from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import * as auth from 'firebase/auth';
import { getReactNativePersistence } from 'firebase/auth';
import { AccessToken, LoginManager } from 'react-native-fbsdk-next';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { firebaseApp } from '~/lib/firebase/app';
import { createLogger } from '~/lib/logs/createLogger';
import { getPushNotificationToken } from '~/lib/utils/PushNotificationsUtils';

GoogleSignin.configure({
    webClientId: '',
});

export type FirebaseUser = auth.User;

const log = createLogger('firebase/auth');
export const firebaseAuth = auth.initializeAuth(firebaseApp, {
    persistence: getReactNativePersistence(ReactNativeAsyncStorage),
});
// export const firebaseAuth = auth.getAuth(firebaseApp);

function isErrorCode(error: unknown, code: string): boolean {
    if (error instanceof Error && 'code' in error) {
        return error.code === code;
    }

    return false;
}

export function getFirebaseUser(): FirebaseUser | null {
    return firebaseAuth.currentUser;
}

export async function sendPasswordResetEmail(email: string): Promise<void> {
    await auth.sendPasswordResetEmail(firebaseAuth, email);
}

export async function signInWithEmailAndPassword(email: string, password: string): Promise<FirebaseUser> {
    const { user } = await auth.signInWithEmailAndPassword(firebaseAuth, email, password);

    return user;
}

export async function signInWithCustomToken(token: string): Promise<FirebaseUser> {
    const { user } = await auth.signInWithCustomToken(firebaseAuth, token);

    return user;
}

export async function changePassword(oldPassword: string, newPassword: string): Promise<void> {
    const user = getFirebaseUser();
    if (user?.email) {
        await signInWithEmailAndPassword(user.email, oldPassword);
        await auth.updatePassword(user, newPassword);
    }
}

export async function createUserWithEmailAndPassword(email: string, password: string): Promise<FirebaseUser> {
    const { user } = await auth.createUserWithEmailAndPassword(firebaseAuth, email, password);

    return user;
}

export async function sendEmailVerification(): Promise<void> {
    const user = getFirebaseUser();

    if (user) {
        await auth.sendEmailVerification(user);
    }
}

export async function signOut(): Promise<void> {
    await auth.signOut(firebaseAuth);
}

export async function signInWithApple(): Promise<FirebaseUser> {
    const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
    });

    if (!appleAuthRequestResponse.identityToken) {
        throw new Error('oauthNoPermission');
    }

    const { identityToken, nonce } = appleAuthRequestResponse;
    const appleCredential = reactNativeFirebaseAuth.AppleAuthProvider.credential(identityToken, nonce);
    const { user } = await reactNativeFirebaseAuth().signInWithCredential(appleCredential);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return user as any;
}

export async function signInWithGoogle(): Promise<FirebaseUser> {
    try {
        await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
        const { idToken } = await GoogleSignin.signIn();
        const googleCredential = auth.GoogleAuthProvider.credential(idToken);
        const { user } = await auth.signInWithCredential(firebaseAuth, googleCredential);

        return user;
    } catch (error) {
        if (isErrorCode(error, '-5')) {
            throw new Error('oauthCancelled');
        }

        throw error;
    }
}

export async function signInWithFacebook(): Promise<FirebaseUser> {
    const result = await LoginManager.logInWithPermissions(['public_profile', 'email']);
    if (result.isCancelled) {
        throw new Error('oauthCancelled');
    }

    const data = await AccessToken.getCurrentAccessToken();
    if (!data) {
        throw new Error('oauthNoPermission');
    }

    const facebookCredential = auth.FacebookAuthProvider.credential(data.accessToken);
    const { user } = await auth.signInWithCredential(firebaseAuth, facebookCredential);

    return user;
}

export async function obtainExpoPushNotification(user: FirebaseUser): Promise<void> {
    const logger = log.child({ userId: user.uid }, 'obtainExpoPushNotification');
    logger.debug('Obtaining expo push notification token');

    try {
        const notificationPermissions = await Notifications.getPermissionsAsync();
        if (notificationPermissions.granted && Device.isDevice) {
            const { data: pushNotificationToken } = await getPushNotificationToken();
            await usersEndpoint.update(user.uid, { pushNotificationToken });
        }
    } catch (error) {
        logger.error('error fetching expo push notification token', { error });
    }
}
