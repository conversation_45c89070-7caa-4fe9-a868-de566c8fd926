import remoteConfig from '@react-native-firebase/remote-config';
import { isProduction } from '@bookr-technologies/env';
import { createLogger } from '~/lib/logs/createLogger';

export enum RemoteConfigParameters {
    DateTimeSearchFilterEnabled = 'dateTimeSearchFilterEnabled',
    MobileMaintenanceEnabled = 'mobileMaintenanceEnabled',
    SocialLoginEnabled = 'socialLoginEnabled',
    TheWomanEnabled = 'theWomanEnabled',
    UntoldFestivalEnabled = 'untoldFestivalEnabled',
}

const logger = createLogger('firebase/remoteConfig');

export const firebaseRemoteConfig = remoteConfig();

export async function initializeFirebaseRemoteConfig(): Promise<boolean> {
    logger.debug('initialize');
    try {
        const ttl = isProduction() ? 60 * 1000 : 0;
        await remoteConfig().setConfigSettings({ fetchTimeMillis: ttl, minimumFetchIntervalMillis: ttl });
        const activated = await remoteConfig().fetchAndActivate();
        logger.debug('initialized', { activated });

        return activated;
    } catch (error) {
        logger.error('fetchAndActivate', { error });
    }

    return false;
}

export function isRemoteConfigParameterEnabled(name: RemoteConfigParameters): boolean {
    return remoteConfig().getValue(name).asBoolean();
}
