import { NotificationItem } from '~/lib/notifications/NotificationItem';
import { useNotificationsStore } from '~/store/useNotificationsStore';
import { NotificationTypeEnum } from './NotificationTypeEnum';

export class NotificationsService {
    public static close(notification: string | NotificationItem): void {
        setImmediate(() => {
            useNotificationsStore.getState().removeNotification(notification);
        });
    }

    public static error(message: string, title = ''): void {
        NotificationsService.notify(NotificationTypeEnum.Error, message, title);
    }

    public static info(message: string, title = ''): void {
        NotificationsService.notify(NotificationTypeEnum.Info, message, title);
    }

    public static notify(type: NotificationTypeEnum, message: string, title = ''): void {
        useNotificationsStore.getState().addNotification({
            key: type + message + title,
            message,
            title,
            type,
            wait: false,
        });
    }

    public static success(message: string, title = ''): void {
        NotificationsService.notify(NotificationTypeEnum.Success, message, title);
    }

    public static warning(message: string, title = ''): void {
        NotificationsService.notify(NotificationTypeEnum.Warning, message, title);
    }
}
