/* eslint-disable @typescript-eslint/no-explicit-any */
import firebaseAnalytics from '@react-native-firebase/analytics';
import { AppEventsLogger, Settings } from 'react-native-fbsdk-next';
import { env } from '@bookr-technologies/env';
import { FirebaseUser } from '~/lib/firebase/auth';
import { createLogger } from '~/lib/logs/createLogger';
import { initializeMixpanelAnalytics, mixpanel } from './mixpanel';

const logger = createLogger('firebase/analytics');

export enum AnalyticsEvent {
    BookingEdited = 'booking_edited',
    BookingMade = 'booking_made',
    BusinessDeleted = 'business_deleted',
    BusinessViewed = 'business_viewed',
    CheckoutSessionCreated = 'checkout_session_created',
    ChoosePlan = 'choose_plan',
    ChoosePlanSkipped = 'choose_plan_skipped',
    ContinueAsGuest = 'continue_as_guest',
    FreePlanAppointmentsIncreaseLimit = 'free_plan_appointments_increase_limit',
    FreePlanAppointmentsViewInfo = 'free_plan_appointments_view_info',
    LeadApp = 'lead_app',
    PhoneCallAppointment = 'phone_call_appointment',
    PlanSelected = 'plan_selected',
    Showcase3DVirtualTourOpened = 'showcase_3d_virtual_tour_opened',
    SignUp = 'sign_up',
    WaitingListSubscriptions = 'waiting_list_subscriptions',
}

export enum AnalyticsEventProvider {
    Facebook = 'facebook',
    Firebase = 'firebase',
    Mixpanel = 'mixpanel',
}

const replicaEventProvidersMap: Record<AnalyticsEvent | string, AnalyticsEventProvider[]> = {
    [AnalyticsEvent.BookingEdited]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.BookingMade]: [
        AnalyticsEventProvider.Mixpanel,
        AnalyticsEventProvider.Firebase,
        AnalyticsEventProvider.Facebook,
    ],
    [AnalyticsEvent.BusinessDeleted]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.LeadApp]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Facebook],
    [AnalyticsEvent.Showcase3DVirtualTourOpened]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.SignUp]: [
        AnalyticsEventProvider.Mixpanel,
        AnalyticsEventProvider.Firebase,
        AnalyticsEventProvider.Facebook,
    ],
    [AnalyticsEvent.WaitingListSubscriptions]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.PhoneCallAppointment]: [AnalyticsEventProvider.Mixpanel],
    [AnalyticsEvent.BusinessViewed]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.ContinueAsGuest]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.FreePlanAppointmentsViewInfo]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.FreePlanAppointmentsIncreaseLimit]: [
        AnalyticsEventProvider.Mixpanel,
        AnalyticsEventProvider.Firebase,
    ],
    [AnalyticsEvent.ChoosePlanSkipped]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.ChoosePlan]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.PlanSelected]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
    [AnalyticsEvent.CheckoutSessionCreated]: [AnalyticsEventProvider.Mixpanel, AnalyticsEventProvider.Firebase],
};

export function logAnalyticsEvent<K extends AnalyticsEvent | string>(name: K, params?: Record<string, any>): void {
    let replicaProviders: AnalyticsEventProvider[] = replicaEventProvidersMap[name] ?? [];
    if (replicaProviders.length === 0 && name in AnalyticsEvent) {
        replicaProviders = [AnalyticsEventProvider.Firebase];
    }

    logger.debug('logAnalyticsEvent', { name, params, replicaProviders });

    if (replicaProviders.includes(AnalyticsEventProvider.Firebase)) {
        // noinspection JSIgnoredPromiseFromCall
        firebaseAnalytics().logEvent(name, params);
    }

    if (replicaProviders.includes(AnalyticsEventProvider.Facebook)) {
        AppEventsLogger.logEvent(name, { ...params });
    }

    if (replicaProviders.includes(AnalyticsEventProvider.Mixpanel)) {
        mixpanel.track(name, params);
    }
}

export function setAnalyticsUser(user: FirebaseUser | null): void {
    const uid = user?.uid ?? null;
    const email = user?.email ?? '';
    const phone = user?.phoneNumber ?? '';
    const [firstName, lastName] = (user?.displayName ?? '').split(' ');

    // noinspection JSIgnoredPromiseFromCall
    firebaseAnalytics().setUserId(uid);

    AppEventsLogger.setUserID(uid);
    AppEventsLogger.setUserData({
        email,
        firstName,
        lastName,
        phone,
    });

    mixpanel.identify(uid);
    mixpanel.getPeople().set({
        email: email,
        name: firstName,
        lastName: lastName,
        userPhoneNumber: phone,
    });
}

export function setAnalyticsCollectionEnabled(enabled: boolean): void {
    // noinspection JSIgnoredPromiseFromCall
    firebaseAnalytics().setAnalyticsCollectionEnabled(enabled);

    // noinspection JSIgnoredPromiseFromCall
    Settings.setAdvertiserTrackingEnabled(enabled);

    if (enabled && env('app.env') !== 'local') {
        initializeMixpanelAnalytics(env('app.env') as any);
    }
}
