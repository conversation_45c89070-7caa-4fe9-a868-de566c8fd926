import { Mixpanel } from 'mixpanel-react-native';

const trackAutomaticEvents = true;

const envs = {
    production: '9194d177d1402429ec9de8212454b32b',
    'production-rc': '3a29171b4e8535e9dcfe80070f862344',
    staging: '3a29171b4e8535e9dcfe80070f862344',
    development: '3a29171b4e8535e9dcfe80070f862344',
    simulator: '3a29171b4e8535e9dcfe80070f862344',
};

let instance: Mixpanel;

const initializeMixpanelAnalytics = async (
    env: 'development' | 'simulator' | 'staging' | 'production-rc' | 'production',
) => {
    console.log('Mixpanel initializing for env', env);
    instance = new Mixpanel(envs[env], trackAutomaticEvents);
    await instance.init();
    console.log(`Mixpanel initialized for env ${env}`);
    instance.track('App Open');
};

const mixpanel = {
    track: (event: string, properties?: any) => {
        if (instance) {
            instance.track(event, properties);
        }
    },
    identify: (userId: string | null) => {
        if (instance && !!userId) {
            instance.identify(userId);
        }
    },
    getPeople: () => {
        if (instance) {
            return instance.getPeople();
        } else {
            return {
                set: () => {
                    // do nothing
                },
            };
        }
    },
};

export { mixpanel, initializeMixpanelAnalytics };
