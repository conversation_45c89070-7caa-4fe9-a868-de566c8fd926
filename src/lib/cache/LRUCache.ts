/* eslint-disable @typescript-eslint/no-explicit-any */

export class LRUCache {
    private keys: Set<string>;
    private store: Map<string, any>;

    public constructor(private maxSize: number = 10, private initialData?: any) {
        this.store = new Map<string, any>();
        this.keys = new Set<string>();

        if (initialData && typeof initialData === 'object') {
            Object.entries(initialData).forEach(([key, value]) => this.set(key, value));
        }
    }

    public delete(key: string): void {
        this.keys.delete(key);
        this.store.delete(key);
    }

    public get(key: string): any {
        const value = this.store.get(key);

        if (!this.keys.has(key) && value) {
            this.keys.add(key);
        }

        if (!value) {
            this.delete(key);
        }

        return value;
    }

    public async getOrSet<T>(key: string, getter: () => T | Promise<T>): Promise<T> {
        const value = this.get(key);

        if (value) {
            return value;
        }

        const newValue = await getter();
        this.set(key, newValue);

        return newValue;
    }

    public has(key: string): boolean {
        return this.keys.has(key);
    }

    public set(key: string, value: any): void {
        this.keys.add(key);
        this.store.set(key, value);

        if (this.keys.size > this.maxSize) {
            this.removeOldest();
        }
    }

    public size(): number {
        return this.keys.size;
    }

    private removeOldest(): void {
        const firstKey = this.keys.keys().next().value;
        if (firstKey) {
            this.delete(firstKey);
        }
    }
}
