import * as Localization from 'expo-localization';
import i18n from 'i18next';
import moment from 'moment';
import 'moment/locale/en-gb';
import 'moment/locale/ro';
import { initReactI18next } from 'react-i18next';
import { createLogger } from '~/lib/logs/createLogger';
import * as resources from './resources';

const log = createLogger('i18n');

const SUPPORTED_LANGUAGES = ['en', 'ro'];

export async function changeLanguage(language: keyof typeof resources | string) {
    let lng = 'en';
    if (SUPPORTED_LANGUAGES.includes(language)) {
        lng = language;
    }
    log.log('Change language', { language: lng });
    await i18n.changeLanguage(lng);
    moment().locale(lng);
}

export function initializeI18n(lng?: string): void {
    lng = lng || Localization.locale;

    log.log('Initializing with language', { language: lng });
    moment().locale(lng);

    i18n.use(initReactI18next).init({
        compatibilityJSON: 'v3',
        fallbackLng: 'en',
        interpolation: {
            escapeValue: false,
        },
        returnNull: true,
        lng,
        resources,
    });
}
