export type TranslationKeys =
    | 'aboutBusiness'
    | 'aboutNews'
    | 'aboutUs'
    | 'absentClient'
    | 'accountTypes.BUSINESS_OWNER'
    | 'accountTypes.CLIENT'
    | 'accountTypes.EMPLOYEE'
    | 'accountVerificationScreenMessage'
    | 'active'
    | 'activities.APPOINTMENT_CANCELLED.message'
    | 'activities.APPOINTMENT_CANCELLED.title'
    | 'activities.APPOINTMENT_RESCHEDULED.message'
    | 'activities.APPOINTMENT_RESCHEDULED.title'
    | 'activities.NEW_APPOINTMENT.message'
    | 'activities.NEW_APPOINTMENT.title'
    | 'activityHistory'
    | 'activityMonth'
    | 'add'
    | 'addBookingToSubscription'
    | 'addBreak'
    | 'addBreakName'
    | 'addClient'
    | 'addClients'
    | 'addClientsSubtitle'
    | 'addClientsTitle'
    | 'addColorToYourService'
    | 'addComment'
    | 'addedToCalendar'
    | 'addMeToWaitingList'
    | 'addNew'
    | 'addNewBooking'
    | 'addNewBreak'
    | 'addNewClient'
    | 'addNewClientByName'
    | 'addNoOfContacts'
    | 'addNotes'
    | 'address1'
    | 'address2'
    | 'addToCalendar'
    | 'agreePrivacyAndTermsMessage'
    | 'allCategories'
    | 'allContacts'
    | 'allowNotifications'
    | 'allYourDataWillBeLost'
    | 'allYourFutureBookingsWillBeCancelled'
    | 'allYourStaffMembersWillBeRemoved'
    | 'appointment'
    | 'appointmentCancelled'
    | 'appointmentCancelledMessage'
    | 'appointmentDetails'
    | 'appointmentRescheduled'
    | 'appointmentRescheduledMessage'
    | 'appointments'
    | 'appointmentsStoryHeadline'
    | 'appointmentSuccessfullyModified'
    | 'appointmentSuccessfullyCreated'
    | 'appointmentUpdated'
    | 'askForVirtualTour'
    | 'auth/too-many-requests'
    | 'auth/user-token-expired'
    | 'auth/wrong-password'
    | 'automaticNotifications'
    | 'availability'
    | 'availabilityCardCaption'
    | 'availabilityNrOfDays'
    | 'availabilityScreenCaption'
    | 'bad'
    | 'barber'
    | 'bestBusinesses'
    | 'bestCategories'
    | 'billingHistory'
    | 'billingInfo'
    | 'block'
    | 'blockClient'
    | 'blockInfo'
    | 'bookAgain'
    | 'booking'
    | 'bookNow'
    | 'bookrFeedback'
    | 'break'
    | 'breakBegins'
    | 'breakBetweenServices'
    | 'breakCouldNotBeDeleted'
    | 'breakEnds'
    | 'breakName'
    | 'breakWasSuccessfullyAdded'
    | 'bugReport'
    | 'business'
    | 'businessCategories'
    | 'businessDeleted'
    | 'businessEmail'
    | 'businessHidden'
    | 'businessInfo'
    | 'businessListing'
    | 'businessLocation'
    | 'businessManagement'
    | 'businessName'
    | 'businessProfile'
    | 'businessStartingDate'
    | 'businessStartingDateDescription'
    | 'buy'
    | 'buyFor'
    | 'buySmsMessages'
    | 'calendar'
    | 'calendarStoryHeadline'
    | 'call'
    | 'cancel'
    | 'cancelAllAppointmentsSubtitle'
    | 'cancelAllAppointmentsTitle'
    | 'cancelAppointment'
    | 'cancelAppointmentTitle'
    | 'cancelled'
    | 'cancelSubscription'
    | 'cancelSubscriptionConfirmationCaption'
    | 'cancelSubscriptionConfirmationTitle'
    | 'cannotCancelAppointmentAnymore'
    | 'cannotLoadClient'
    | 'cannotLoadClients'
    | 'categories.barber'
    | 'categories.body_remodeling'
    | 'categories.cosmetics'
    | 'categories.coworking'
    | 'categories.dentistry'
    | 'categories.dermatology'
    | 'categories.event'
    | 'categories.gynecology'
    | 'categories.hairstyling'
    | 'categories.implant'
    | 'categories.makeup'
    | 'categories.manicure'
    | 'categories.massage'
    | 'categories.nutrition_dietetics'
    | 'categories.ophthalmology'
    | 'categories.orl'
    | 'categories.other'
    | 'categories.permanent_hair_removal'
    | 'categories.photography'
    | 'categories.plastic_surgery'
    | 'categories.podiatry'
    | 'categories.psychologist'
    | 'categories.sport'
    | 'categories.surgery'
    | 'categories.tattoos'
    | 'categories.videography'
    | 'categoriesLabel'
    | 'categoriesStoryHeadline'
    | 'categoriesWereUpdatedSuccessfully'
    | 'changeLanguage'
    | 'changePass'
    | 'changesWereSuccessfullySaved'
    | 'charactersLeft'
    | 'childAbuse'
    | 'chooseAccountTypeDescription'
    | 'chooseAccountTypeHeadline'
    | 'chooseAPlan'
    | 'chooseBusinessDescription'
    | 'chooseDay'
    | 'chooseHour'
    | 'chooseInterval'
    | 'choosePersonalDescription'
    | 'choosePlan'
    | 'chooseStaffMember'
    | 'chooseSubscriptionPlan'
    | 'chooseSubscriptionPlanDescription'
    | 'chooseSubscriptionPlanHeadline'
    | 'chooseSubscriptionPlanTerms'
    | 'chooseTeamMember'
    | 'chooseYourDate'
    | 'chooseYourTime'
    | 'chosenService'
    | 'city'
    | 'clientDetails'
    | 'clientDocuments'
    | 'clientEmail'
    | 'clientHistory'
    | 'clientImport'
    | 'clientImportFailed'
    | 'clientImportStarted'
    | 'clientIsNotSelected'
    | 'clientListing'
    | 'clientName'
    | 'clients'
    | 'closed'
    | 'commission'
    | 'communityAccess'
    | 'completed'
    | 'compliment'
    | 'confirmationSent'
    | 'confirmed'
    | 'confirmYourPassword'
    | 'contactDetails'
    | 'contactPermission'
    | 'content'
    | 'continue'
    | 'copyLink'
    | 'couldNotSubscribeToWaitingList'
    | 'country'
    | 'createAppointmentScreen.headline'
    | 'createAService'
    | 'createBreakScreen.headline'
    | 'createdAt'
    | 'createNewService'
    | 'createPass'
    | 'createPassword'
    | 'createService'
    | 'createServices'
    | 'crm'
    | 'currencies.EUR'
    | 'currencies.GBP'
    | 'currencies.LEI'
    | 'currencies.USD'
    | 'currency'
    | 'currentPlan'
    | 'customNotifications'
    | 'dateAndTime'
    | 'days.friday'
    | 'days.monday'
    | 'days.saturday'
    | 'days.sunday'
    | 'days.thursday'
    | 'days.tuesday'
    | 'days.wednesday'
    | 'daysLabel'
    | 'delete'
    | 'deleteAccount'
    | 'deleteAccountSideEffects'
    | 'deleteServiceHeadline'
    | 'description'
    | 'displayName'
    | 'done'
    | 'doYouWantToDeleteThisImage'
    | 'doYouWantToDeleteThisStaffMember'
    | 'duration'
    | 'edit'
    | 'editAppointment'
    | 'editNote'
    | 'editProfile'
    | 'editTime'
    | 'email'
    | 'emailAddress'
    | 'employee'
    | 'en-EN'
    | 'en-US'
    | 'enableSmsReminders'
    | 'enableSmsRemindersCaption'
    | 'enterYourEmail'
    | 'errorCreatingAccount'
    | 'errorSavingToCalendar'
    | 'errorSendingInvitation'
    | 'explore3DTour'
    | 'exploreNewBusinesses'
    | 'facebookUrl'
    | 'favouriteAdded'
    | 'favouriteError'
    | 'favouriteRemoved'
    | 'favourites'
    | 'feedback'
    | 'feedbackCategory'
    | 'feedbackUs'
    | 'fetchingClients'
    | 'fetchingClientsNoData'
    | 'filterByRating'
    | 'finishAccount'
    | 'finished'
    | 'finishSubscriptionBooking'
    | 'firstNameAndLastName'
    | 'followUsOnInstagram'
    | 'forbiddenAccess'
    | 'forBusiness'
    | 'forBusinessesStoryHeadline'
    | 'forgotPassword'
    | 'FREE_ACCESS'
    | 'free_things'
    | 'from'
    | 'fromContacts'
    | 'gallery'
    | 'general'
    | 'generalInformation'
    | 'getDirections'
    | 'getThereWithUber'
    | 'good'
    | 'googleIntegration'
    | 'gotIt'
    | 'grantAccess'
    | 'harmfulDangerousActs'
    | 'hatefulOrAbusiveContent'
    | 'haveYouTypedAWrongEmail'
    | 'hello'
    | 'help'
    | 'hideBusiness'
    | 'hideBusinessDescription'
    | 'hideService'
    | 'history'
    | 'home'
    | 'howCanWeImprove'
    | 'howToImprove'
    | 'id'
    | 'idValue'
    | 'iHaveChangedMyMind'
    | 'iHaveConfirmed'
    | 'imageAdded'
    | 'imageDeleted'
    | 'imReady'
    | 'imReadySent'
    | 'infringesMyRights'
    | 'instagramUrl'
    | 'invalidEmail'
    | 'inviteStaffMember'
    | 'inviteStaffMemberDescription'
    | 'johnDoe'
    | 'joinBusiness'
    | 'joinBusinessDescription'
    | 'joinBusinessHeadline'
    | 'keepMyAccountActive'
    | 'language'
    | 'latestSearches'
    | 'limited'
    | 'linkCopied'
    | 'location'
    | 'locationInformationCouldNotBeFetched'
    | 'locationPermissionsDenied'
    | 'locationPermissionsDeniedMessage'
    | 'locationWasUpdated'
    | 'logout'
    | 'mail'
    | 'makeAppointment'
    | 'marketing'
    | 'membersBreaks'
    | 'message'
    | 'minLength'
    | 'minNumberOfSessionsError'
    | 'minTimeBeforeAppointment'
    | 'minTimeBeforeAppointmentCardCaption'
    | 'minTimeBeforeCancel'
    | 'minTimeBeforeCancelCardCaption'
    | 'minutes'
    | 'moreOptions'
    | 'myAppointments'
    | 'myProfile'
    | 'nearbyBusinesses'
    | 'needANewAccount'
    | 'newAppointment'
    | 'newAppointmentMessage'
    | 'newPass'
    | 'next'
    | 'nextAppointment'
    | 'nextPaymentDate'
    | 'no'
    | 'noAppointmentsCreateNewOne'
    | 'noAppointmentsYet'
    | 'noBillingHistory'
    | 'noBreaksYet'
    | 'noClients'
    | 'noContactInformation'
    | 'noContacts'
    | 'noFavouritesYet'
    | 'noNoteSaved'
    | 'noNotifications'
    | 'noPreviewAvailable'
    | 'noProfilePictureSelected'
    | 'noServices'
    | 'noServicesFound'
    | 'noShow'
    | 'noSmsOptionsFound'
    | 'noStaffMembersFound'
    | 'noSubscriptionFound'
    | 'noteCreated'
    | 'notes'
    | 'noteUpdated'
    | 'notificationPermissionsDenied'
    | 'notificationPreferenceDescription'
    | 'notificationPreferenceHeadline'
    | 'notifications'
    | 'notificationsError'
    | 'notificationsSettings'
    | 'noTimeSlotsAvailable'
    | 'notNow'
    | 'numberOfDays'
    | 'numberOfLeftSms'
    | 'numberOfMessages'
    | 'numberOfMinutes'
    | 'numberOfSessions'
    | 'ok'
    | 'oldPass'
    | 'oneStaffMember'
    | 'onlyAdminCanPay'
    | 'open'
    | 'others'
    | 'ourTeamWillContactYouAsSoonAsPossible'
    | 'overlapAppointmentMessage'
    | 'overlapAppointmentTitle'
    | 'passChangeSuccess'
    | 'password'
    | 'passwordNotMatch'
    | 'payments'
    | 'payNow'
    | 'performance'
    | 'performanceIndicators'
    | 'performanceStats.cancelledAppointments'
    | 'performanceStats.finalizedAppointments'
    | 'performanceStats.hours'
    | 'performanceStats.lostHours'
    | 'performanceStats.reservedTime'
    | 'performanceStats.ron'
    | 'performanceStats.totalAppointments'
    | 'performanceStats.totalClients'
    | 'performanceStats.totalEarnings'
    | 'performanceStats.totalTime'
    | 'performanceStats.workedHours'
    | 'performanceStoryHeadline'
    | 'personal'
    | 'personalDetails'
    | 'phoneNumber'
    | 'postalCode'
    | 'previous'
    | 'price'
    | 'priceMustBePositive'
    | 'primaryInformation'
    | 'privacyPolicy'
    | 'privilegeBasedAccessMultiple'
    | 'privilegeBasedAccessSingle'
    | 'privilegeBasedAccessTitle'
    | 'PROFESSIONAL_ACCESS'
    | 'professional_things'
    | 'profile'
    | 'profilePicturePickerScreenDescription'
    | 'profilePicturePickerScreenHeadline'
    | 'profileUpdateSuccess'
    | 'program'
    | 'promoLink'
    | 'promoLinkDescription'
    | 'promoLinkUse'
    | 'promotesTerrorism'
    | 'publicProfileOfBusiness'
    | 'ratingNumber'
    | 'readMore'
    | 'recentBusinesses'
    | 'recoverPassword'
    | 'reminders'
    | 'reportBusiness'
    | 'reportContent'
    | 'reportContentError'
    | 'reportContentSuccess'
    | 'requestCalendarPermissionsAsyncNotGranted'
    | 'requestHasBeenSent'
    | 'requestRemindersPermissionsAsyncNotGranted'
    | 'requiredField'
    | 'reschedule'
    | 'resend'
    | 'resendIn'
    | 'reservationDone'
    | 'resetPasswordSuccessMessage'
    | 'reviews'
    | 'reviewSent'
    | 'reviewsFromOurUsers'
    | 'reviewsNumber'
    | 'reviewsYouCanTrust'
    | 'reviewUsOnAppStore'
    | 'reviewUsOnPlayStore'
    | 'reviewYourExperience'
    | 'ro-RO'
    | 'salesReports'
    | 'saveChanges'
    | 'saveComment'
    | 'saveLocation'
    | 'saveNotes'
    | 'schedule'
    | 'searchClients'
    | 'searchServiceOrLocation'
    | 'seeAllReviews'
    | 'selectEventTypeScreen.appointment'
    | 'selectEventTypeScreen.appointmentDescription'
    | 'selectEventTypeScreen.break'
    | 'selectEventTypeScreen.breakDescription'
    | 'selectEventTypeScreen.description'
    | 'selectEventTypeScreen.headline'
    | 'sendFeedback'
    | 'sendInvitation'
    | 'sendMail'
    | 'sendReview'
    | 'sendSMS'
    | 'sendSmsMessages'
    | 'sendSmsMessagesCaption'
    | 'service'
    | 'serviceDeleted'
    | 'serviceDescription'
    | 'serviceDurationAndPrice'
    | 'serviceHiddenForClients'
    | 'serviceIsNotSelected'
    | 'serviceName'
    | 'services'
    | 'settings'
    | 'settingsProfileCategoriesScreenCaption'
    | 'settingsProfileCategoriesScreenHeadline'
    | 'settingsProfileWorkingProgramBusinessDescription'
    | 'settingsProfileWorkingProgramStaffMemberDescription'
    | 'settingsUpdated'
    | 'sexualContent'
    | 'showAllAppointments'
    | 'showAllCategories'
    | 'showLess'
    | 'showMore'
    | 'signInScreenDescription'
    | 'signInScreenHeadline'
    | 'signout'
    | 'signUpBusinessCategoriesDescription'
    | 'signUpBusinessCategoriesHeadline'
    | 'signUpBusinessDetailsDescription'
    | 'signUpBusinessDetailsHeadline'
    | 'signUpBusinessLocationHeadline'
    | 'signUpBusinessProgramDescription'
    | 'signUpBusinessProgramHeadline'
    | 'signUpScreenDescription'
    | 'signUpScreenHeadline'
    | 'skip'
    | 'smsBalance'
    | 'smsBalanceDescription'
    | 'smsBought'
    | 'smsNotifications'
    | 'socialMedia'
    | 'somethingWentWrong'
    | 'somethingWentWrongError'
    | 'spamOrMisleading'
    | 'staffMember'
    | 'staffMemberInvitationSent'
    | 'staffMemberIsNotSelected'
    | 'staffMembers'
    | 'staffMembersOrderCouldNotBeSaved'
    | 'staffMembersTop'
    | 'staffName'
    | 'STANDARD_ACCESS'
    | 'standard_things'
    | 'startNow'
    | 'statistics'
    | 'status'
    | 'subscribedSuccessfullyToWaitingList'
    | 'subscription'
    | 'subscriptionPlans.custom.description'
    | 'subscriptionPlans.custom.name'
    | 'subscriptionPlans.custom.price'
    | 'subscriptionPlans.free.description'
    | 'subscriptionPlans.free.name'
    | 'subscriptionPlans.professional.description'
    | 'subscriptionPlans.professional.name'
    | 'subscriptionPlans.standard.description'
    | 'subscriptionPlans.standard.name'
    | 'suggestions'
    | 'support'
    | 'sureBlock'
    | 'switchToBusiness'
    | 'switchToClient'
    | 'taxId'
    | 'teamManagement'
    | 'technicalSupport'
    | 'termsAndConditions'
    | 'textMessages'
    | 'thanksBookr'
    | 'thanksForFeedback'
    | 'to'
    | 'totalBookings'
    | 'totalCancelled'
    | 'totalFinished'
    | 'totalNoShows'
    | 'totalResults'
    | 'totalRevenueFromClient'
    | 'totalVisits'
    | 'trialPeriod'
    | 'typeBusinessLocation'
    | 'typeToSearch'
    | 'unblockClient'
    | 'unknown'
    | 'unlimitedAppointments'
    | 'unlimitedStaffMembers'
    | 'unpaidBillMessage'
    | 'unpaidBillTitle'
    | 'upgradePlan'
    | 'uploadImage'
    | 'userNotFoundError'
    | 'verificationEmailSent'
    | 'verificationLinkSendOn'
    | 'viewAppointment'
    | 'viewDemo'
    | 'viewLocation'
    | 'violentOrRepulsiveContent'
    | 'virtual3dTourCaption'
    | 'virtual3dTourHeadline'
    | 'virtualTour'
    | 'waitingClients'
    | 'waitingList'
    | 'waitingListClients'
    | 'websiteUrl'
    | 'weCouldNotFindAnyBusinessForThisAppointment'
    | 'weCouldNotFindAnyEmailForThisAppointment'
    | 'weCouldNotFindAnyPhoneNumberForThisAppointment'
    | 'weCouldNotFindAnyServices'
    | 'weCouldNotFindAnyServicesForTheSelectedStaffMember'
    | 'weCouldNotFindYourBusiness'
    | 'welcomeToBookr'
    | 'welcomeToBookrStoryHeadline'
    | 'weWillNotBeAbleToRecoverYourAccount'
    | 'with'
    | 'withPhoneNumber'
    | 'workingHours'
    | 'workingProgramCouldNotBeSaved'
    | 'workingSchedule'
    | 'xOutOfY'
    | 'yesCancel'
    | 'youCanAskTheStaffMemberToCreateAService'
    | 'yourAccountIsNotVerified'
    | 'yourContacts'
    | 'yourDevicesWillNotBeAbleToAccessYourAccount'
    | 'yourExperienceWith'
    | 'yourFavourites'
    | 'yourInformation'
    | 'yourPersonalWebsite'
    | 'yourServices';
