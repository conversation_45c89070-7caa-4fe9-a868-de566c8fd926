import { objectToQuery } from './objectToQuery';

describe('lib/http#objectToQuery', () => {
    it('should convert object to query correctly', () => {
        expect(
            objectToQuery({
                foo: 'bar',
            }),
        ).toBe('foo=bar');

        expect(
            objectToQuery({
                foo: 'bar',
                bar: 'baz',
            }),
        ).toBe('foo=bar&bar=baz');
    });
});
