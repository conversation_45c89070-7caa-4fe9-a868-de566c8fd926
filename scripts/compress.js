/* eslint-disable @typescript-eslint/no-var-requires */
const glob = require('glob');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const _ = require('lodash');

const files = glob.sync(path.join(__dirname, '..', 'src/assets/raw', '**', '*.{jpg,png,json}'));

async function main(files) {
    let chunk;

    while ((chunk = files.pop())) {
        await Promise.all(
            chunk.map(async (file) => {
                const ext = path.extname(file);
                const output = file.replace('/raw/', '/').replace(/\s/g, '_');
                fs.mkdirSync(path.dirname(output), { recursive: true });
                console.log(
                    '[INFO] process',
                    path.relative(process.cwd(), file),
                    'output',
                    path.relative(process.cwd(), output),
                );

                if (ext === '.json') {
                    await new Promise((resolve, reject) => {
                        fs.readFile(file, (err, content) => {
                            if (err) {
                                return reject(err);
                            }

                            const json = JSON.parse(content.toString());
                            fs.writeFile(output, JSON.stringify(json), (err) => {
                                if (err) {
                                    return reject(err);
                                }
                                resolve();
                            });
                        });
                    });
                    return;
                }

                let img = sharp(file);
                if (ext === '.png') {
                    img = img.png({
                        quality: 90,
                    });
                } else {
                    img = img.jpeg({
                        mozjpeg: true,
                        quality: 80,
                        progressive: true,
                    });
                }

                await img.toFile(output);
            }),
        );
    }
}

main(_.chunk(files, 5)).then(() => {
    console.log('done', files.length, 'files');
});
