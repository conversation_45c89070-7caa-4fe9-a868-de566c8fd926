module.exports = function (file, api, options) {
    const j = api.jscodeshift;
    const source = j(file.source);
    // const translation = source.findVariableDeclarators('translation').nodes()[0];
    // whatever other code...

    return source
        .findVariableDeclarators('translation')
        .replaceWith((path) => {
            path.node.init.properties.sort((a, b) => {
                const prev = a.key?.value || a.key?.name || '';
                const next = b.key?.value || b.key?.name || '';

                return prev.localeCompare(next);
            });

            return path.node;
        })
        .toSource();
};

module.exports.parser = 'ts';
