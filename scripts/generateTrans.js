require('ts-node/register');

const { translation } = require('../src/lib/translations/resources/en');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');

function objectToDot(object, path = '') {
    let collection = {};

    Object.entries(object).forEach(([key, value]) => {
        if (typeof value === 'object') {
            collection = {
                ...objectToDot(value, `${path}.${key}`),
                ...collection,
            };
        } else {
            collection[`${path}.${key}`.replace(/^\./, '')] = value;
        }
    });

    return collection;
}

const map = Object.keys(objectToDot(translation)).sort((a, b) => a.localeCompare(b));

const target = path.join(__dirname, '../src/lib/translations/keys.ts');
fs.writeFileSync(target, `export type TranslationKeys = '${map.join("' | '")}';`);
exec(`yarn prettier -w ${target}`);

if (!process.argv.includes('--skip-add-git')) {
    try {
        exec(`git add ${target}`);
    } catch (e) {
        console.log(target, 'could not be added to git');
    }
}
