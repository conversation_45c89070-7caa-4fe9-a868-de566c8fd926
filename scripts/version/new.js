#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const yargs = require('yargs');
const { exec } = require('child_process');

function jsonFromFile(file) {
    return JSON.parse(fs.readFileSync(path.resolve(__dirname, '../../', file)).toString());
}

function updateAppVersion() {
    const pkg = jsonFromFile('package.json');
    const app = jsonFromFile('app.json');

    app.expo.version = pkg.version;
    const contents = JSON.stringify(app, null, 4);

    fs.writeFileSync(path.resolve(__dirname, '../../app.json'), contents);
}

async function main() {
    const { _ } = yargs.argv;
    await new Promise((resolve, reject) => {
        exec(`yarn version ${_.join(' ')}`, (err, stdout, stderr) => {
            if (err) {
                reject(err);
                return;
            }

            process.stdout.write(stdout);
            process.stderr.write(stderr);

            resolve();
        });
    });

    updateAppVersion();
}

main();
