import { ReactElement } from 'react';
import { View, ViewProps } from 'react-native';
import { makeStyles } from '~/components/ui/makeStyles';

export interface {{ componentNameProps }} extends ViewProps {
    // Customer properties
}

export function {{ componentName }} ({ ...rest }: {{ componentNameProps }}): ReactElement {
    const styles = useStyles();

    return (
        <View style={ styles.root } {...rest}/>
    );
}

const useStyles = makeStyles(({theme}) => ({
    root: {
        // Styles
    },
}));

