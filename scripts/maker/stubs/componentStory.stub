import { Text } from 'react-native';
import { storiesOf } from '@storybook/react-native';
import { Typography } from '~/components/ui/Typography';
import { Screen } from '~/components/ui/Screen';
import { ThemeProvider } from '~/components/ui/ThemeProvider';

function DefaultExample() {
    return (
        <Text>{{componentName}} examples</Text>
    );
}

storiesOf('{{componentName}}', module)
    .addDecorator((getStory) => (
        <ThemeProvider>
            <Screen>
                <Typography variant={'title3'} fontWeight={700} mt={4}>
                    {{_.startCase(componentName)}}
                </Typography>
                <Typography variant={'subhead'} color={'textSecondary'} fontWeight={500} mb={2}>
                    {{_.startCase(componentName)}} description.
                </Typography>
                {getStory()}
            </Screen>
        </ThemeProvider>
    ))
    .add('Default', DefaultExample);
