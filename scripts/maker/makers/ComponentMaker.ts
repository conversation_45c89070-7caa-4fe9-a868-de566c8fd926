import { camelCase, upperFirst } from 'lodash';
import * as path from 'path';
import { Arguments, Argv } from 'yargs';
import { Maker } from './Maker';

interface ComponentMakerArgs {
    name: string;
    dir?: string;
    single?: boolean;
    styled?: boolean;
    force?: boolean;
    story?: boolean;
}

// eslint-disable-next-line @typescript-eslint/ban-types
export class ComponentMaker extends Maker<ComponentMakerArgs> {
    public command = 'component <name>';
    public describe = 'Create a component';
    public aliases = ['c'];

    public builder = (argv: Argv) =>
        this.forcedOption(argv)
            .option('single', {
                type: 'boolean',
                description: 'Create a single component',
                default: false,
            })
            .option('styled', {
                alias: 's',
                type: 'boolean',
                description: 'Create a styled component',
                default: false,
            })
            .option('story', {
                type: 'boolean',
                description: 'Create a story component',
                default: false,
            })
            .positional('name', {
                type: 'string',
                description: 'The name of the component',
                required: true,
            });

    public async handle(args: Arguments<ComponentMakerArgs>) {
        this.args.componentName = upperFirst(camelCase(path.basename(this.args.name)));
        this.args.componentNameProps = this.args.componentName + 'Props';

        if (!args.single || !args.story) {
            await this.createExportableComponent();
        } else {
            await this.createSingleComponent();
        }

        await this.prettier();
    }

    private async createSingleComponent() {
        await this.make('src/components/{{name}}.tsx', this.generateComponent());
    }

    private async createExportableComponent() {
        const storyPath = 'src/components/{{name}}/{{componentName}}.stories.tsx';
        const srcImport = await this.getSrcImport(storyPath);

        await Promise.all([
            this.make('src/components/{{name}}/index.ts', this.stub('componentIndex')),
            this.make('src/components/{{name}}/{{componentName}}.tsx', this.generateComponent()),

            this.args.story && this.make(storyPath, this.stub('componentStory')),
            this.args.story && this.prepend('storybook/stories.ts', `import '${srcImport}';\n`),
        ]);
    }

    private generateComponent(): Promise<string> {
        if (this.args.styled) {
            return this.stub('componentStyled');
        }

        return this.stub('component');
    }
}
