/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/ban-ts-comment */
import { blue, green, red, yellow } from 'chalk';
import { exec } from 'child_process';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as path from 'path';
import { promisify } from 'util';
import { Arguments, Argv } from 'yargs';

export abstract class Maker<T> {
    protected readFile = promisify(fs.readFile);
    protected writeFile = promisify(fs.writeFile);
    protected appendFile = promisify(fs.appendFile);
    protected args!: Arguments<T>;
    private stubs: Record<string, _.TemplateExecutor> = {};
    private prettierCollection: string[] = [];

    constructor() {
        // @ts-ignore
        if (typeof this.builder === 'function') {
            // @ts-ignore
            this.builder = this.builder.bind(this);
        }

        // @ts-ignore
        this.handler = this.handler.bind(this);
    }

    public abstract handle(args: Arguments<T>): Promise<void>;

    public async handler(args: Arguments<T>) {
        this.args = args;
        await this.handle(args);
    }

    protected info(...args: any[]) {
        console.info(blue('[INFO]   ', ...args));
    }

    protected warn(...args: any[]) {
        console.warn(yellow('[WARN]   ', ...args));
    }

    protected error(...args: any[]) {
        console.error(red('[ERROR]  ', ...args));
    }

    protected success(...args: any[]) {
        console.log(green('[SUCCESS]', ...args));
    }

    protected basePath(...paths: string[]): Promise<string> {
        if (!fs.existsSync(paths[0])) {
            paths.unshift(__dirname, '../../..');
        }

        return this.stub(path.resolve(...paths));
    }

    protected async make(
        pathname: string,
        contents: string | Promise<string>,
        props?: Arguments<T & { force?: boolean }>,
    ) {
        const data = await contents;
        const destination = await this.stub(this.basePath(pathname), props);

        if (!fs.existsSync(destination) || this.args?.force) {
            this.info('Creating file', path.relative(process.cwd(), destination));
            this.prettierCollection.push(destination);

            return new Promise((resolve, reject) => {
                fs.mkdirSync(path.dirname(destination), { recursive: true });
                fs.writeFile(destination, data, (error) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(destination);
                    }
                });
            });
        } else {
            this.warn('File already exists, skipping file', path.relative(process.cwd(), destination));
        }

        return destination;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    protected async stub(name: string | Promise<string>, props?: Record<string, any>): Promise<string> {
        const namePath = await name;
        if (!this.stubs[namePath]) {
            const stubFile = path.resolve(__dirname, `../../../scripts/maker/stubs/${namePath}.stub`);
            const contents = fs.existsSync(stubFile) ? await this.readFile(stubFile, 'utf8') : namePath;
            this.stubs[namePath] = _.template(contents, {
                interpolate: /{{([\s\S]+?)}}/g,
                imports: {
                    _,
                },
            });
        }

        return this.stubs[namePath]({
            ...this.args,
            ...props,
            _,
        });
    }

    protected async prettier() {
        this.prettierCollection = this.prettierCollection.filter((f) => fs.existsSync(f));

        if (this.prettierCollection.length === 0) {
            return;
        }

        this.info('Prettify', this.prettierCollection.length, 'files...');

        const collection = await Promise.all(this.prettierCollection.map((p) => this.basePath(p)));

        this.prettierCollection = [];

        return new Promise((resolve, reject) => {
            const proc = exec(`yarn prettier -w ${collection.join(' ')}`, (error) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(true);
                }
            });

            proc.stdout?.pipe(process.stdout);
            proc.stderr?.pipe(process.stderr);
        });
    }

    protected forcedOption<T>(argv: Argv<T>): Argv<T> {
        return argv.option('force', {
            alias: 'f',
            type: 'boolean',
            description: 'Force action',
            default: false,
        });
    }

    protected async append(pathname: string, contents: string | Promise<string>, props?: Arguments<T>) {
        const data = await contents;
        const [destination, content] = await Promise.all([this.stub(pathname, props), this.stub(data, props)]);

        this.prettierCollection.push(destination);

        await this.appendFile(destination, content);
    }

    protected async prepend(pathname: string, contents: string | Promise<string>, props?: Arguments<T>) {
        const data = await contents;
        const [destination, content] = await Promise.all([this.stub(pathname, props), this.stub(data, props)]);
        const fileContents = await this.readFile(destination, 'utf8');
        this.prettierCollection.push(destination);

        await this.writeFile(destination, content + fileContents);
    }

    protected async getSrcImport(pathname: string) {
        const [srcPath, absolutPath] = await Promise.all([this.basePath('src'), this.basePath(pathname)]);

        return absolutPath.replace(srcPath, '~');
    }
}
