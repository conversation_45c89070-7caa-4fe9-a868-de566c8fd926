let bookrConfig = require('@bookr-technologies/env/lib/config.defaults.cjs');

if (process.env.EXPO_PUBLIC_APP_ENV === 'staging') {
    bookrConfig = require('@bookr-technologies/env/lib/config.staging.cjs');
} else if (process.env.EXPO_PUBLIC_APP_ENV === 'production') {
    bookrConfig = require('@bookr-technologies/env/lib/config.production.cjs');
} else if (process.env.EXPO_PUBLIC_APP_ENV === 'local') {
    bookrConfig.apiUrl = 'http://192.168.1.135:8080';
}

const getAppDetails = (config) => {
    return {
        appName: process.env.EXPO_PUBLIC_APP_NAME ? process.env.EXPO_PUBLIC_APP_NAME : config.name || 'Bookr',
        bundleIdentifier: process.env.EXPO_PUBLIC_APP_BUNDLE_IDENTIFIER
            ? process.env.EXPO_PUBLIC_APP_BUNDLE_IDENTIFIER
            : config.ios.bundleIdentifier,
    };
};

/**
 * @param {import('@expo/config').ConfigContext} config
 * @returns {import('@expo/config').ExpoConfig}
 */
module.exports = ({ config }) => {
    const appDetails = getAppDetails(config);

    return {
        slug: config.slug || 'bookr',
        ...config,
        name: appDetails.appName,
        ios: {
            ...config.ios,
            bundleIdentifier: appDetails.bundleIdentifier,
        },
        updates: {
            ...config.updates,
            fallbackToCacheTimeout: 60000,
            url: 'https://u.expo.dev/323ab156-2736-427a-b504-95ad50acb380',
        },
        runtimeVersion: {
            policy: 'sdkVersion',
        },

        extra: {
            ...config.extra,
            config: bookrConfig,
        },
    };
};
