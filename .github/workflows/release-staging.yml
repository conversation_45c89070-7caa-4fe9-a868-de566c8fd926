name: 'Release Staging'

on:
  workflow_dispatch:
  # replace it with staging after the refactor branch is merged into main
  push:
    branches:
      - main
    paths:
      - assets/**
      - src/**
      - App.tsx
      - app.json
      - babel.config.js
      - google-services.json
      - GoogleService-info.plist
      - metro.config.js
      - package.json
      - .github/workflows/release-staging.yml

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  release-staging:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Restore yarn cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            .yarn/install-state.gz
            yarn.lock
            node_modules
          key: ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-v2-

      - name: Setup Expo
        uses: expo/expo-github-action@v7
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Dependency install
        env:
          YARN_ENABLE_IMMUTABLE_INSTALLS: false
        run: yarn install

      - name: Release Staging
        run: yarn release:staging

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v4
        env:
          SLACK_CHANNEL: ci-cd
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/bookr-technologies.png?size=48
          SLACK_MESSAGE: |
            Hey, A new release for mobile app has been published to the staging channel.
            https://expo.dev/@andreicovaciu/bookr?release-channel=staging
          SLACK_TITLE: New mobile release [STAGING]
          SLACK_USERNAME: bookr-bot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
