name: 'Generators'

# Remove push after the refactor branch is merged into main
on:
  pull_request:
    paths:
      - "src/lib/translations/resources/**.ts"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  translations:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            .yarn/install-state.gz
            yarn.lock
            node_modules
          key: ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-v2-

      - name: Dependency install
        env:
          YARN_ENABLE_IMMUTABLE_INSTALLS: false
        run: yarn install

      - name: Generate Translations
        run: yarn i18n:keys --skip-add-git

      - name: Sort Translations
        run: yarn i18n:sort yarn

      - name: Prettier
        run: yarn prettier -w ./src/lib/translations

      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: Generator changes
