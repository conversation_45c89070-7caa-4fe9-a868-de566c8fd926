name: 'Release Production'

on:
  workflow_dispatch:
    inputs:
      password:
        required: true
        description: Release password
        type: string

      dry:
        required: false
        description: Dry run
        type: boolean
        default: false

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  release-production:
    runs-on: ubuntu-latest
    steps:
      - name: Allow Access
        run: |
          if [[ "${{github.event.inputs.password}}" != "${{secrets.RELEASE_PASSWORD}}" ]]; then
            echo "Incorrect password. You are not allowed to release the app."
            exit 1
          fi

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Restore yarn cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            .yarn/install-state.gz
            yarn.lock
            node_modules
          key: ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-v2-

      - name: Setup Expo
        uses: expo/expo-github-action@v7
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Dependency install
        env:
          YARN_ENABLE_IMMUTABLE_INSTALLS: false
        run: yarn install

      - name: Release Production
        run: yarn release:production
        if: ${{ github.event.inputs.dry == 'false' }}

      - name: Version info
        id: version-info
        run: |
          version=$(cat package.json | jq -r .version)
          echo ::set-output name=version::${version}

      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          name: Release v${{ steps.version-info.outputs.version }}
          tag_name: v${{ steps.version-info.outputs.version }}
          draft: false
          prerelease: false
          generate_release_notes: true

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v4
        env:
          SLACK_CHANNEL: ci-cd
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/bookr-technologies.png?size=48
          SLACK_MESSAGE: |
            Hey, A new release for mobile app has been published to the production channel.
            Version v${{ steps.version-info.outputs.version }} is now available at https://expo.dev/@andreicovaciu/bookr?release-channel=production
          SLACK_TITLE: |
            ${{ github.event.inputs.dry == 'true' && '[DRY-RUN]' }} New mobile release [PRODUCTION]
          SLACK_USERNAME: bookr-bot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

