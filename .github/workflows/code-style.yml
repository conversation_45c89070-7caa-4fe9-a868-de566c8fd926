name: 'Code style'

# Remove push after the refactor branch is merged into main
on:
  pull_request:
    paths:
      - "**.ts"
      - "**.tsx"

concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
  eslint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Restore yarn cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            .yarn/install-state.gz
            yarn.lock
            node_modules
          key: ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-v2-

      - name: Restore eslint cache
        uses: actions/cache@v4
        with:
          path: |
            .eslintrc.json
          key: ${{ runner.os }}-eslint-v1-${{ hashFiles('.eslintrc.json') }}
          restore-keys: |
            ${{ runner.os }}-eslint-v1

      - name: Dependency install
        env:
          YARN_ENABLE_IMMUTABLE_INSTALLS: false
        run: yarn install

      - name: Linting
        run: yarn lint

  prettier:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            .yarn/install-state.gz
            yarn.lock
            node_modules
          key: ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}

      - name: Dependency install
        run: yarn install

      - name: Pretty code
        run: yarn format

      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: Prettier changes

  tsc:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            .yarn/cache
            .yarn/install-state.gz
            yarn.lock
            node_modules
          key: ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-v2-${{ hashFiles('yarn.lock') }}

      - name: Dependency install
        run: yarn install

      - name: TSC Check
        run: yarn tsc:check
