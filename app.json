{"expo": {"name": "BOOKR", "slug": "bookr", "owner": "andreicovaciu", "version": "6.0.0", "orientation": "portrait", "icon": "./assets/logo.png", "scheme": "bookr", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 60000}, "assetBundlePatterns": ["**/*"], "ios": {"googleServicesFile": "./GoogleService-info.plist", "appStoreUrl": "https://apps.apple.com/us/app/bookr-online-booking-app/id1547131136", "supportsTablet": true, "bundleIdentifier": "com.bookr.app", "infoPlist": {"NSPhotoLibraryUsageDescription": "BOOKR wants to access your photos to change your profile picture.", "NSLocationWhenInUseUsageDescription": "BOOKR wants to get your location for an easier business profile setup and a better search experience.", "NSContactsUsageDescription": "BOOKR wants to access your contacts for an easier client base setup.", "NSUserTrackingUsageDescription": "BOOKR wants to collect your data to deliver personalized content and ads for you.", "NSRemindersUsageDescription": "BOOKR wants to access your calendar to setup reminders for your appointments.", "NSCalendarsUsageDescription": "BOOKR wants to access your calendar to setup reminders for your appointments.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}]}, "privacyManifests": {"NSPrivacyAccessedAPITypes": [{"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryUserDefaults", "NSPrivacyAccessedAPITypeReasons": ["CA92.1"]}]}, "config": {"googleMapsApiKey": "AIzaSyBcu7RYxidg5ZlDvsCsLUBvK6Ka0Fk_Cbo"}}, "android": {"package": "com.bookr.app", "googleServicesFile": "./google-services.json", "versionCode": 16, "playStoreUrl": "https://play.google.com/store/apps/details?id=com.bookr.app", "config": {"googleMaps": {"apiKey": "AIzaSyAAxC4kVWMng15Rs-3fqxyVX8AQbUIpk4A"}}, "permissions": ["READ_CALENDAR", "WRITE_CALENDAR", "android.permission.INTERNET"]}, "web": {"favicon": "./assets/images/favicon.png", "config": {"firebase": {"apiKey": "AIzaSyABnTzuA_UCrj3poKssKiX9oGF7sJMFEiI", "authDomain": "bookr-api.firebaseapp.com", "databaseURL": "https://bookr-api.firebaseio.com", "projectId": "bookr-api", "storageBucket": "bookr-api.appspot.com", "messagingSenderId": "393065803535", "appId": "1:393065803535:web:3e8203d5c227a79a03187a", "measurementId": "G-0K6BPRYR67"}}}, "plugins": ["expo-localization", ["@sentry/react-native/expo", {"organization": "bookr-technologies", "project": "bookr-mobile"}], ["expo-tracking-transparency", {"userTrackingPermission": "BOOKR wants to collect app-related data that can be used provide a better user experience."}], ["react-native-fbsdk-next", {"userTrackingPermission": "BOOKR wants to collect your data to deliver personalized content and ads for you.", "appID": "411284243301785", "clientToken": "c7cfeb8fbcdf45a9ccf0bb4705b200fc", "displayName": "BOOKR: Online Booking App", "advertiserIDCollectionEnabled": true, "autoLogAppEventsEnabled": true, "isAutoInitEnabled": false, "iosUserTrackingPermission": "BOOKR wants to collect your data to deliver personalized content and ads for you."}], "@react-native-firebase/app", "@react-native-firebase/perf", "@react-native-firebase/crashlytics", ["expo-build-properties", {"ios": {"useFrameworks": "static", "privacyManifestAggregationEnabled": true, "deploymentTarget": "14.0"}, "android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}]], "extra": {"eas": {"projectId": "323ab156-2736-427a-b504-95ad50acb380"}}}}