{"extends": "expo/tsconfig.base", "compilerOptions": {"lib": ["dom", "esnext"], "strict": true, "skipDefaultLibCheck": true, "skipLibCheck": true, "rootDirs": ["src", "stories", "scripts"], "baseUrl": "src", "paths": {"~/*": ["./*"]}}, "files": ["./src/lib/translations/translations.d.ts", "./src/RoutesParams.d.ts"], "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "**/*.test.tsx", "**/*.spec.ts"]}