{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:jsx-a11y/recommended", "google", "prettier"], "plugins": ["react", "react-hooks", "jsx-a11y", "typescript-sort-keys", "@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "settings": {"react": {"version": "detect"}}, "rules": {"react-hooks/exhaustive-deps": "error", "react/react-in-jsx-scope": "off", "eqeqeq": "error", "require-jsdoc": "off", "valid-jsdoc": "off", "new-cap": "off", "no-unused-vars": "off", "typescript-sort-keys/string-enum": "error", "@typescript-eslint/array-type": ["error", {"default": "array-simple"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/explicit-member-accessibility": "warn", "@typescript-eslint/member-ordering": ["error", {"default": {"memberTypes": ["signature", "field", "constructor", "public-static-method", "protected-static-method", "private-static-method", "public-method", "protected-method", "private-method"], "order": "alphabetically"}}], "camelcase": ["error", {"properties": "always"}], "@typescript-eslint/no-unused-vars": ["error", {"args": "after-used", "ignoreRestSiblings": true}]}, "overrides": [{"files": ["*.js"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-var-requires": "off"}}]}