<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>393065803535-m7d3chm39mellnepp70406fbghl21r17.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.393065803535-m7d3chm39mellnepp70406fbghl21r17</string>
	<key>API_KEY</key>
	<string>AIzaSyBcu7RYxidg5ZlDvsCsLUBvK6Ka0Fk_Cbo</string>
	<key>GCM_SENDER_ID</key>
	<string>393065803535</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.bookr.app</string>
	<key>PROJECT_ID</key>
	<string>bookr-api</string>
	<key>STORAGE_BUCKET</key>
	<string>bookr-api.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:393065803535:ios:96c115e7c8a442bf03187a</string>
	<key>DATABASE_URL</key>
	<string>https://bookr-api.firebaseio.com</string>
</dict>
</plist>
