## Mobile application - Bookr

[![Release Staging](https://github.com/bookr-technologies/mobileapp/actions/workflows/release-staging.yml/badge.svg)](https://github.com/bookr-technologies/mobileapp/actions/workflows/release-staging.yml)

Bookr mobile is a ReactNative application built with [Expo](https://expo.dev/)

### Pre-requisites

1. If you are developing for iOS, you will need to have XCode installed v15 or higher.
2. Java 11 for building on Android.

### Start project

1. Install dependencies

```
nvm use
yarn
```

2. To start the Expo server, run:

```
yarn start
```

3. To run the app on a simulator, run: `yarn ios:staging` or `yarn android:staging`
4. To run the app on the device, run: `yarn ios --device` or `yarn ios:production --device`

### Build

There are two was of building the app, either with Expo servers or locally.

-   Build with Expo servers: `eas build -p ios --profile <PROFILE>`
-   Build locally: `eas build -p ios --profile <PROFILE> --local`

Profile can be found in `eas.json` file.

### OTA Updates

We can update our app without going through the App Store or Play Store. This is done by using EAS `update` command.

We can run an update when we change JS/TS files and/or assets.

**Important**: check [this](https://docs.expo.dev/eas-update/how-eas-update-works/).

To update the app using an OTA update, run `yarn release:<PROFILE>`. E.g. `yarn release:staging`.

Check the `package.json` scripts for more release commands.

### Versioning

1. Run the following command: `yarn version <patch | minor | major>`
2. Copy the `version` from `package.json` and paste it in `app.json`.
3. Update the 'android.versionCode' in `app.json` by adding 1 to the previous value. E.g. if the previous value was 1,
   the new value should be 2.
4. Commit the changes and push to the remote branch.

### Release to App Store a new version (currently manual operations)

1. Run the steps in the [Versioning](#versioning) section.
2. Trigger a production build (locally works faster) by running: `eas build -p ios --profile production --local`. This
   will generate an `.ipa` file on the root of the project.
3. Upload the `.ipa` file to App Store Connect using Transporter.app.
4. Go to App Store Connect and submit the app for review and fill in the missing compliance questions.

### Release to Play Store a new version (currently manual operations)

1. Run the steps in the [Versioning](#versioning) section.
2. Trigger a production build (locally works faster) by running: `eas build -p android --profile production --local`.
   This will generate an `.apk` file on the root of the project.
3. Upload the `.apk` file to Play Store using the [Play Console](https://play.google.com/apps/publish/).
4. Go to Play Console and hit the "Start rollout to production" button when it becomes available.

### Want to install an .ipa file on your device?

If you have an .ipa file (usually generated by `eas build` locally) and want to install it on your device, you can do it
by following these steps:

```bash
brew install libimobiledevice
brew install ideviceinstaller
ideviceinstaller -i <path-to-ipa-file>
```

### Issues

#### yarn ios

In case you encounter any issues while doing `yarn ios:<env>`, make sure you do a `yarn expo prebuild --clean`

In case you encounter errors related to FBSDKCoreKit framework, have a look at
[this](https://stackoverflow.com/questions/73526777/library-not-loaded-rpath-fbsdkcorekit-framework-fbsdkcorekit-error)
