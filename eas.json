{"cli": {"version": ">= 7.8.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug"}, "env": {"EXPO_PUBLIC_APP_ENV": "production", "EXPO_PUBLIC_APP_NAME": "<PERSON><PERSON> (<PERSON>)", "EXPO_PUBLIC_APP_BUNDLE_IDENTIFIER": "com.bookr.app-dev"}}, "simulator": {"distribution": "internal", "channel": "staging", "ios": {"buildConfiguration": "Debug", "simulator": "true"}, "env": {"EXPO_PUBLIC_APP_ENV": "staging"}}, "staging": {"distribution": "internal", "channel": "staging", "env": {"EXPO_PUBLIC_APP_ENV": "staging", "EXPO_PUBLIC_APP_NAME": "Bookr (Stg)", "EXPO_PUBLIC_APP_BUNDLE_IDENTIFIER": "com.bookr.app-stg"}}, "production-rc": {"distribution": "internal", "channel": "production-rc", "env": {"EXPO_PUBLIC_APP_ENV": "production"}}, "production": {"distribution": "store", "channel": "production", "env": {"EXPO_PUBLIC_APP_ENV": "production"}}}, "submit": {"production": {}}}