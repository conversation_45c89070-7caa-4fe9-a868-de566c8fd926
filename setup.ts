import Constants from 'expo-constants';
import 'intl';
import 'intl/locale-data/jsonp/en';
import 'intl/locale-data/jsonp/ro';
import 'react-native-reanimated';
import { ExternalConfigurationAdapter, setAdapter } from '@bookr-technologies/env';

/**
 * For more information on why the `||` is needed, see:
 * https://docs.expo.dev/eas-update/migrate-to-eas-update/#additional-possible-migration-steps
 */
setAdapter(new ExternalConfigurationAdapter(Constants.expoConfig?.extra?.config));
